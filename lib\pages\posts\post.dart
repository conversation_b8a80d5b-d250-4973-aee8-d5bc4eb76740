import 'package:estockcafe/utils/logging.dart';
import 'package:estockcafe/widgets/post/list_view.dart';
import 'package:estockcafe/widgets/post/post_content.dart';
import 'package:estockcafe/widgets/post/future_content_widget.dart';
import 'package:flutter/material.dart';
import 'package:estockcafe/services/post_service.dart';
import 'package:estockcafe/models/post.dart';
import 'package:estockcafe/models/comment.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../widgets/bottom_navbar.dart' as navbar;

class PostPage extends StatefulWidget {
  final String articleId;

  const PostPage({super.key, required this.articleId});

  @override
  _PostPageState createState() => _PostPageState();
}

class _PostPageState extends State<PostPage> {
  bool isLoading = true;

  final _logger = Logging.getLogger('PostPage');

  final PostService _postService = PostService();
  final TextEditingController _commentController = TextEditingController();

  late Future<PostItem> _postFuture;
  late Future<List<Comment>> _commentsFuture;
  late Future<List<PostItem>> _relatedPostsFuture;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    setState(() {
      isLoading = true;
      _postFuture = _loadPost();
      _commentsFuture = _loadComment();
      _relatedPostsFuture = _loadRelatedPosts();
    });
  }

  Future<void> _refreshData() async {
    _loadData();
    await Future.wait([_postFuture, _commentsFuture, _relatedPostsFuture]);
    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: isLoading ? const Text('加载中...') : null,
      ),
      body: Container(
        color: Colors.grey[200],
        child: Column(
          children: [
            Expanded(
              child: RefreshIndicator(
                onRefresh: _refreshData,
                child: ListView(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                      ),
                      child: _buildPostContent(),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                      ),
                      child: _buildRelatedPosts(),
                    ),
                    // 评论部分暂时注释掉
                    /* const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                      ),
                      child: _buildComments(),
                    ), */
                  ],
                ),
              ),
            ),
            // 评论输入部分暂时注释掉
            // _buildCommentInput(),
          ],
        ),
      ),
      bottomNavigationBar: const navbar.BottomNavBar(currentIndex: -1),
    );
  }

  Future<PostItem> _loadPost() async {
    try {
      return await _postService.fetchPost(widget.articleId);
    } catch (e) {
      _logger.severe('加载文章失败: $e');
      _showErrorSnackBar('加载文章失败: $e');
      rethrow;
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<List<Comment>> _loadComment() async {
    try {
      return await _postService.fetchComments(widget.articleId);
    } catch (e) {
      _logger.severe('加载评论失败: $e');
      _showErrorSnackBar('加载评论失败: $e');
      return [];
    }
  }

  Future<List<PostItem>> _loadRelatedPosts() async {
    try {
      return await _postService.fetchRelatedPosts(widget.articleId);
    } catch (e) {
      _logger.severe('加载相关文章失败: $e');
      _showErrorSnackBar('加载相关文章失败: $e');
      return [];
    }
  }

  Widget _buildPostContent() {
    return FutureBuilder<PostItem>(
      future: _postFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
              child: SpinKitPulsingGrid(
            size: 20,
            color: Colors.grey,
          ));
        } else if (snapshot.hasError) {
          return Center(child: Text('加载文章失败: ${snapshot.error}'));
        } else if (snapshot.hasData) {
          final post = snapshot.data!;
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(post.title, style: Theme.of(context).textTheme.titleLarge),
              const SizedBox(height: 10),
              _buildPostMetadata(post),
              const SizedBox(height: 16),
              ErphpdownContent(post: post),
            ],
          );
        } else {
          return const Center(child: Text('没有文章数据'));
        }
      },
    );
  }

  Widget _buildPostMetadata(PostItem post) {
    return Row(
      children: [
        _buildMetadataItem(Icons.calendar_today, post.date),
        const SizedBox(width: 16),
        _buildMetadataItem(Icons.remove_red_eye, '${post.view} 浏览'),
        const SizedBox(width: 16),
        _buildMetadataItem(Icons.comment, '${post.ping} 评论'),
      ],
    );
  }

  Widget _buildMetadataItem(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 14, color: Colors.grey),
        const SizedBox(width: 4),
        Text(text, style: const TextStyle(color: Colors.grey)),
      ],
    );
  }

  Widget _buildRelatedPosts() {
    return FutureContentWidget<PostItem>(
      future: _relatedPostsFuture,
      title: '相关文章',
      builder: (context, posts) {
        return PostListView(articles: posts, hasNext: false);
      },
    );
  }

  Widget _buildComments() {
    return FutureContentWidget<Comment>(
      future: _commentsFuture,
      title: '评论',
      builder: (context, comments) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: comments.map(_buildCommentItem).toList(),
        );
      },
    );
  }

  Widget _buildCommentItem(Comment comment) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            child: Text(comment.author),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(comment.author,
                    style: const TextStyle(fontWeight: FontWeight.bold)),
                Text(comment.content),
                Text(comment.date,
                    style: const TextStyle(color: Colors.grey, fontSize: 12)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentInput() {
    return Container(
      padding: const EdgeInsets.all(8),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _commentController,
              decoration: const InputDecoration(
                hintText: '写评论',
                border: OutlineInputBorder(),
              ),
            ),
          ),
          const SizedBox(width: 8),
          ElevatedButton(
            onPressed: () {
              // TODO: 实现发送评论功能
            },
            child: const Text('发送'),
          ),
        ],
      ),
    );
  }

  void _handleLinkTap(BuildContext context, String url) async {
    try {
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;

      if (pathSegments.isEmpty) return;

      final type = pathSegments[0];
      final id = pathSegments.length > 1 ? pathSegments[1] : null;

      if (id == null) {
        // 处理外部链接
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri);
        } else {
          _logger.warning('无法打开链接: $url');
          _showErrorSnackBar('无法打开链接: $url');
        }
        return;
      }

      // 处理应用内链接
      switch (type) {
        case 'page':
        case 'post':
        case 'category':
          Navigator.pushNamed(context, '/$type/$id');
          break;
        default:
          _logger.warning('未知的链接类型: $type');
          _showErrorSnackBar('未知的链接类型: $type');
          break;
      }
    } catch (e) {
      _logger.severe('处理链接时出错: $e');
      _showErrorSnackBar('处理链接时出错: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Text(message),
      duration: const Duration(seconds: 3),
    ));
  }
}
