import 'dart:io';
import 'dart:async';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';
import 'package:flutter/foundation.dart';

class LogService {
  static final LogService _instance = LogService._internal();
  factory LogService() => _instance;
  LogService._internal();

  File? _logFile;
  bool _isLoggingEnabled = false;
  static const String _logEnabledKey = 'log_enabled';
  final List<String> _memoryLogs = [];
  static const int _maxMemoryLogs = 1000; // 内存中保留的最大日志条数
  final Set<String> _logCache = {}; // 用于防止重复日志的缓存
  StreamSubscription<LogRecord>? _logSubscription;
  final Logger _logger = Logger('LogService');

  // 需要过滤的系统日志标识
  static final List<String> _systemLogPatterns = [
    'HandWritingStubImpl',
    'DecorView',
    'ViewRootImpl',
    'SurfaceView',
    'Choreographer',
    'AdrenoUtils',
    'Gralloc',
    'GraphicBuffer',
    'TrafficStats',
    'AppScoutStateMachine',
    'qdgralloc',
    'AHardwareBuffer',
    'MiuiMagicPointer',
    'Looper',
    'liteav',
    'FirebaseInstanceId',
    'VRI[',
  ];

  // 是否捕获系统日志
  bool _captureSystemLogs = false;

  // 是否是系统日志
  bool _isSystemLog(String message) {
    if (_captureSystemLogs) {
      return false; // 如果启用了系统日志捕获，则不过滤任何日志
    }

    for (final pattern in _systemLogPatterns) {
      if (message.contains(pattern)) {
        return true;
      }
    }
    return false;
  }

  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isLoggingEnabled = prefs.getBool(_logEnabledKey) ?? false;

      // 取消旧的订阅
      _logSubscription?.cancel();

      // 设置日志监听器
      _logSubscription = Logger.root.onRecord.listen((record) {
        final timestamp = DateFormat('HH:mm:ss.SSS').format(record.time);
        final logMessage =
            '[$timestamp] ${record.level.name}: ${record.loggerName}: ${record.message}';

        // 过滤系统日志，只记录应用日志
        if (_isSystemLog(record.message)) {
          return; // 跳过系统日志
        }

        // 防止重复日志 - 使用消息内容和时间戳的组合作为唯一标识
        final logKey =
            '${record.time.millisecondsSinceEpoch}:${record.loggerName}:${record.message}';
        if (_logCache.contains(logKey)) {
          return; // 跳过重复日志
        }

        // 添加到缓存
        _logCache.add(logKey);

        // 限制缓存大小
        if (_logCache.length > 500) {
          _logCache.remove(_logCache.first);
        }

        // 添加到内存缓存
        _addToMemoryLogs(logMessage);

        // 如果启用了文件记录，写入文件
        if (_isLoggingEnabled) {
          _writeToFile(logMessage);
        }

        // 始终打印到控制台，确保在开发时可见
        debugPrint(logMessage);
      });

      if (_isLoggingEnabled) {
        await _createNewLogFile();
      }

      // 添加一个全局未捕获异常处理器
      FlutterError.onError = (FlutterErrorDetails details) {
        _logger.severe('未捕获的Flutter错误: ${details.exception}', details.exception,
            details.stack);
        // 继续使用Flutter的默认错误处理
        FlutterError.dumpErrorToConsole(details);
      };

      // 恢复原始的print函数，确保所有print调用都能显示
      debugPrint = debugPrintThrottled;
    } catch (e) {
      debugPrint('初始化日志服务失败: $e');
    }
  }

  void _addToMemoryLogs(String message) {
    _memoryLogs.add(message);
    if (_memoryLogs.length > _maxMemoryLogs) {
      _memoryLogs.removeAt(0);
    }
  }

  Future<void> _writeToFile(String message) async {
    if (_logFile != null) {
      try {
        // 确保文件存在
        if (!await _logFile!.exists()) {
          await _createNewLogFile();
        }
        await _logFile!.writeAsString('$message\n', mode: FileMode.append);
      } catch (e) {
        debugPrint('写入日志文件失败: $e');
      }
    } else {
      // 如果文件不存在，尝试创建
      try {
        await _createNewLogFile();
        await _logFile?.writeAsString('$message\n', mode: FileMode.append);
      } catch (e) {
        debugPrint('创建并写入日志文件失败: $e');
      }
    }
  }

  Future<void> _createNewLogFile() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final now = DateTime.now();
      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(now);
      final logFilePath = '${appDir.path}/app_log_$timestamp.txt';
      _logFile = File(logFilePath);

      // 确保文件存在且可写
      if (!await _logFile!.exists()) {
        await _logFile!.create(recursive: true);
      }
      await _logFile!.writeAsString('=== 日志开始: ${now.toString()} ===\n');
      debugPrint('创建新日志文件: $logFilePath');
    } catch (e) {
      debugPrint('创建日志文件失败: $e');
      _logFile = null;
    }
  }

  Future<void> setLoggingEnabled(bool enabled) async {
    try {
      _isLoggingEnabled = enabled;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_logEnabledKey, enabled);

      if (enabled) {
        if (_logFile == null || !await _logFile!.exists()) {
          await _createNewLogFile();
        }
        _logger.info('日志记录已开启');
      } else {
        _logger.info('日志记录已关闭');
      }
    } catch (e) {
      debugPrint('设置日志记录状态失败: $e');
    }
  }

  bool get isLoggingEnabled => _isLoggingEnabled;

  Future<String> getLogContent() async {
    try {
      // 首先检查是否启用了文件日志
      if (_isLoggingEnabled && _logFile != null) {
        // 检查文件是否存在
        if (await _logFile!.exists()) {
          // 尝试读取文件内容
          final content = await _logFile!.readAsString();
          // 如果内容为空，返回内存日志
          if (content.trim().isEmpty) {
            return _memoryLogs.join('\n');
          }
          return content;
        }
      }

      // 如果文件不存在或未启用文件日志，返回内存中的日志
      return _memoryLogs.join('\n');
    } catch (e) {
      debugPrint('获取日志内容失败: $e');
      // 发生错误时，尝试返回内存中的日志
      return '读取日志文件失败: $e\n\n内存中的日志:\n${_memoryLogs.join('\n')}';
    }
  }

  Future<List<String>> getAllLogFiles() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final files = appDir
          .listSync()
          .where((entity) => entity is File && entity.path.contains('app_log_'))
          .map((file) => file.path)
          .toList();
      return files;
    } catch (e) {
      debugPrint('获取所有日志文件失败: $e');
      return [];
    }
  }

  Future<void> clearAllLogs() async {
    try {
      _memoryLogs.clear();

      // 获取并删除所有日志文件
      final files = await getAllLogFiles();
      for (var filePath in files) {
        try {
          final file = File(filePath);
          if (await file.exists()) {
            await file.delete();
          }
        } catch (e) {
          debugPrint('删除日志文件失败: $filePath, 错误: $e');
        }
      }

      _logFile = null;

      // 如果日志功能启用，创建新的日志文件
      if (_isLoggingEnabled) {
        await _createNewLogFile();
      }

      _logger.info('所有日志已清除');
    } catch (e) {
      debugPrint('清除所有日志失败: $e');
    }
  }

  /// 获取当前日志文件的路径
  Future<String?> getCurrentLogFilePath() async {
    if (_logFile != null && await _logFile!.exists()) {
      return _logFile!.path;
    }
    return null;
  }

  /// 创建一个包含所有日志的临时文件，用于分享
  Future<File?> createShareableLogFile() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final now = DateTime.now();
      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(now);
      final shareableFile =
          File('${tempDir.path}/estockcafe_logs_$timestamp.txt');

      // 获取日志内容
      String logContent;
      if (_isLoggingEnabled && _logFile != null && await _logFile!.exists()) {
        // 如果启用了文件日志，使用文件中的内容
        logContent = await _logFile!.readAsString();
      } else {
        // 否则使用内存中的日志
        logContent = _memoryLogs.join('\n');
      }

      // 添加设备和应用信息
      final header = '''====== 股市咖啡屋日志文件 ======
时间: ${now.toString()}
日志级别: ${Logger.root.level.name}
============================

''';

      // 写入文件
      await shareableFile.writeAsString(header + logContent);

      return shareableFile;
    } catch (e) {
      debugPrint('创建可分享日志文件失败: $e');
      return null;
    }
  }

  /// 获取所有可用日志文件的路径列表
  Future<List<Map<String, dynamic>>> getAvailableLogFiles() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final entities = appDir.listSync().where(
          (entity) => entity is File && entity.path.contains('app_log_'));

      final result = <Map<String, dynamic>>[];

      for (var entity in entities) {
        final file = entity as File;
        final fileName = file.path.split('/').last;
        final stats = await file.stat();

        // 解析日期时间戳
        String dateInfo = '未知日期';
        final regex = RegExp(r'app_log_(\d{8})_(\d{6})\.txt');
        final match = regex.firstMatch(fileName);
        if (match != null) {
          final dateStr = match.group(1)!;
          final timeStr = match.group(2)!;
          final year = dateStr.substring(0, 4);
          final month = dateStr.substring(4, 6);
          final day = dateStr.substring(6, 8);
          final hour = timeStr.substring(0, 2);
          final minute = timeStr.substring(2, 4);
          final second = timeStr.substring(4, 6);
          dateInfo = '$year-$month-$day $hour:$minute:$second';
        }

        result.add({
          'path': file.path,
          'name': fileName,
          'size': stats.size,
          'modified': stats.modified,
          'dateInfo': dateInfo,
        });
      }

      // 按修改时间排序，最新的排在前面
      result.sort((a, b) =>
          (b['modified'] as DateTime).compareTo(a['modified'] as DateTime));

      return result;
    } catch (e) {
      debugPrint('获取可用日志文件失败: $e');
      return [];
    }
  }

  /// 捕获系统打印输出并记录
  void captureSystemLogs(bool enabled) {
    _captureSystemLogs = enabled;
    if (enabled) {
      _logger.info('已开启系统日志捕获');
    } else {
      _logger.info('已关闭系统日志捕获');
    }
  }
}
