import 'package:flutter/widgets.dart';
import 'package:logging/logging.dart';
import 'navigation_service.dart';
import 'app_state_manager.dart';
import '../utils/app_state.dart';

// 路由观察者 - 监听路由变化并更新应用状态
class AppRouteObserver implements NavigatorObserver {
  final NavigationService _navigationService;
  final AppStateManager _stateManager = AppStateManager();
  final Logger _logger = Logger('AppRouteObserver');

  AppRouteObserver(this._navigationService);

  @override
  NavigatorState? get navigator => null;

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    _handleRouteChange(route);
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    if (newRoute != null) {
      _handleRouteChange(newRoute);
    }
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    if (previousRoute != null) {
      _handleRouteChange(previousRoute);
    }
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    if (previousRoute != null) {
      _handleRouteChange(previousRoute);
    }
  }

  @override
  void didStartUserGesture(
      Route<dynamic> route, Route<dynamic>? previousRoute) {}

  @override
  void didStopUserGesture() {}

  @override
  void didChangeTop(Route? route, Route? previousRoute) {
    if (route != null) {
      _handleRouteChange(route);
    }
  }

  void _handleRouteChange(Route<dynamic> route) {
    final routeName = route.settings.name;
    if (routeName != null) {
      _logger.info('路由变化: $routeName');
      _navigationService.setCurrentRoute(routeName);

      // 检查是否是聊天室路由
      if (routeName.startsWith('/chatroom')) {
        // 提取频道名称
        final parts = routeName.split('/');
        final channelName = (parts.length > 2) ? parts[2] : null;

        _logger.info('检测到聊天室路由，频道: $channelName');

        if (channelName != null) {
          // 使用静默方式更新状态，避免触发WebSocket消息
          _stateManager.updateStateWithoutMessage(AppState.CHATROOM_ACTIVE,
              channelName: channelName);
        }
      } else {
        _logger.info('非聊天室路由，更新为APP_ACTIVE状态');
        // 使用静默方式更新状态，避免触发WebSocket消息
        _stateManager.updateStateWithoutMessage(AppState.APP_ACTIVE);
      }
    }
  }
}
