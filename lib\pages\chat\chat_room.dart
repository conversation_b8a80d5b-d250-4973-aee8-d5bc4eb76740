import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../models/chat_message.dart';
import '../../providers/auth_provider.dart';
import '../../services/chat_database_service.dart';
import '../../services/chat_notification_service.dart';
import '../../services/global_chat_service.dart';
import '../../services/token_service.dart';
import '../../services/user_service.dart';
import '../../theme.dart';
import '../../widgets/bottom_navbar.dart';
import 'chat_input.dart';
import 'chat_message.dart' as chat_widget;
import '../../utils/app_state.dart';
import '../../services/app_state_manager.dart';

class ChatRoomPage extends StatefulWidget {
  final String? messageId; // 添加messageId参数

  const ChatRoomPage({Key? key, this.messageId}) : super(key: key);

  @override
  State<ChatRoomPage> createState() => _ChatRoomPageState();
}

class _ChatRoomPageState extends State<ChatRoomPage>
    with TickerProviderStateMixin {
  final _logger = Logger('ChatRoomPage');
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  List<ChatMessageData> _messages = [];
  ChatMessageData? _replyTo;
  bool _isConnected = false;
  bool _isLoading = false;
  bool _disposed = false;
  List<String> _selectedImages = [];
  bool _connecting = false;
  late GlobalChatService _globalChatService;
  late AuthProvider _authProvider;
  late AppStateManager _appStateManager;
  StreamSubscription? _messageSubscription;
  StreamSubscription? _connectionSubscription;
  late AnimationController _connectionStatusAnimController;

  // 添加消息撤回功能的状态
  Map<String, bool> _canRevokeMessages = {};

  // 添加图片预览状态
  String? _previewImageUrl;

  // 添加新消息提示状态
  bool _hasNewMessages = false;

  // 目标消息ID，用于从通知跳转
  String? _targetMessageId;

  // 是否正在查看旧消息
  bool _isViewingOldMessages = false;

  @override
  void initState() {
    super.initState();

    // 初始化连接状态动画控制器
    _connectionStatusAnimController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300), // 减少动画时间
    );

    // 立即设置加载状态
    _isLoading = true;

    // 立即获取服务实例，不等待帧回调
    _globalChatService = Provider.of<GlobalChatService>(context, listen: false);
    _authProvider = Provider.of<AuthProvider>(context, listen: false);
    _appStateManager = Provider.of<AppStateManager>(context, listen: false);

    // 检查连接状态并立即更新UI
    _isConnected = _globalChatService.isConnected;
    if (_isConnected) {
      _connectionStatusAnimController.forward();
    }

    // 更新应用状态 - 进入聊天室
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        try {
          // 从路由参数获取频道名称，而不是依赖于_globalChatService.channelName
          final routeData = ModalRoute.of(context)?.settings.arguments;
          String? channelName;

          if (routeData != null && routeData is Map<String, dynamic>) {
            // 如果有传递频道名称参数，使用它
            channelName = routeData['channelName'] as String?;
          }

          // 如果从路由参数中没有获取到频道名称，尝试从_globalChatService获取
          channelName ??= _globalChatService.channelName;

          if (channelName != null && channelName.isNotEmpty) {
            _logger.info('更新应用状态为CHATROOM_ACTIVE，频道: $channelName');
            // 使用静默方式更新状态，避免触发WebSocket消息
            _appStateManager.updateStateWithoutMessage(AppState.CHATROOM_ACTIVE,
                channelName: channelName);
          } else {
            _logger.warning('无法更新应用状态：频道名称为空');
          }
        } catch (e) {
          _logger.warning('更新应用状态失败: $e');
        }
      }
    });

    // 使用WidgetsBinding.instance.addPostFrameCallback确保在构建完成后执行
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 清理数据库中的空系统消息
      _cleanEmptySystemMessages();

      // 快速检查用户登录状态，避免不必要的令牌刷新
      _quickCheckAuth();
    });
  }

  // 快速检查用户登录状态，不进行令牌刷新
  Future<void> _quickCheckAuth() async {
    // 1. 先检查用户对象是否已存在(内存中)
    if (_authProvider.user != null) {
      _logger.info('用户已登录(内存中存在用户对象)');
      await _initializeForLoggedInUser();
      return;
    }

    // 2. 检查是否有刷新令牌
    try {
      if (_disposed || !mounted) return;

      TokenService? tokenService;
      try {
        tokenService = Provider.of<TokenService>(context, listen: false);
      } catch (e) {
        _logger.warning('获取TokenService失败: $e');
        _handleNotLoggedIn();
        return;
      }

      final refreshToken = await tokenService.getRefreshToken();

      if (refreshToken == null) {
        _logger.info('用户未登录，无刷新令牌');
        _handleNotLoggedIn();
        return;
      }

      if (_disposed || !mounted) return;

      // 有刷新令牌但没有用户对象，尝试一次加载用户信息
      _logger.info('发现刷新令牌，尝试获取用户信息');

      UserService? userService;
      try {
        userService = Provider.of<UserService>(context, listen: false);
      } catch (e) {
        _logger.warning('获取UserService失败: $e');
        _handleNotLoggedIn();
        return;
      }

      final user = await userService.getCurrentUser();

      if (_disposed || !mounted) return;

      if (user != null) {
        // 获取用户成功，用户对象会自动更新到AuthProvider
        _logger.info('成功获取用户信息');
        await _initializeForLoggedInUser();
      } else {
        // 无法获取用户信息，可能是令牌无效
        _logger.warning('无法获取用户信息，令牌可能无效');
        _handleNotLoggedIn();
      }
    } catch (e, stackTrace) {
      _logger.severe('检查登录状态出错: $e');
      _logger.severe('堆栈跟踪: $stackTrace');
      _handleNotLoggedIn();
    }
  }

  // 处理未登录状态
  void _handleNotLoggedIn() {
    _safeSetState(() {
      _isLoading = false;
    });

    // 跳转到登录页面
    if (mounted) {
      Navigator.of(context).pushReplacementNamed('/login');
    }
  }

  // 针对已登录用户的初始化
  Future<void> _initializeForLoggedInUser() async {
    if (_disposed || !mounted) return;

    try {
      if (!mounted) return;

      // 清除通知
      Provider.of<ChatNotificationService>(context, listen: false)
          .clearMessages();

      // 标记所有消息为已读
      _markAllMessagesAsRead();

      // 清理数据库中的空系统消息
      await _cleanEmptySystemMessages();
      if (!mounted) return;

      // 检查目标消息ID
      _checkTargetMessage();

      // 加载本地缓存的消息
      await _loadLocalMessages();

      // 请求最新消息，确保消息列表是最新的
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && _globalChatService.isConnected) {
          _logger.info('请求最新离线消息');

          // 计算时间点：使用最近6小时或最后一条消息的时间
          DateTime lastMessageTime;
          if (_messages.isNotEmpty) {
            // 找出最近一条消息的时间
            int? latestTimestamp = 0;
            for (var msg in _messages) {
              int msgTime = msg.createTime ??
                  (msg.timestamp.millisecondsSinceEpoch ~/ 1000);
              if (msgTime > latestTimestamp!) {
                latestTimestamp = msgTime;
              }
            }
            lastMessageTime =
                DateTime.fromMillisecondsSinceEpoch(latestTimestamp! * 1000);
          } else {
            // 如果没有消息，使用6小时前的时间点
            lastMessageTime = DateTime.now().subtract(const Duration(hours: 6));
          }

          // 转换为时间戳字符串
          String lastMessageTimeStr =
              (lastMessageTime.millisecondsSinceEpoch ~/ 1000).toString();

          // 获取WebSocket实例并请求离线消息
          try {
            final webSocketUtility = _globalChatService.getWebSocketUtility();
            if (webSocketUtility != null) {
              webSocketUtility.fetchOfflineMessages(lastMessageTimeStr);
              _logger.info('已请求离线消息，从时间点: $lastMessageTimeStr');
            } else {
              _logger.warning('无法获取WebSocket实例');
            }
          } catch (e) {
            _logger.warning('请求离线消息失败: $e');
          }
        }
      });

      // 注册消息和连接监听器
      _setupListeners();

      // 检查用户权限并连接聊天
      // 使用微任务确保在当前帧完成后执行
      Future.microtask(() {
        if (mounted) {
          _checkPermissionAndConnect();
        }
      });

      // 在初始化完成后，再次确保应用状态为CHATROOM_ACTIVE
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          try {
            final channelName = _globalChatService.channelName;
            if (channelName != null && channelName.isNotEmpty) {
              _logger.info('初始化完成后再次更新应用状态为CHATROOM_ACTIVE，频道: $channelName');
              _appStateManager.updateStateWithoutMessage(
                  AppState.CHATROOM_ACTIVE,
                  channelName: channelName);
            }
          } catch (e) {
            _logger.warning('更新应用状态失败: $e');
          }
        }
      });
    } catch (e, stackTrace) {
      _logger.severe('初始化已登录用户失败: $e');
      _logger.severe('堆栈跟踪: $stackTrace');
      if (mounted) {
        _safeSetState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 检查目标消息ID
  void _checkTargetMessage() {
    if (!mounted) return;

    final args = ModalRoute.of(context)?.settings.arguments;
    if (args != null && args is Map<String, dynamic>) {
      _targetMessageId = args['messageId'] as String?;
      if (_targetMessageId != null) {
        _logger.info('需要滚动到消息ID: $_targetMessageId');
        // 延迟执行，确保消息列表已加载
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            _scrollToMessage(_targetMessageId!);
          }
        });
      }
    }
  }

  // 设置监听器
  void _setupListeners() {
    // 注册消息监听器
    _messageSubscription = _globalChatService.messageStream.listen((message) {
      _handleNewMessage(message);

      // 如果用户当前在聊天室，自动标记新消息为已读
      if (mounted && ModalRoute.of(context)?.isCurrent == true) {
        _markMessageAsRead(message.id);
      }
    });

    // 注册连接状态监听器
    _connectionSubscription =
        _globalChatService.connectionStream.listen((connected) {
      if (mounted) {
        _safeSetState(() {
          _isConnected = connected;
          if (connected) {
            _connectionStatusAnimController.forward();
          } else {
            _connectionStatusAnimController.reverse();
          }
        });
      }
    });

    // 初始化滚动控制器
    _scrollController.addListener(_onScroll);
  }

  // 检查权限并连接聊天
  void _checkPermissionAndConnect() {
    if (_disposed || !mounted) return;

    if (_authProvider.user == null) {
      _safeSetState(() {
        _isLoading = false;
      });
      return;
    }

    final user = _authProvider.user!;
    final currentTime = _authProvider.getCurrentTimestamp();
    final vipLevel = (user.vip['level'] as int?) ?? 0;
    final vipEndTime = (user.vip['end'] as int?) ?? 0;
    final hasPermission = vipLevel > 0 && vipEndTime > currentTime;

    _logger.info('用户VIP等级: $vipLevel, 到期时间: $vipEndTime, 当前时间: $currentTime');
    _logger.info('用户聊天权限: $hasPermission');

    if (hasPermission && !_isConnected) {
      // 使用Future.microtask确保connect操作在当前帧之后执行
      // 这样可以防止因页面导航而导致的context被处置问题
      Future.microtask(() {
        if (mounted) {
          _connectChat();
        }
      });
    } else {
      _safeSetState(() {
        _isLoading = false;
      });
    }
  }

  // 从本地数据库加载消息
  Future<void> _loadLocalMessages() async {
    try {
      if (!mounted) return;
      _logger.info('从本地缓存加载消息');

      // 立即从本地缓存加载消息并显示
      final localMessages = _globalChatService.messages;

      if (!mounted) return;

      if (localMessages.isNotEmpty) {
        _safeSetState(() {
          // 清空现有消息，避免可能的重复
          _messages.clear();
          _messages.addAll(localMessages);
          _sortMessages();
          _isLoading = false;
        });

        _logger.info('从本地缓存加载了 ${localMessages.length} 条消息');

        // 判断是否需要滚动到特定消息
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            if (_targetMessageId != null) {
              _logger.info('检测到目标消息ID: $_targetMessageId，尝试滚动到该消息');
              _scrollToMessage(_targetMessageId!);
            } else {
              // 否则默认滚动到底部
              _scrollToBottom();
            }
          }
        });
      } else {
        _safeSetState(() {
          _isLoading = false;
        });
        _logger.warning('本地缓存中没有消息');
      }
    } catch (e) {
      if (mounted) {
        _safeSetState(() {
          _isLoading = false;
        });
      }
      _logger.severe('从本地缓存加载消息失败: $e');
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // 首先检查widget传入的messageId
    if (widget.messageId != null && widget.messageId != _targetMessageId) {
      _logger.info('组件参数中发现目标消息ID: ${widget.messageId}');
      _targetMessageId = widget.messageId;

      // 如果消息已加载，立即滚动到指定消息
      if (!_isLoading && _messages.isNotEmpty && mounted) {
        _scrollToMessage(_targetMessageId!);
      }
      // 否则会在_loadLocalMessages中处理滚动
    }

    // 然后检查路由参数中的messageId
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args != null && args is Map<String, dynamic>) {
      String? newMessageId = args['messageId'] as String?;
      if (newMessageId != null && newMessageId != _targetMessageId) {
        _logger.info('路由参数中发现新的消息ID: $newMessageId');
        _targetMessageId = newMessageId;

        // 如果消息已加载，立即滚动到指定消息
        if (!_isLoading && _messages.isNotEmpty && mounted) {
          _scrollToMessage(_targetMessageId!);
        }
        // 否则会在_loadLocalMessages中处理滚动
      }
    }
  }

  @override
  void dispose() {
    _logger.info('ChatRoomPage销毁');
    _disposed = true;

    // 更新应用状态 - 离开聊天室
    try {
      _logger.info('更新应用状态为APP_ACTIVE (离开聊天室)');
      // 使用静默方式更新状态，避免触发WebSocket消息，并且不通知监听器
      _appStateManager.updateStateWithoutMessage(AppState.APP_ACTIVE,
          notify: false);
    } catch (e) {
      _logger.warning('更新应用状态失败: $e');
    }

    // 取消消息监听
    _messageSubscription?.cancel();
    _connectionSubscription?.cancel();

    // 取消重连定时器
    _reconnectTimer?.cancel();

    // 销毁动画控制器
    _connectionStatusAnimController.dispose();

    // 取消滚动监听
    _scrollController.dispose();

    // 销毁消息控制器
    _messageController.dispose();

    super.dispose();
  }

  void _safeSetState(VoidCallback fn) {
    if (!_disposed && mounted) {
      setState(fn);
    }
  }

  Future<void> _connectChat() async {
    if (_disposed || !mounted) return;

    try {
      // 检查用户是否已登录
      if (_authProvider.user == null) {
        _logger.warning('用户未登录，无法连接聊天室');
        // 跳转到登录页面
        if (mounted) {
          Navigator.of(context).pushReplacementNamed('/login');
        }
        return;
      }

      // 尝试获取访问令牌，确保连接前有有效令牌
      String? accessToken = await _authProvider.getAccessToken();
      if (accessToken == null) {
        _logger.info('连接聊天室前尝试刷新访问令牌');
        bool refreshed = await _authProvider.refreshUserInfo(notify: false);
        if (!refreshed) {
          _logger.warning('刷新访问令牌失败，但继续尝试连接');
        }
      }

      // 再次检查mounted状态，防止在异步操作过程中组件被卸载
      if (_disposed || !mounted) return;

      final user = _authProvider.user!;

      // 检查用户是否有权限访问聊天室
      final currentTime = _authProvider.getCurrentTimestamp();
      final vipLevel = (user.vip['level'] as int?) ?? 0;
      final vipEndTime = (user.vip['end'] as int?) ?? 0;
      final hasPermission = vipLevel > 0 && vipEndTime > currentTime;

      if (!hasPermission) {
        _logger.warning('用户无权限访问聊天室，VIP等级不足或已过期');
        // 如果已连接，断开连接
        if (_isConnected) {
          await _authProvider.disconnectFromChat();
          if (mounted) {
            _safeSetState(() {
              _isConnected = false;
            });
          }
        }
        return;
      }

      // 请求通知权限 - 移到后台处理，不阻塞UI
      if (mounted) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            try {
              final chatNotificationService =
                  Provider.of<ChatNotificationService>(context, listen: false);
              chatNotificationService.requestNotificationPermissions();
            } catch (e) {
              _logger.warning('请求通知权限失败: $e');
            }
          }
        });
      }

      // 获取全局聊天服务实例
      if (!mounted) return;

      final globalChatService =
          Provider.of<GlobalChatService>(context, listen: false);

      // 检查连接状态并同步到本地状态
      _safeSetState(() {
        _isConnected = globalChatService.isConnected;
      });

      // 如果已连接，直接加载历史消息
      if (_isConnected) {
        _logger.info('聊天室已连接，直接加载历史消息');
        // app打开后就一直连接着，不需要在这里请求历史消息
        return;
      }

      // 打印详细的连接信息，帮助调试
      _logger.info('===== 聊天连接调试信息 =====');
      _logger.info('用户ID: ${user.id}');
      _logger.info('用户名: ${user.name}');
      _logger.info('VIP级别: ${user.vip['level']}');
      _logger.info('VIP到期时间: ${user.vip['end']}');
      _logger.info('当前时间戳: $currentTime');
      _logger.info('聊天室配置: ${user.chatroom}');
      _logger.info('频道名称: ${user.chatroom['channel']}');
      _logger.info('Token: ${user.chatroom['token']}');
      _logger.info('页面ID: ${user.chatroom['page_id']}');
      _logger.info('===========================');

      // 确保chatroom中有channel字段
      if (user.chatroom['channel'] == null ||
          user.chatroom['channel'].toString().isEmpty) {
        _logger.severe('聊天室频道名称为空，无法连接');
        if (mounted) {
          _safeSetState(() {
            _isConnected = false;
          });
          // 显示错误提示
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('聊天室连接失败：频道名称为空'),
              duration: Duration(seconds: 3),
            ),
          );
        }
        return;
      }

      // 连接聊天室 - 使用更短的超时时间
      if (mounted) {
        _safeSetState(() {
          _connecting = true;
        });
      }

      // 先设置用户信息
      await globalChatService.setUserInfo(
        user.id.toString(),
        user.name.isEmpty ? 'User${user.id}' : user.name,
        user.chatroom.isEmpty ? {} : user.chatroom,
        hasPermission,
      );
      _logger.info('已设置用户信息，准备连接聊天室');

      // 再次检查mounted状态
      if (_disposed || !mounted) {
        _logger.info('组件已卸载，取消连接聊天室');
        return;
      }

      // 然后尝试连接
      bool connected = await _authProvider.connectToChat(context);

      // 再次检查mounted状态
      if (_disposed || !mounted) {
        _logger.info('组件已卸载，连接状态未更新');
        return;
      }

      _safeSetState(() {
        _connecting = false;
        _isConnected = globalChatService.isConnected;
      });

      _logger.info(
          '连接状态: globalChatService.isConnected=${globalChatService.isConnected}, authProvider.isChatConnected=${_authProvider.isChatConnected}');

      // 监听消息变化
      globalChatService.addListener(() {
        if (_disposed || !mounted) return;

        _safeSetState(() {
          _isConnected = globalChatService.isConnected;
          final newMessages = globalChatService.messages;

          // 更新消息列表
          _messages.clear();
          _messages.addAll(newMessages);

          // 更新可撤回消息状态
          _updateRevokableMessages();

          // 如果滚动位置在底部，则自动滚动到新消息
          if (_isScrollAtBottom()) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) _scrollToBottom();
            });
          }
        });
      });

      // 如果连接成功，加载历史消息
      if (connected && globalChatService.isConnected) {
        _logger.info('聊天室连接成功，加载最新消息');

        // 从服务器请求最新消息
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && globalChatService.isConnected) {
            // 连接成功后，确保更新应用状态为CHATROOM_ACTIVE
            try {
              final channelName = globalChatService.channelName;
              if (channelName != null && channelName.isNotEmpty) {
                _logger.info('聊天室连接成功后更新应用状态为CHATROOM_ACTIVE，频道: $channelName');
                _appStateManager.updateStateWithoutMessage(
                    AppState.CHATROOM_ACTIVE,
                    channelName: channelName);
              }
            } catch (e) {
              _logger.warning('连接成功后更新应用状态失败: $e');
            }

            // 请求离线消息而不是历史消息
            try {
              // 计算时间点：使用最近6小时
              final lastMsgTime =
                  DateTime.now().subtract(const Duration(hours: 6));
              String lastMessageTimeStr =
                  (lastMsgTime.millisecondsSinceEpoch ~/ 1000).toString();

              // 获取WebSocket实例并请求离线消息
              final webSocketUtility = globalChatService.getWebSocketUtility();
              if (webSocketUtility != null) {
                webSocketUtility.fetchOfflineMessages(lastMessageTimeStr);
                _logger.info('聊天室连接成功，请求离线消息，从时间点: $lastMessageTimeStr');
              } else {
                _logger.warning('无法获取WebSocket实例');
              }
            } catch (e) {
              _logger.warning('请求离线消息失败: $e');
            }
          }
        });
        return;
      } else {
        _logger.warning('连接聊天室失败，状态未同步');
        // 显示错误提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('聊天室连接失败：${globalChatService.lastError}'),
              duration: const Duration(seconds: 3),
            ),
          );
        }
        _scheduleReconnect();
      }
    } catch (e, stackTrace) {
      _logger.severe('连接聊天室失败: $e');
      _logger.severe('堆栈跟踪: $stackTrace');
      // 显示错误提示
      if (mounted) {
        try {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('聊天室连接失败: $e')),
          );
        } catch (e) {
          _logger.severe('显示错误提示失败: $e');
        }
      }
      _scheduleReconnect();
    }
  }

  void _scheduleReconnect() {
    if (_disposed || _reconnectTimer != null) return;

    // 检查用户是否已登录，如果未登录，不进行重连
    if (_authProvider.user == null) {
      _logger.info('用户未登录，取消重连计划');
      return;
    }

    final delay = min(30, pow(2, _reconnectAttempts).toInt()) * 1000;
    _reconnectAttempts++;

    _reconnectTimer = Timer(Duration(milliseconds: delay), () {
      _reconnectTimer = null;
      _connectChat();
    });
    _logger.info('计划在 ${delay}ms 后重新连接');
  }

  bool _isScrollAtBottom() {
    if (!_scrollController.hasClients) return true;

    final position = _scrollController.position;
    // 在ListView.reverse=true的情况下，pixels接近0表示在底部
    // 允许有10像素的误差，提供更好的用户体验
    return position.pixels <= 10;
  }

  void _scrollToBottom() {
    if (!_scrollController.hasClients) return;

    // 在ListView.reverse=true的情况下，滚动到最小位置（即底部）
    _scrollController.animateTo(
      0, // 对于reverse=true的ListView，0是底部
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );

    // 重置新消息提示
    if (_hasNewMessages) {
      _safeSetState(() {
        _hasNewMessages = false;
      });
    }

    _logger.fine('滚动到底部');
  }

  void _onScroll() {
    // 更新是否正在查看旧消息的状态
    if (_scrollController.hasClients) {
      final position = _scrollController.position;
      final isViewingOld = position.pixels < position.maxScrollExtent - 100;

      if (isViewingOld != _isViewingOldMessages) {
        _safeSetState(() {
          _isViewingOldMessages = isViewingOld;
        });
      }

      // app打开后就一直连接着，不需要在滚动到顶部时加载更多历史消息

      // 检查是否滚动到底部
      bool isAtBottom = _isScrollAtBottom();

      // 如果滚动到底部，重置新消息提示
      if (isAtBottom && _hasNewMessages) {
        _safeSetState(() {
          _hasNewMessages = false;
        });
        _logger.fine('用户滚动到底部，重置新消息提示');
      }

      // 智能标记已读：当用户滚动查看消息时，标记可见的消息为已读
      _markVisibleMessagesAsRead();
    }
  }

  // 更新可撤回消息状态
  void _updateRevokableMessages() {
    final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final currentUserId = _authProvider.user?.id.toString();

    if (currentUserId == null) return;

    for (var message in _messages) {
      // 只有自己发送的消息且在3分钟内的消息可以撤回
      if (message.userId == currentUserId) {
        final messageTime = message.createTime ??
            (message.timestamp.millisecondsSinceEpoch ~/ 1000);
        final canRevoke = (currentTime - messageTime) < 180; // 3分钟内可撤回
        _canRevokeMessages[message.id] = canRevoke;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final globalChatService = Provider.of<GlobalChatService>(context);

    // 如果用户未登录，跳转到登录页面
    if (!authProvider.isAuthenticated) {
      // 使用延迟导航，避免在build方法中直接返回Future
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(context).pushReplacementNamed('/login');
      });

      // 返回一个加载中的界面，等待导航完成
      return Scaffold(
        appBar: AppBar(
          title: const Text('聊天室'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // 检查用户是否有权限访问聊天室
    final user = authProvider.user;
    if (user == null) {
      return const Center(child: Text('用户未登录，无法访问聊天室'));
    }

    final currentTime = authProvider.getCurrentTimestamp();
    final vipLevel = (user.vip['level'] as int?) ?? 0;
    final vipEndTime = (user.vip['end'] as int?) ?? 0;
    final hasPermission = vipLevel > 0 && vipEndTime > currentTime;

    // 如果用户没有权限，显示提示
    if (!hasPermission) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('聊天室'),
        ),
        body: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.lock, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              const Text('您暂无权限访问聊天室',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Text('需要VIP会员才能访问聊天室', style: TextStyle(color: Colors.grey[600])),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/vip');
                },
                child: const Text('升级VIP'),
              ),
            ],
          ),
        ),
        bottomNavigationBar: const BottomNavBar(currentIndex: 1),
      );
    }

    // 如果聊天服务未连接且用户有权限，显示手动连接按钮
    if (!globalChatService.isConnected && !_connecting && hasPermission) {
      _connecting = false; // 更新状态
    }

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.grey[200], // 背景色调整为淡灰色
        centerTitle: true, // 标题居中显示
        title: Row(
          mainAxisSize: MainAxisSize.min, // 使Row的宽度适应内容
          children: [
            const Text('聊天室'),
            const SizedBox(width: 8),
            // 连接状态指示器
            AnimatedBuilder(
              animation: _connectionStatusAnimController,
              builder: (context, child) {
                return Container(
                  width: 10,
                  height: 10,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Color.lerp(Colors.red, Colors.green,
                        _connectionStatusAnimController.value),
                  ),
                );
              },
            ),
          ],
        ),
        actions: [
          // 添加更多选项菜单
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: AppColors.primaryGreen),
            elevation: 8,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            color: AppColors.primaryWhite,
            offset: const Offset(0, 40),
            onSelected: (value) {
              switch (value) {
                case 'notification_settings':
                  _showNotificationSettings();
                  break;
                case 'network_diagnosis':
                  _showNetworkDiagnosis();
                  break;
                case 'refresh':
                  _refreshMessages();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem<String>(
                value: 'notification_settings',
                height: 48,
                child: Row(
                  children: [
                    Icon(Icons.notifications,
                        size: 20, color: AppColors.primaryGreen),
                    SizedBox(width: 12),
                    Text(
                      '提醒设置',
                      style: TextStyle(
                        fontSize: AppFontSizes.fontSizeMedium,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textEmphasis,
                      ),
                    ),
                  ],
                ),
              ),
              const PopupMenuDivider(height: 1),
              const PopupMenuItem<String>(
                value: 'network_diagnosis',
                height: 48,
                child: Row(
                  children: [
                    Icon(Icons.network_check,
                        size: 20, color: AppColors.primaryGreen),
                    SizedBox(width: 12),
                    Text(
                      '网络诊断',
                      style: TextStyle(
                        fontSize: AppFontSizes.fontSizeMedium,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textEmphasis,
                      ),
                    ),
                  ],
                ),
              ),
              const PopupMenuDivider(height: 1),
              const PopupMenuItem<String>(
                value: 'refresh',
                height: 48,
                child: Row(
                  children: [
                    Icon(Icons.refresh,
                        size: 20, color: AppColors.primaryGreen),
                    SizedBox(width: 12),
                    Text(
                      '刷新消息',
                      style: TextStyle(
                        fontSize: AppFontSizes.fontSizeMedium,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textEmphasis,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
        elevation: 1, // 减小阴影
      ),
      body: Stack(
        children: [
          Column(
            children: [
              if (!_isConnected && !_connecting)
                Container(
                  color: Colors.red,
                  padding:
                      const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
                  child: Row(
                    children: [
                      const Icon(Icons.error_outline,
                          color: Colors.white, size: 16),
                      const SizedBox(width: 8),
                      const Expanded(
                        child: Text(
                          '未连接到聊天服务器...',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                      TextButton(
                        onPressed: () => _connectChat(),
                        child: const Text('重试',
                            style: TextStyle(color: Colors.white)),
                      ),
                    ],
                  ),
                ),
              Expanded(
                child: _isLoading
                    ? const Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SpinKitPulsingGrid(
                              size: 20,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 8), // 减少间距
                            Text('加载中...', // 简化文本
                                style: TextStyle(fontSize: 14)), // 减小字体大小
                          ],
                        ),
                      )
                    : _messages.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.chat_bubble_outline,
                                    size: 64, color: Colors.grey[400]),
                                const SizedBox(height: 16),
                                Text('暂无消息',
                                    style: TextStyle(color: Colors.grey[600])),
                                const SizedBox(height: 8),
                                if (!_isConnected)
                                  ElevatedButton(
                                    onPressed: () => _connectChat(),
                                    child: const Text('重新连接'),
                                  ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            controller: _scrollController,
                            reverse: true,
                            itemCount: _messages.length,
                            itemBuilder: (context, index) {
                              final message = _messages[index];
                              final isMe = message.userId ==
                                  Provider.of<AuthProvider>(context,
                                          listen: false)
                                      .user
                                      ?.id
                                      .toString();

                              return chat_widget.ChatMessage(
                                message: message,
                                isMe: isMe,
                                onTap: message.isImage
                                    ? () {
                                        // 点击图片查看大图
                                        _showImagePreview(context, message);
                                      }
                                    : null,
                                onLongPress: () {
                                  // 长按消息，显示操作选项
                                  _showMessageOptions(context, message, isMe);
                                },
                              );
                            },
                          ),
              ),
              ChatInput(
                controller: _messageController,
                onSendMessage: _sendMessage,
                onPickImage: _pickImage,
                replyTo: _replyTo,
                selectedImages: _selectedImages,
                onRemoveImage: (index) {
                  _safeSetState(() {
                    _selectedImages.removeAt(index);
                  });
                },
                onCancelReply: () {
                  _safeSetState(() {
                    _replyTo = null;
                  });
                },
                isDisabled: !_isConnected,
              ),
            ],
          ),
          if (_hasNewMessages && !_isLoading)
            Positioned(
              bottom: 80, // 上移到发送消息框的上面
              left: 0,
              right: 0,
              child: Center(
                child: GestureDetector(
                  onTap: () {
                    _scrollToBottom();
                    _safeSetState(() {
                      _hasNewMessages = false;
                    });
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green[400], // 改为淡绿色
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Text(
                      '新消息',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          // 图片预览层
          if (_previewImageUrl != null)
            GestureDetector(
              onTap: () {
                _safeSetState(() {
                  _previewImageUrl = null;
                });
              },
              child: Container(
                color: Colors.black.withOpacity(0.9),
                alignment: Alignment.center,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: InteractiveViewer(
                        minScale: 0.5,
                        maxScale: 3.0,
                        child: CachedNetworkImage(
                          imageUrl: _previewImageUrl!,
                          fit: BoxFit.contain,
                          placeholder: (context, url) => const Center(
                            child: SpinKitPulsingGrid(
                              size: 20,
                              color: Colors.grey,
                            ),
                          ),
                          errorWidget: (context, url, error) => const Icon(
                            Icons.error,
                            color: Colors.red,
                            size: 50,
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        '点击空白区域关闭',
                        style: TextStyle(color: Colors.white.withOpacity(0.7)),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
      // 使用自定义底部导航栏
      bottomNavigationBar: const BottomNavBar(currentIndex: 1),
    );
  }

  // 对消息进行排序
  void _sortMessages() {
    _messages.sort((a, b) {
      final timeA =
          a.createTime ?? (a.timestamp.millisecondsSinceEpoch ~/ 1000);
      final timeB =
          b.createTime ?? (b.timestamp.millisecondsSinceEpoch ~/ 1000);
      return timeB.compareTo(timeA); // 降序排列，新消息在前，旧消息在后
    });
  }

  void _handleNewMessage(ChatMessageData message) {
    if (_disposed || !mounted) return;

    try {
      // 检查消息是否已存在
      final existingIndex = _messages.indexWhere((m) => m.id == message.id);

      _safeSetState(() {
        if (existingIndex >= 0) {
          // 更新现有消息
          _messages[existingIndex] = message;
        } else {
          // 添加新消息
          _messages.add(message);
          _sortMessages();

          // 如果用户正在查看底部，则自动滚动到新消息并标记为已读
          if (_isScrollAtBottom()) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (_disposed || !mounted) return;

              _scrollToBottom();
              // 立即标记消息为已读
              _markMessageAsRead(message.id);

              // 清除通知服务中的未读消息
              try {
                final chatNotificationService =
                    Provider.of<ChatNotificationService>(context,
                        listen: false);
                chatNotificationService.clearUnreadMessages();
              } catch (e) {
                _logger.warning('清除未读消息失败: $e');
              }
            });
          } else {
            // 如果用户不在底部，显示新消息提示
            _hasNewMessages = true;
          }
        }

        // 更新可撤回消息状态
        _updateRevokableMessages();
      });
    } catch (e, stackTrace) {
      _logger.severe('处理新消息时出错: $e');
      _logger.severe('堆栈跟踪: $stackTrace');
    }
  }

  Future<List<String>> _pickImage() async {
    try {
      final picker = ImagePicker();
      final pickedFiles = await picker.pickMultiImage(
        imageQuality: 80, // 压缩图片质量以减小文件大小
      );

      if (pickedFiles.isEmpty) {
        return [];
      }

      List<String> base64Images = [];

      // 处理每张图片
      for (int i = 0; i < pickedFiles.length; i++) {
        final pickedFile = pickedFiles[i];

        // 读取图片文件并转换为base64
        final bytes = await pickedFile.readAsBytes();
        final base64Image = base64Encode(bytes);

        // 获取文件扩展名
        final fileExt = pickedFile.path.split('.').last.toLowerCase();
        final mimeType = 'image/$fileExt';

        // 创建base64图片URL
        final base64Url = 'data:$mimeType;base64,$base64Image';
        base64Images.add(base64Url);
      }

      // 添加到选择的图片列表
      setState(() {
        _selectedImages.addAll(base64Images);
      });

      return base64Images;
    } catch (e) {
      _logger.severe('选择图片失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('选择图片失败: $e')),
      );
      return [];
    }
  }

  Future<void> _sendMessage(String content, {List<String>? images}) async {
    if (!_isConnected) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('未连接到聊天服务器，无法发送消息')),
      );
      return;
    }

    if (content.isEmpty &&
        (images == null || images.isEmpty) &&
        _selectedImages.isEmpty) return;

    try {
      // 获取全局聊天服务
      final globalChatService =
          Provider.of<GlobalChatService>(context, listen: false);

      // 创建消息对象
      final message = {
        'content': content,
        'type': 'msg', // 修改为'msg'
        'user_id': _authProvider.user?.id.toString(), // 添加user_id
        'user_name': _authProvider.user?.name, // 添加user_name
        'create_time':
            DateTime.now().millisecondsSinceEpoch ~/ 1000, // 添加create_time
        'msg_id': DateTime.now().millisecondsSinceEpoch.toString() +
            '_' +
            (1000 + Random().nextInt(9000)).toString(), // 添加msg_id
      };

      // 如果有回复对象，添加到消息中
      if (_replyTo != null) {
        message['reply'] = _replyTo!.toJson();
      }

      // 使用传入的图片或已选择的图片，避免重复
      final allImages = images != null && images.isNotEmpty ? images : _selectedImages;

      // 如果有图片，添加到消息中
      if (allImages.isNotEmpty) {
        message['pics'] = allImages;
      }

      // 发送消息
      await globalChatService.sendMessage(message);

      // 清除回复对象和选择的图片
      setState(() {
        _replyTo = null;
        _selectedImages.clear();
      });

      // 清空输入框
      _messageController.clear();
    } catch (e) {
      _logger.severe('发送消息失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('发送消息失败: $e')),
      );
    }
  }

  void _showMessageOptions(
      BuildContext context, ChatMessageData message, bool isMe) {
    final canRevoke = isMe && (_canRevokeMessages[message.id] ?? false);

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundImage: message.userAvatar != null &&
                              message.userAvatar!.isNotEmpty
                          ? NetworkImage(message.userAvatar!)
                          : null,
                      child: message.userAvatar == null ||
                              message.userAvatar!.isEmpty
                          ? Text(message.userName.isNotEmpty
                              ? message.userName[0].toUpperCase()
                              : '?')
                          : null,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      message.userName,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
              const Divider(height: 1),
              ListTile(
                leading: const Icon(Icons.reply),
                title: const Text('回复'),
                onTap: () {
                  Navigator.of(context).pop();
                  setState(() {
                    _replyTo = message;
                  });
                },
              ),
              if (canRevoke)
                ListTile(
                  leading: const Icon(Icons.delete_outline),
                  title: const Text('撤回'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _revokeMessage(message);
                  },
                ),
              if (message.isImage)
                ListTile(
                  leading: const Icon(Icons.image),
                  title: const Text('查看图片'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _showImagePreview(context, message);
                  },
                ),
              ListTile(
                leading: const Icon(Icons.content_copy),
                title: const Text('复制文本'),
                onTap: () {
                  Navigator.of(context).pop();
                  // 复制消息内容到剪贴板
                  // Clipboard.setData(ClipboardData(text: message.content));
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('已复制到剪贴板')),
                  );
                },
              ),
              const SizedBox(height: 8),
            ],
          ),
        );
      },
    );
  }

  // 撤回消息
  Future<void> _revokeMessage(ChatMessageData message) async {
    try {
      // 获取全局聊天服务
      final globalChatService =
          Provider.of<GlobalChatService>(context, listen: false);

      // 创建撤回消息对象 - 使用与Web端一致的格式
      final revokeMessage = {
        'type': 'revoke',
        'msg_id': message.msgId ?? message.id, // 优先使用msgId，这可能是服务器识别的ID
        'user_id': _authProvider.user?.id.toString(),
        'user_name': _authProvider.user?.name,
        'create_time': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        'meta': {'revoke': 1}
      };

      // 打印撤回消息，用于调试
      _logger.info('发送撤回消息: ${jsonEncode(revokeMessage)}');

      // 发送撤回消息
      await globalChatService.sendMessage(revokeMessage);

      // 更新本地消息状态
      setState(() {
        final index = _messages.indexWhere((m) => m.id == message.id);
        if (index >= 0) {
          // 更新消息的metadata
          Map<String, dynamic> updatedMetadata =
              _messages[index].metadata != null
                  ? Map<String, dynamic>.from(_messages[index].metadata!)
                  : {};
          updatedMetadata['revoke'] = 1;

          _messages[index] = _messages[index]
              .copyWith(revoked: true, metadata: updatedMetadata);
        }
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('消息已撤回')),
      );
    } catch (e) {
      _logger.severe('撤回消息失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('撤回消息失败: $e')),
      );
    }
  }

  void _showImagePreview(BuildContext context, ChatMessageData message) {
    // 收集所有图片URL
    List<String> imageUrls = [];

    // 从imgs字段获取图片
    if (message.imgs != null && message.imgs!.isNotEmpty) {
      imageUrls.addAll(message.imgs!);
    }
    // 如果内容是图片URL，也添加到列表
    else if (message.isImage &&
        (message.content.startsWith('http') ||
            message.content.startsWith('data:image'))) {
      imageUrls.add(message.content);
    }

    // 从metadata.pics获取图片
    if (message.metadata != null &&
        message.metadata!['pics'] != null &&
        message.metadata!['pics'] is List) {
      List<dynamic> pics = message.metadata!['pics'];
      for (var pic in pics) {
        if (pic is String && !imageUrls.contains(pic)) {
          imageUrls.add(pic);
        }
      }
    }

    // 如果没有图片，直接返回
    if (imageUrls.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('没有可查看的图片')),
      );
      return;
    }

    // 打开图片查看器
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            iconTheme: const IconThemeData(color: Colors.white),
            title: Text(
              '图片预览 (${imageUrls.length}张)',
              style: const TextStyle(color: Colors.white),
            ),
          ),
          body: PageView.builder(
            itemCount: imageUrls.length,
            itemBuilder: (context, index) {
              final url = imageUrls[index];
              return GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Center(
                  child: InteractiveViewer(
                    minScale: 0.5,
                    maxScale: 4.0,
                    child: url.startsWith('data:image')
                        ? Image.memory(
                            base64Decode(url.split(',')[1]),
                            fit: BoxFit.contain,
                            errorBuilder: (context, error, stackTrace) =>
                                const Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.error,
                                      color: Colors.red, size: 48),
                                  SizedBox(height: 16),
                                  Text(
                                    'Base64图片解析失败',
                                    style: TextStyle(color: Colors.white70),
                                  ),
                                ],
                              ),
                            ),
                          )
                        : CachedNetworkImage(
                            imageUrl: url,
                            fit: BoxFit.contain,
                            placeholder: (context, url) => const Center(
                              child: CircularProgressIndicator(
                                  color: Colors.white54),
                            ),
                            errorWidget: (context, url, error) => const Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.error,
                                      color: Colors.red, size: 48),
                                  SizedBox(height: 16),
                                  Text(
                                    '图片加载失败',
                                    style: TextStyle(color: Colors.white70),
                                  ),
                                ],
                              ),
                            ),
                          ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  // 滚动到指定消息
  void _scrollToMessage(String messageId) {
    try {
      _logger.info('尝试滚动到消息: $messageId');

      // 查找消息在列表中的索引
      final index = _messages.indexWhere((msg) => msg.id == messageId);

      if (index != -1) {
        _logger.info('找到消息，索引: $index');

        // 计算滚动位置
        const itemHeight = 80.0; // 估计的消息高度
        final offset = index * itemHeight;

        // 滚动到消息位置
        _scrollController.animateTo(
          offset,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );

        // 高亮显示消息
        _highlightMessage(messageId);
      } else {
        _logger.warning('未找到消息: $messageId');

        // 如果消息不在当前列表中，可能需要加载更多历史消息
        // 这里可以添加加载更多历史消息的逻辑
      }
    } catch (e) {
      _logger.severe('滚动到消息失败: $e');
    }
  }

  // 高亮显示消息
  void _highlightMessage(String messageId) {
    // 这里可以添加高亮显示消息的逻辑
    // 例如，可以使用一个Map来存储消息的高亮状态
    // 然后在消息组件中根据高亮状态显示不同的背景色

    // 示例：设置高亮状态，并在3秒后取消高亮
    setState(() {
      // 假设有一个_highlightedMessageId变量
      // _highlightedMessageId = messageId;
    });

    // 3秒后取消高亮
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          // _highlightedMessageId = null;
        });
      }
    });
  }

  // 清理数据库中的空系统消息
  Future<void> _cleanEmptySystemMessages() async {
    try {
      if (!mounted) return;

      final globalChatService =
          Provider.of<GlobalChatService>(context, listen: false);
      final channelName = globalChatService.channelName;

      if (channelName != null && channelName.isNotEmpty) {
        // 获取ChatDatabaseService实例
        final chatDatabaseService =
            ChatDatabaseService(userId: _authProvider.user?.id.toString());

        // 清理数据库中的空系统消息
        final count =
            await chatDatabaseService.cleanEmptySystemMessages(channelName);
        if (!mounted) return;

        _logger.info('已从数据库中清理 $count 条空系统消息');

        // 清理内存中和数据库中的空系统消息
        await globalChatService.cleanEmptySystemMessages();
        if (!mounted) return;
      }
    } catch (e) {
      _logger.warning('清理空系统消息失败: $e');
    }
  }

  // 显示通知设置对话框
  void _showNotificationSettings() {
    final chatNotificationService =
        Provider.of<ChatNotificationService>(context, listen: false);

    // 获取当前设置
    bool playSound = chatNotificationService.playSoundOnNewMessage;
    String notificationType = chatNotificationService.notificationType;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            titlePadding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
            contentPadding: const EdgeInsets.fromLTRB(20, 0, 20, 10),
            actionsPadding: const EdgeInsets.fromLTRB(20, 0, 20, 16),
            backgroundColor: AppColors.primaryWhite,
            elevation: 8,
            title: const Row(
              children: [
                Icon(Icons.notifications,
                    color: AppColors.primaryGreen, size: 24),
                SizedBox(width: 12),
                Text(
                  '提醒设置',
                  style: TextStyle(
                    fontSize: AppFontSizes.fontSizeLarge,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textEmphasis,
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 提醒方式
                const Padding(
                  padding: EdgeInsets.only(left: 4, top: 8, bottom: 4),
                  child: Text(
                    '提醒方式:',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: AppFontSizes.fontSizeMedium,
                      color: AppColors.textEmphasis,
                    ),
                  ),
                ),
                Card(
                  elevation: 0,
                  color: AppColors.secondaryLightGreen.withValues(alpha: 77),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  child: RadioListTile<String>(
                    title: const Text(
                      '全部消息提醒',
                      style: TextStyle(
                        fontSize: AppFontSizes.fontSizeMedium,
                        color: AppColors.textEmphasis,
                      ),
                    ),
                    activeColor: AppColors.primaryGreen,
                    value: 'all',
                    groupValue: notificationType,
                    onChanged: (value) {
                      setState(() {
                        notificationType = value!;
                      });
                    },
                  ),
                ),
                Card(
                  elevation: 0,
                  color: AppColors.secondaryLightGreen.withValues(alpha: 77),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  child: RadioListTile<String>(
                    title: const Text(
                      '只提醒管理员消息',
                      style: TextStyle(
                        fontSize: AppFontSizes.fontSizeMedium,
                        color: AppColors.textEmphasis,
                      ),
                    ),
                    activeColor: AppColors.primaryGreen,
                    value: 'admin_only',
                    groupValue: notificationType,
                    onChanged: (value) {
                      setState(() {
                        notificationType = value!;
                      });
                    },
                  ),
                ),
                Card(
                  elevation: 0,
                  color: AppColors.secondaryLightGreen.withValues(alpha: 77),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  child: RadioListTile<String>(
                    title: const Text(
                      '只提醒回复我的消息',
                      style: TextStyle(
                        fontSize: AppFontSizes.fontSizeMedium,
                        color: AppColors.textEmphasis,
                      ),
                    ),
                    activeColor: AppColors.primaryGreen,
                    value: 'mention_only',
                    groupValue: notificationType,
                    onChanged: (value) {
                      setState(() {
                        notificationType = value!;
                      });
                    },
                  ),
                ),

                // 声音提醒
                const Padding(
                  padding: EdgeInsets.only(left: 4, top: 16, bottom: 4),
                  child: Text(
                    '声音设置:',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: AppFontSizes.fontSizeMedium,
                      color: AppColors.textEmphasis,
                    ),
                  ),
                ),
                Card(
                  elevation: 0,
                  color: AppColors.secondaryLightGreen.withValues(alpha: 77),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  child: SwitchListTile(
                    title: const Text(
                      '新消息声音提醒',
                      style: TextStyle(
                        fontSize: AppFontSizes.fontSizeMedium,
                        color: AppColors.textEmphasis,
                      ),
                    ),
                    activeColor: AppColors.primaryGreen,
                    value: playSound,
                    onChanged: (value) {
                      setState(() {
                        playSound = value;
                      });
                    },
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.grey,
                ),
                child: const Text('取消'),
              ),
              ElevatedButton(
                onPressed: () {
                  // 保存设置
                  chatNotificationService.setNotificationType(notificationType);
                  chatNotificationService.setPlaySoundOnNewMessage(playSound);
                  Navigator.of(context).pop();

                  // 显示保存成功提示
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('设置已保存'),
                      backgroundColor: AppColors.primaryGreen,
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryGreen,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('保存'),
              ),
            ],
          );
        },
      ),
    );
  }

  // 显示网络诊断对话框
  void _showNetworkDiagnosis() {
    final chatService = Provider.of<GlobalChatService>(context, listen: false);
    final wsStatus = chatService.isConnected ? "已连接" : "未连接";
    final error = chatService.lastError ?? "无错误";

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("网络诊断"),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("WebSocket状态: $wsStatus"),
            const SizedBox(height: 8),
            Text("最后错误: $error"),
            const SizedBox(height: 8),
            Text("WebSocket URL: ${chatService.wsUrl}"),
            const SizedBox(height: 8),
            Text("频道名称: ${chatService.channelName ?? '未设置'}"),
            const SizedBox(height: 8),
            Text("用户ID: ${chatService.userId ?? '未设置'}"),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              chatService.manualReconnect();
            },
            child: const Text("尝试重连"),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text("关闭"),
          ),
        ],
      ),
    );
  }

  // 刷新消息
  Future<void> _refreshMessages() async {
    // 显示加载指示器
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('正在重新连接聊天服务...'),
        duration: Duration(seconds: 1),
      ),
    );

    // 调用GlobalChatService的manualReconnect方法
    bool success = await Provider.of<GlobalChatService>(context, listen: false)
        .manualReconnect();

    if (mounted) {
      // 显示连接结果
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? '连接成功' : '连接失败，请稍后再试'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  // 标记所有消息为已读
  Future<void> _markAllMessagesAsRead() async {
    try {
      final chatNotificationService =
          Provider.of<ChatNotificationService>(context, listen: false);
      await chatNotificationService.clearUnreadMessages();
      _logger.info('已将所有消息标记为已读');
    } catch (e) {
      _logger.severe('标记所有消息为已读失败: $e');
    }
  }

  // 标记可见的消息为已读
  Future<void> _markVisibleMessagesAsRead() async {
    if (!_scrollController.hasClients || _messages.isEmpty) return;

    try {
      final chatDatabaseService =
          ChatDatabaseService(userId: _authProvider.user?.id.toString() ?? '');
      final visibleMessageIds = <String>[];

      // 获取当前可见的消息ID
      // 简化实现：将当前滚动位置附近的消息都标记为已读
      final firstVisibleIndex = _getFirstVisibleItemIndex();
      final lastVisibleIndex = _getLastVisibleItemIndex();

      if (firstVisibleIndex >= 0 && lastVisibleIndex >= 0) {
        for (int i = firstVisibleIndex; i <= lastVisibleIndex; i++) {
          if (i >= 0 && i < _messages.length) {
            visibleMessageIds.add(_messages[i].id);
          }
        }
      }

      // 批量标记可见消息为已读
      if (visibleMessageIds.isNotEmpty) {
        await chatDatabaseService.markMessagesAsRead(visibleMessageIds);
        _logger.fine('已将 ${visibleMessageIds.length} 条可见消息标记为已读');
      }
    } catch (e) {
      _logger.warning('标记可见消息为已读失败: $e');
    }
  }

  // 获取第一个可见项的索引
  int _getFirstVisibleItemIndex() {
    if (!_scrollController.hasClients) return -1;

    final position = _scrollController.position;
    // 由于ListView.reverse=true，所以计算方式需要调整
    return (_messages.length - 1) -
        (position.maxScrollExtent - position.pixels) ~/ 50;
  }

  // 获取最后一个可见项的索引
  int _getLastVisibleItemIndex() {
    if (!_scrollController.hasClients) return -1;

    final position = _scrollController.position;
    // 由于ListView.reverse=true，所以计算方式需要调整
    return (_messages.length - 1) - position.pixels ~/ 50;
  }

  // 标记单条消息为已读
  Future<void> _markMessageAsRead(String messageId) async {
    try {
      final chatDatabaseService =
          ChatDatabaseService(userId: _authProvider.user?.id.toString() ?? '');
      await chatDatabaseService.markMessageAsRead(messageId);
      _logger.fine('已将消息 $messageId 标记为已读');
    } catch (e) {
      _logger.warning('标记消息为已读失败: $e');
    }
  }
}

// ProgressDialog 类已移动到 widgets/progress_dialog.dart，统一使用
