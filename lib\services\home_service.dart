import 'dart:async';
import 'package:estockcafe/models/home.dart';
import 'package:estockcafe/config/config.dart';
import 'package:estockcafe/utils/logging.dart';
import 'package:logging/logging.dart';
import 'package:estockcafe/services/cache_service.dart';
import 'package:estockcafe/utils/functions.dart';

class HomeService {
  final String baseUrl = AppConfig.baseUrl;
  final Logger _logger = Logging.getLogger('HomeService');
  final CacheService _cacheService = CacheService();

  // 获取轮播信息
  Future<List<CarouselItem>> fetchHomeSlider(
      {bool forceRefresh = false}) async {
    return _cacheService.getCachedData<List<CarouselItem>>(
      'home_slider',
      () async {
        final jsonRes = await jsonRequest('$baseUrl/home_slider');
        if (jsonRes['code'] == 0) {
          List<dynamic> data = jsonRes['data'];
          return data.map((item) => CarouselItem.fromJson(item)).toList();
        } else {
          _logger.severe('Failed to load slider items: ${jsonRes['msg']}');
          throw Exception('Failed to load slider items');
        }
      },
      cacheDuration: const Duration(hours: 1),
      forceRefresh: forceRefresh,
    );
  }

  // 获取菜单
  Future<List<MenuItem>> fetchHomeMenu({bool forceRefresh = false}) async {
    return _cacheService.getCachedData<List<MenuItem>>(
      'home_menu',
      () async {
        final jsonRes = await jsonRequest('$baseUrl/home_menu');
        if (jsonRes['code'] == 0 && jsonRes['data'] is List) {
          List<dynamic> data = jsonRes['data'];
          return data.map((item) => MenuItem.fromJson(item)).toList();
        } else {
          _logger.severe('Failed to load menu items: ${jsonRes['msg']}');
          throw Exception('Failed to load menu items');
        }
      },
      cacheDuration: const Duration(hours: 2),
      forceRefresh: forceRefresh,
    );
  }

  // 获取最新文章
  Future<List<DailyRecommendation>> fetchDailyRecommendations({
    bool forceRefresh = false,
  }) async {
    try {
      return await _cacheService.getCachedData<List<DailyRecommendation>>(
        'daily_recommendations',
        _fetchFromApi,
        cacheDuration: const Duration(minutes: 30),
        forceRefresh: forceRefresh,
      );
    } catch (e) {
      _logger.severe('Error in fetchDailyRecommendations: $e');
      rethrow;
    }
  }

  // 直接从缓存获取数据，如果没有缓存则返回空列表
  Future<List<DailyRecommendation>> getCachedRecommendations() async {
    try {
      final cachedData = await _cacheService.getFromCache<List<DailyRecommendation>>(
        'daily_recommendations',
      );
      return cachedData ?? [];
    } catch (e) {
      _logger.warning('Error getting cached recommendations: $e');
      return [];
    }
  }

  Future<List<DailyRecommendation>> _fetchFromApi() async {
    try {
      final jsonRes = await jsonRequest(
        '$baseUrl/home_newposts',
        maxRetries: 2,
      );

      if (jsonRes['code'] == 0 && jsonRes['data'] is List) {
        List<dynamic> data = jsonRes['data'];
        return data.map((item) => DailyRecommendation.fromJson(item)).toList();
      } else {
        _logger.severe('Invalid response format: $jsonRes');
        throw Exception('服务器返回的数据格式不正确');
      }
    } on TimeoutException {
      _logger.severe('API request timed out');
      throw Exception('网络请求超时，请检查您的网络连接');
    } on FormatException catch (e) {
      _logger.severe('Error parsing JSON: $e');
      throw Exception('解析服务器返回的数据时出错');
    } catch (e) {
      _logger.severe('Error during API call: $e');
      throw Exception('获取数据时发生错误: $e');
    }
  }
}
