class AppConfig {
  // API基础配置
  static const String _baseUrl = 'https://max.estockcafe.com';
  static const String baseUrl = '$_baseUrl/app/v1';
  static const String apiBaseUrl = 'https://api.estockcafe.com';

  // WebSocket配置
  static const String wsUrl = 'wss://ws.estockcafe.com/v1';
  static const String chatWsUrl = 'wss://ws.estockcafe.com/v1/ws';
  static const String chatWsOrigin = 'https://ws.estockcafe.com';

  // 应用配置
  static const String appName = 'com.estockcafe.app';
  static const String appVersion = '1.0.0';
  static const String vLicUrl =
      'https://license.vod2.myqcloud.com/license/v2/**********_1/v_cube.license';
  static const String vLicKey = '29ecf150422bc837500d9dbfd18c3fea';
  static const int appId = **********;

  // 聊天配置
  static const int chatMessageLimit = 50;
  static const Duration chatReconnectInterval = Duration(seconds: 5);
  static const int chatMaxReconnectAttempts = 5;
  static const Duration chatHeartbeatInterval = Duration(seconds: 30);
  static const int chatMaxHeartbeatFailures = 3;
  static const int chatImageMaxSize = 5 * 1024 * 1024; // 5MB
  static const Duration chatMessageCooldown = Duration(seconds: 10);

  // FCM配置
  static const bool enableFcm = true;

  // 超时设置
  static const int connectionTimeoutSeconds = 30;
  static const int requestTimeoutSeconds = 15;

  // 重试配置
  static const int maxRetryAttempts = 3;
  static const int retryDelayMilliseconds = 1000;

  // 更新配置
  static const String updateUrl = '$baseUrl/check_update';
  static const String appStoreID = 'your-app-store-id';
}

// 环境配置
const String environment = 'development';
