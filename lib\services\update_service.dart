import 'dart:io';
import 'package:estockcafe/config/config.dart';
import 'package:estockcafe/theme.dart';
import 'package:estockcafe/utils/functions.dart';
import 'package:estockcafe/utils/logging.dart';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class UpdateService {
  final updateUrl = AppConfig.updateUrl;
  final appStoreID = AppConfig.appStoreID;

  final Logger _logger = Logging.getLogger('UpdateService');

  // Clean HTML tags from update description
  String _cleanHtmlTags(String text) {
    // 将<br>标签替换为换行符
    String cleaned =
        text.replaceAll(RegExp(r'<br\s*\/?>', caseSensitive: false), '\n');

    // 移除多余的空行，将连续的换行符压缩为两个换行符
    cleaned = cleaned.replaceAll(RegExp(r'\n\s*\n'), '\n\n');

    // 移除行首行尾的空白字符
    cleaned = cleaned
        .split('\n')
        .map((line) => line.trim())
        .where((line) => line.isNotEmpty)
        .join('\n');

    return cleaned.trim();
  }

  Future<void> checkForUpdates([BuildContext? context]) async {
    if (context == null) {
      _logger.warning('context为空，无法检查更新');
      return;
    }

    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version;

      _logger.info('Checking for updates. Current version: $currentVersion, '
          'Platform: ${Platform.operatingSystem}');
      final checkUrl = '$updateUrl?version=$currentVersion';
      _logger.info('Check URL: $checkUrl');

      final response = await jsonRequest(checkUrl);
      _logger.info('服务器响应: $response');

      if (response['code'] == 0) {
        final updateInfo = response['data'];
        _logger.info('Update info: $updateInfo');

        final updateAvailable = updateInfo['need_update'] as bool;
        _logger.info('需要更新: $updateAvailable');

        if (updateAvailable) {
          final newVersion = updateInfo['version'] as String;
          final apkUrl = updateInfo['url'] as String;
          final desc = (updateInfo['desc'] as String?)?.isNotEmpty == true
              ? _cleanHtmlTags(updateInfo['desc'] as String)
              : '新版本可用，建议立即更新。';

          _logger.info('准备显示更新对话框，版本: $newVersion');

          if (context.mounted) {
            await _showUpdateDialog(context, newVersion, apkUrl, desc);
          } else {
            _logger.warning('context已失效，无法显示更新对话框');
          }
        } else {
          _logger.info('当前已是最新版本');
        }
      } else {
        _logger.warning('Update check failed. Response: $response');
      }
    } catch (e, stackTrace) {
      _logger.severe('更新检查出错: $e\n$stackTrace');
    }
  }

  Future<void> _showUpdateDialog(BuildContext context, String newVersion,
      String apkUrl, String desc) async {
    try {
      await showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.white,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(4)),
            ),
            title: const Text('有新版本可用', style: TextStyle(fontSize: 16)),
            content: SingleChildScrollView(
              child: Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: desc,
                      style: const TextStyle(
                        height: 1.6, // 减小行高，使文字更紧凑
                        fontSize: 14,
                      ),
                    ),
                    TextSpan(
                      text: '\n\n新版本: $newVersion', // 改为单换行符
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        height: 1.6, // 保持一致的行高
                      ),
                    ),
                  ],
                ),
              ),
            ),
            actions: <Widget>[
              TextButton(
                child: const Text('取消', style: TextStyle(color: Colors.grey)),
                onPressed: () => Navigator.of(context).pop(),
              ),
              TextButton(
                child: const Text('更新',
                    style: TextStyle(color: AppColors.primaryBlue)),
                onPressed: () async {
                  Navigator.of(context).pop();
                  if (Platform.isAndroid) {
                    await _openDownloadLink(apkUrl);
                  } else if (Platform.isIOS) {
                    await _openAppStore();
                  }
                },
              ),
            ],
          );
        },
      );
    } catch (e, stackTrace) {
      _logger.severe('显示更新对话框时出错: $e\n$stackTrace');
    }
  }

  Future<void> _openDownloadLink(String apkUrl) async {
    try {
      final url = Uri.parse(apkUrl);
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        throw '无法打开下载地址： $url';
      }
    } catch (e, stackTrace) {
      _logger.severe('打开下载链接时出错: $e\n$stackTrace');
    }
  }

  Future<void> _openAppStore() async {
    try {
      final url = Uri.parse('https://apps.apple.com/app/id$appStoreID');
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        throw '无法打开 App Store： $url';
      }
    } catch (e, stackTrace) {
      _logger.severe('打开 App Store 时出错: $e\n$stackTrace');
    }
  }
}
