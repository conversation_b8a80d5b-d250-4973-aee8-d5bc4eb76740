import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'dart:math';
import 'package:logging/logging.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import '../models/chat_message.dart';

class ChatDatabaseService {
  static final Map<String, ChatDatabaseService> _instances = {};

  factory ChatDatabaseService({String? userId, String? channelName}) {
    // 使用userId和channelName组合作为key
    final key = '${channelName ?? 'default'}_${userId ?? 'guest'}';
    if (!_instances.containsKey(key)) {
      _instances[key] = ChatDatabaseService._internal(
          userId: userId ?? 'guest', channelName: channelName ?? 'default');
    }
    return _instances[key]!;
  }

  final Logger _logger = Logger('ChatDatabaseService');
  Database? _database;
  bool _initialized = false;
  final String _userId;
  final String _channelName;

  ChatDatabaseService._internal(
      {required String userId, required String channelName})
      : _userId = userId,
        _channelName = channelName {
    _logger.info('为用户 $_userId 在聊天室 $_channelName 创建数据库服务实例');
  }

  Future<Database> get database async {
    if (_database != null) return _database!;

    try {
      _database = await _initDatabase();
      _initialized = true;
      return _database!;
    } catch (e, stackTrace) {
      _logger.severe('获取数据库实例失败', e, stackTrace);
      rethrow;
    }
  }

  bool get isInitialized => _initialized;

  Future<Database> _initDatabase() async {
    _logger.info('初始化用户 $_userId 在聊天室 $_channelName 的数据库');
    try {
      // 使用聊天室名称和用户ID作为数据库名
      String path =
          join(await getDatabasesPath(), 'chat_${_channelName}_${_userId}.db');
      _logger.info('数据库路径: $path');

      final dbFolder = Directory(dirname(path));
      if (!await dbFolder.exists()) {
        await dbFolder.create(recursive: true);
        _logger.info('创建数据库目录: ${dbFolder.path}');
      }

      return await openDatabase(
        path,
        version: 2,
        onCreate: _createDb,
        onUpgrade: _upgradeDb,
        onOpen: (db) {
          _logger.info('用户 $_userId 在聊天室 $_channelName 的数据库已打开');
        },
      );
    } catch (e, stackTrace) {
      _logger.severe('初始化数据库失败', e, stackTrace);
      rethrow;
    }
  }

  Future<void> _createDb(Database db, int version) async {
    _logger.info('创建聊天消息数据库表');
    await db.execute('''
      CREATE TABLE messages(
        id TEXT PRIMARY KEY,
        userId TEXT,
        userName TEXT,
        content TEXT,
        timestamp TEXT,
        type TEXT,
        isAdmin INTEGER,
        toUserId TEXT,
        toUserName TEXT,
        avatar TEXT,
        metadata TEXT,
        msgId TEXT,
        createTime INTEGER,
        reply TEXT,
        imgs TEXT,
        userAvatar TEXT,
        read INTEGER,
        revoked INTEGER,
        channelName TEXT,
        sortTime INTEGER,
        notification_shown INTEGER DEFAULT 0,
        is_unread INTEGER DEFAULT 1
      )
    ''');

    await db.execute(
        'CREATE INDEX idx_channel_sort ON messages(channelName, sortTime)');
    await db.execute('CREATE INDEX idx_message_id ON messages(msgId)');
    await db.execute('CREATE INDEX idx_unread ON messages(is_unread)');
  }

  Future<void> _upgradeDb(Database db, int oldVersion, int newVersion) async {
    _logger.info('升级数据库从 $oldVersion 到 $newVersion');

    if (oldVersion < 2) {
      await db.execute(
          'ALTER TABLE messages ADD COLUMN notification_shown INTEGER DEFAULT 0');
      await db.execute(
          'ALTER TABLE messages ADD COLUMN is_unread INTEGER DEFAULT 1');
      await db.execute('CREATE INDEX idx_unread ON messages(is_unread)');
      _logger.info('数据库升级完成：添加了通知状态和未读状态字段');
    }
  }

  Future<void> saveMessage(ChatMessageData message, String channelName) async {
    // 记录完整的消息内容
    _logger.fine('尝试保存消息: ${message.toJson()}');

    // 过滤系统类型消息
    if (message.type == 'app_state' ||
        message.type == 'ping' ||
        message.type == 'pong' ||
        message.type == 'connect' ||
        message.type == 'delivery_strategy' ||
        message.type == 'user_state' ||
        message.type == 'register_fcm' ||
        message.type == 'background_keepalive' ||
        message.type == 'fetch_offline_messages' ||
        message.type == 'disconnect') {
      _logger.info('跳过保存系统类型消息: ${message.type}，消息ID: ${message.id}');
      return;
    }

    // 过滤空内容的系统消息
    if ((message.type == 'system' ||
            message.type == 'notification' ||
            message.isSystem) &&
        message.content.trim().isEmpty) {
      _logger.info('跳过保存空内容系统消息: ${message.toJson()}');
      return;
    }

    try {
      final db = await database;

      // 检查消息是否已存在 - 增强检查逻辑，同时检查id和msgId
      // 安全处理查询条件，避免null值导致的SQLite错误
      String whereClause;
      List<dynamic> whereArgs;

      if (message.msgId != null && message.msgId!.isNotEmpty) {
        whereClause = '(id = ? OR msgId = ?) AND channelName = ?';
        whereArgs = [message.id, message.msgId, channelName];
      } else {
        whereClause = 'id = ? AND channelName = ?';
        whereArgs = [message.id, channelName];
      }

      final existingMessages = await db.query(
        'messages',
        where: whereClause,
        whereArgs: whereArgs,
      );

      if (existingMessages.isNotEmpty) {
        _logger.fine('消息已存在，跳过保存: ${message.id}，msgId: ${message.msgId}');

        // 更新消息状态（如果需要）
        if (message.revoked) {
          // 同样安全处理更新条件
          String updateWhereClause;
          List<dynamic> updateWhereArgs;

          if (message.msgId != null && message.msgId!.isNotEmpty) {
            updateWhereClause = '(id = ? OR msgId = ?) AND channelName = ?';
            updateWhereArgs = [message.id, message.msgId, channelName];
          } else {
            updateWhereClause = 'id = ? AND channelName = ?';
            updateWhereArgs = [message.id, channelName];
          }

          await db.update(
            'messages',
            {'revoked': 1},
            where: updateWhereClause,
            whereArgs: updateWhereArgs,
          );
          _logger.fine('更新消息状态为已撤回: ${message.id}');
        }
        return;
      }

      // 保存新消息
      try {
        await db.insert(
          'messages',
          {
            'id': message.id,
            'userId': message.userId,
            'userName': message.userName,
            'userAvatar': message.userAvatar,
            'content': message.content,
            'type': message.type,
            'createTime': message.createTime,
            'timestamp': message.timestamp.toIso8601String(),
            'revoked': message.revoked ? 1 : 0,
            'channelName': channelName,
            'read': 0, // 默认为未读
            'metadata':
                message.metadata != null ? jsonEncode(message.metadata) : null,
            'msgId': message.msgId,
            'isAdmin': message.isAdmin ? 1 : 0,
            'toUserId': message.toUserId,
            'toUserName': message.toUserName,
            'avatar': message.avatar,
            'reply': message.reply != null ? jsonEncode(message.reply) : null,
            'imgs': message.imgs != null ? message.imgs.toString() : null,
            'sortTime': message.createTime ??
                (message.timestamp.millisecondsSinceEpoch ~/ 1000),
            'notification_shown': 0,
            'is_unread': 1,
          },
        );
        _logger.fine('消息保存成功: ${message.id}');
      } catch (e) {
        // 如果是主键冲突错误，尝试更新消息而不是插入
        if (e.toString().contains('UNIQUE constraint failed') ||
            e.toString().contains('SQLITE_CONSTRAINT')) {
          _logger.info('消息已存在，尝试更新: ${message.id}');
          try {
            // 安全处理更新条件，避免null值导致的SQLite错误
            String updateWhereClause;
            List<dynamic> updateWhereArgs;

            if (message.msgId != null && message.msgId!.isNotEmpty) {
              updateWhereClause = 'id = ? OR msgId = ?';
              updateWhereArgs = [message.id, message.msgId];
            } else {
              updateWhereClause = 'id = ?';
              updateWhereArgs = [message.id];
            }

            // 更新消息内容
            await db.update(
              'messages',
              {
                'content': message.content,
                'type': message.type,
                'revoked': message.revoked ? 1 : 0,
                'metadata': message.metadata != null
                    ? jsonEncode(message.metadata)
                    : null,
              },
              where: updateWhereClause,
              whereArgs: updateWhereArgs,
            );
            _logger.fine('消息更新成功: ${message.id}');
          } catch (updateError) {
            _logger.severe('更新消息失败: $updateError');
          }
        } else {
          _logger.severe('保存消息失败: $e');
        }
      }
    } catch (e) {
      _logger.severe('保存消息失败: $e');
    }
  }

  Future<void> saveMessages(
      List<ChatMessageData> messages, String channelName) async {
    try {
      final db = await database;

      await db.transaction((txn) async {
        for (var message in messages) {
          // 安全处理查询条件，避免null值导致的SQLite错误
          String whereClause;
          List<dynamic> whereArgs;

          if (message.msgId != null && message.msgId!.isNotEmpty) {
            whereClause = 'id = ? OR msgId = ?';
            whereArgs = [message.id, message.msgId];
          } else {
            whereClause = 'id = ?';
            whereArgs = [message.id];
          }

          final existing = await txn.query(
            'messages',
            where: whereClause,
            whereArgs: whereArgs,
          );

          if (existing.isNotEmpty) {
            continue; // 跳过已存在的消息
          }

          final sortTime = message.createTime ??
              (message.timestamp.millisecondsSinceEpoch ~/ 1000);

          final Map<String, dynamic> messageMap = {
            'id': message.id,
            'userId': message.userId,
            'userName': message.userName,
            'content': message.content,
            'timestamp': message.timestamp.toIso8601String(),
            'type': message.type,
            'isAdmin': message.isAdmin ? 1 : 0,
            'toUserId': message.toUserId,
            'toUserName': message.toUserName,
            'avatar': message.avatar,
            'metadata':
                message.metadata != null ? message.metadata.toString() : null,
            'msgId': message.msgId,
            'createTime': message.createTime,
            'reply': message.reply != null ? message.reply.toString() : null,
            'imgs': message.imgs != null ? message.imgs.toString() : null,
            'userAvatar': message.userAvatar,
            'read': message.read ? 1 : 0,
            'revoked': message.revoked ? 1 : 0,
            'channelName': channelName,
            'sortTime': sortTime,
            'notification_shown': 0,
            'is_unread': 1,
          };

          await txn.insert('messages', messageMap);
        }
      });

      _logger.info('批量保存了 ${messages.length} 条消息到数据库');
    } catch (e) {
      _logger.severe('批量保存消息到数据库失败: $e');
    }
  }

  ChatMessageData _mapToMessage(Map<String, dynamic> map) {
    // 处理timestamp
    DateTime timestamp;
    try {
      if (map['timestamp'] is int) {
        // 如果是毫秒时间戳
        timestamp = DateTime.fromMillisecondsSinceEpoch(map['timestamp']);
      } else if (map['timestamp'] is String) {
        // 尝试解析ISO8601字符串
        try {
          timestamp = DateTime.parse(map['timestamp']);
        } catch (e) {
          // 如果解析失败，尝试将其解析为整数时间戳
          int? timeInt = int.tryParse(map['timestamp']);
          if (timeInt != null) {
            timestamp = DateTime.fromMillisecondsSinceEpoch(timeInt);
          } else {
            // 如果都失败，使用当前时间
            timestamp = DateTime.now();
          }
        }
      } else {
        // 默认使用当前时间
        timestamp = DateTime.now();
      }
    } catch (e) {
      // 如果出现任何错误，使用当前时间
      timestamp = DateTime.now();
    }

    // 处理元数据
    Map<String, dynamic>? metadata;
    if (map['metadata'] != null && map['metadata'] is String) {
      try {
        metadata = jsonDecode(map['metadata']);
      } catch (e) {
        _logger.warning('解析metadata失败: $e');
      }
    }

    // 处理回复消息
    ChatMessageData? reply;
    if (map['reply'] != null && map['reply'] is String) {
      try {
        final replyJson = jsonDecode(map['reply']);
        if (replyJson is Map<String, dynamic>) {
          reply = ChatMessageData.fromJson(replyJson);
        }
      } catch (e) {
        _logger.warning('解析reply失败: $e');
      }
    }

    return ChatMessageData(
      id: map['id'],
      userId: map['userId'],
      userName: map['userName'],
      content: map['content'],
      timestamp: timestamp,
      type: map['type'],
      isAdmin: map['isAdmin'] == 1,
      toUserId: map['toUserId'],
      toUserName: map['toUserName'],
      avatar: map['avatar'],
      metadata: metadata,
      msgId: map['msgId'],
      createTime: map['createTime'],
      reply: reply,
      imgs: map['imgs'] != null ? _parseStringList(map['imgs']) : null,
      userAvatar: map['userAvatar'],
      read: map['read'] == 1,
      revoked: map['revoked'] == 1,
    );
  }

  List<String>? _parseStringList(String? listStr) {
    if (listStr == null) return null;

    try {
      String cleaned = listStr.replaceAll('[', '').replaceAll(']', '');
      List<String> items = cleaned.split(', ');
      return items.map((item) => item.trim()).toList();
    } catch (e) {
      _logger.warning('解析字符串列表失败: $e');
      return null;
    }
  }

  Future<void> updateMessageStatus(String messageId,
      {bool? read, bool? revoked, bool? notificationShown}) async {
    try {
      final db = await database;

      Map<String, dynamic> updates = {};
      if (read != null) {
        updates['read'] = read ? 1 : 0;
        if (read) {
          updates['is_unread'] = 0;
        }
      }
      if (revoked != null) updates['revoked'] = revoked ? 1 : 0;
      if (notificationShown != null)
        updates['notification_shown'] = notificationShown ? 1 : 0;

      if (updates.isEmpty) return;

      await db.update(
        'messages',
        updates,
        where: 'id = ? OR msgId = ?',
        whereArgs: [messageId, messageId],
      );

      _logger.fine('已更新消息状态: $messageId');
    } catch (e) {
      _logger.severe('更新消息状态失败: $e');
    }
  }

  Future<List<ChatMessageData>> getUnreadMessages({int limit = 200}) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        'messages',
        where: 'is_unread = ?',
        whereArgs: [1],
        orderBy: 'sortTime DESC',
        limit: limit,
      );

      _logger.info('从数据库获取了 ${maps.length} 条未读消息');

      if (maps.isEmpty) {
        _logger.info('数据库中没有找到未读消息');
        return [];
      }

      return _convertMapsToMessages(maps);
    } catch (e) {
      _logger.severe('获取未读消息失败', e);
      return [];
    }
  }

  Future<int> getUnreadCount(String channelName) async {
    try {
      final db = await database;
      final result = await db.rawQuery(
          'SELECT COUNT(*) as count FROM messages WHERE is_unread = 1 AND channelName = ?',
          [channelName]);

      final count = Sqflite.firstIntValue(result) ?? 0;
      _logger.info('频道 $channelName 有 $count 条未读消息');

      return count;
    } catch (e) {
      _logger.severe('获取未读消息数量失败', e);
      return 0;
    }
  }

  Future<int> getTotalUnreadCount() async {
    try {
      final db = await database;
      final result = await db.rawQuery(
          'SELECT COUNT(*) as count FROM messages WHERE is_unread = 1');

      final count = Sqflite.firstIntValue(result) ?? 0;
      _logger.info('总共有 $count 条未读消息');

      return count;
    } catch (e) {
      _logger.severe('获取未读消息总数失败', e);
      return 0;
    }
  }

  Future<void> markMessageAsRead(String messageId) async {
    try {
      final db = await database;
      await db.update(
        'messages',
        {'is_unread': 0, 'read': 1},
        where: 'id = ?',
        whereArgs: [messageId],
      );
      _logger.info('已将消息 $messageId 标记为已读');
    } catch (e) {
      _logger.severe('标记消息已读失败', e);
    }
  }

  Future<void> markMessagesAsRead(List<String> messageIds) async {
    if (messageIds.isEmpty) return;

    try {
      final db = await database;
      final batch = db.batch();

      for (final id in messageIds) {
        batch.update(
          'messages',
          {'is_unread': 0, 'read': 1},
          where: 'id = ?',
          whereArgs: [id],
        );
      }

      await batch.commit(noResult: true);
      _logger.info('已批量标记 ${messageIds.length} 条消息为已读');
    } catch (e) {
      _logger.severe('批量标记消息已读失败', e);
    }
  }

  Future<void> markAllMessagesAsRead(String channelName) async {
    try {
      final db = await database;
      await db.update(
        'messages',
        {'is_unread': 0, 'read': 1},
        where: 'channelName = ? AND is_unread = ?',
        whereArgs: [channelName, 1],
      );
      _logger.info('已将频道 $channelName 的所有消息标记为已读');
    } catch (e) {
      _logger.severe('标记所有消息已读失败', e);
    }
  }

  Future<List<ChatMessageData>> getMessagesWithoutNotification(
      {int limit = 50}) async {
    try {
      final db = await database;

      final List<Map<String, dynamic>> maps = await db.query(
        'messages',
        where: 'notification_shown = ? AND is_unread = ?',
        whereArgs: [0, 1],
        orderBy: 'sortTime DESC',
        limit: limit,
      );

      if (maps.isEmpty) {
        _logger.info('数据库中没有找到未显示通知的消息');
        return [];
      }

      return _convertMapsToMessages(maps);
    } catch (e) {
      _logger.severe('获取未显示通知的消息失败: $e');
      return [];
    }
  }

  Future<void> markNotificationShown(String messageId) async {
    try {
      final db = await database;

      await db.update(
        'messages',
        {'notification_shown': 1},
        where: 'id = ? OR msgId = ?',
        whereArgs: [messageId, messageId],
      );

      _logger.fine('已标记消息通知已显示: $messageId');
    } catch (e) {
      _logger.severe('标记消息通知已显示失败: $e');
    }
  }

  Future<int> cleanupOldMessages({int daysToKeep = 30}) async {
    // 不再删除旧消息，只记录日志并返回0
    _logger.info('清理旧消息功能已禁用，保留所有历史消息');
    return 0;
  }

  Future<int> getUnreadMessageCount() async {
    try {
      final db = await database;

      final result = await db.rawQuery(
          'SELECT COUNT(*) as count FROM messages WHERE is_unread = 1');

      final count = Sqflite.firstIntValue(result) ?? 0;
      return count;
    } catch (e) {
      _logger.severe('获取未读消息数量失败: $e');
      return 0;
    }
  }

  Future<void> clearMessages(String channelName) async {
    try {
      final db = await database;

      await db.delete(
        'messages',
        where: 'channelName = ?',
        whereArgs: [channelName],
      );

      _logger.info('已清除频道 $channelName 的所有消息');
    } catch (e) {
      _logger.severe('清除消息失败: $e');
    }
  }

  Future<void> clearAllMessages() async {
    try {
      final db = await database;

      await db.delete('messages');

      _logger.info('已清除所有频道的消息');
    } catch (e) {
      _logger.severe('清除所有消息失败: $e');
    }
  }

  // 清除所有未读状态（将所有消息标记为已读）
  Future<void> clearAllUnreadMessages() async {
    try {
      final db = await database;

      await db.update(
        'messages',
        {'is_unread': 0, 'read': 1},
        where: 'is_unread = ?',
        whereArgs: [1],
      );

      _logger.info('已将所有消息标记为已读');
    } catch (e) {
      _logger.severe('标记所有消息为已读失败: $e');
    }
  }

  Future<List<ChatMessageData>> getLatestMessages(String channelName,
      {int limit = 50}) async {
    try {
      final db = await database;

      final List<Map<String, dynamic>> maps = await db.query(
        'messages',
        where: 'channelName = ?',
        whereArgs: [channelName],
        orderBy: 'sortTime DESC', // 降序排列，最新的消息在前
        limit: limit,
      );

      if (maps.isEmpty) {
        _logger.info('数据库中没有找到频道 $channelName 的消息');
        return [];
      }

      List<ChatMessageData> messages = _convertMapsToMessages(maps);
      // 反转列表，使消息按时间升序排列
      return messages.reversed.toList();
    } catch (e) {
      _logger.severe('获取最新消息失败: $e');
      return [];
    }
  }

  Future<List<ChatMessageData>> getMessagesBefore(
      String channelName, int timestamp,
      {int limit = 50}) async {
    try {
      final db = await database;

      final List<Map<String, dynamic>> maps = await db.query(
        'messages',
        where: 'channelName = ? AND sortTime < ?',
        whereArgs: [channelName, timestamp],
        orderBy: 'sortTime DESC', // 降序排列，最新的消息在前
        limit: limit,
      );

      if (maps.isEmpty) {
        _logger.info('数据库中没有找到 $timestamp 之前的消息');
        return [];
      }

      List<ChatMessageData> messages = _convertMapsToMessages(maps);
      // 反转列表，使消息按时间升序排列
      return messages.reversed.toList();
    } catch (e) {
      _logger.severe('获取历史消息失败: $e');
      return [];
    }
  }

  Future<int> getLatestMessageTimestamp(String channelName) async {
    try {
      final db = await database;
      final result = await db.query(
        'messages',
        columns: ['MAX(sortTime) as latest_time'],
        where: 'channelName = ?',
        whereArgs: [channelName],
      );

      if (result.isNotEmpty && result.first['latest_time'] != null) {
        return result.first['latest_time'] as int;
      }
      return 0;
    } catch (e) {
      _logger.severe('获取最新消息时间戳失败', e);
      return 0;
    }
  }

  List<ChatMessageData> _convertMapsToMessages(
      List<Map<String, dynamic>> maps) {
    List<ChatMessageData> messages = [];
    for (var map in maps) {
      try {
        messages.add(_mapToMessage(map));
      } catch (e) {
        _logger.warning('转换消息数据失败: $e');
      }
    }
    return messages;
  }

  Future<void> close() async {
    if (_database != null && _database!.isOpen) {
      await _database!.close();
      _database = null;
      _initialized = false;
      _logger.info('用户 $_userId 在聊天室 $_channelName 的数据库已关闭');
    }
  }

  static Future<void> closeAll() async {
    for (var service in _instances.values) {
      await service.close();
    }
    _instances.clear();
    Logger('ChatDatabaseService').info('所有用户的数据库连接已关闭');
  }

  String get userId => _userId;
  String get channelName => _channelName;

  Future<void> saveOfflineMessage(
      ChatMessageData message, String channelName) async {
    _logger.info('保存离线消息: ${message.id} - 频道: $channelName');

    try {
      // 过滤系统类型消息
      if (message.type == 'app_state' ||
          message.type == 'ping' ||
          message.type == 'pong' ||
          message.type == 'connect' ||
          message.type == 'delivery_strategy' ||
          message.type == 'user_state' ||
          message.type == 'register_fcm' ||
          message.type == 'background_keepalive' ||
          message.type == 'fetch_offline_messages' ||
          message.type == 'disconnect') {
        _logger.info('跳过保存系统类型离线消息: ${message.type}');
        return;
      }

      // 过滤空内容的系统消息
      if ((message.type == 'system' ||
              message.type == 'notification' ||
              message.isSystem) &&
          message.content.trim().isEmpty) {
        _logger.info('跳过保存空内容系统离线消息');
        return;
      }

      // 保存到数据库
      await saveMessage(message, channelName);

      // 标记为未读和需要通知
      final db = await database;
      await db.update(
        'messages',
        {
          'is_unread': 1,
          'notification_shown': 0,
        },
        where: 'id = ?',
        whereArgs: [message.id],
      );

      _logger.info('离线消息已保存: ${message.id}');
    } catch (e) {
      _logger.severe('保存离线消息失败: $e');
    }
  }

  // 获取离线消息
  Future<List<ChatMessageData>> getOfflineMessages(String channelName) async {
    _logger.info('获取频道 $channelName 的离线消息');

    try {
      final db = await database;
      final results = await db.query(
        'messages',
        where: 'channelName = ? AND is_unread = 1',
        whereArgs: [channelName],
        orderBy: 'sortTime DESC',
      );

      final messages =
          results.map((map) => ChatMessageData.fromDbMap(map)).toList();
      _logger.info('获取到 ${messages.length} 条离线消息');
      return messages;
    } catch (e) {
      _logger.severe('获取离线消息失败: $e');
      return [];
    }
  }

  // 清除已处理的离线消息
  Future<void> clearOfflineMessages(String channelName) async {
    _logger.info('清除频道 $channelName 的离线消息');

    try {
      final db = await database;
      await db.update(
        'messages',
        {'is_unread': 0, 'notification_shown': 1},
        where: 'channelName = ? AND is_unread = 1',
        whereArgs: [channelName],
      );
      _logger.info('离线消息已标记为已读');
    } catch (e) {
      _logger.severe('清除离线消息失败: $e');
    }
  }

  // 根据消息ID获取消息
  Future<ChatMessageData?> getMessageById(String? messageId) async {
    if (messageId == null || messageId.isEmpty) {
      _logger.warning('消息ID为空，无法获取消息');
      return null;
    }

    final db = await database;
    try {
      final List<Map<String, dynamic>> maps = await db.query(
        'messages',
        where: 'id = ? OR msgId = ?',
        whereArgs: [messageId, messageId],
        limit: 1,
      );

      if (maps.isEmpty) {
        _logger.fine('未找到ID为 $messageId 的消息');
        return null;
      }

      // 使用_mapToMessage方法将数据库记录转换为ChatMessageData对象
      return _mapToMessage(maps.first);
    } catch (e) {
      _logger.warning('根据ID获取消息失败: $e');
      return null;
    }
  }

  // 清理空系统消息和系统类型消息
  Future<int> cleanEmptySystemMessages(String channelName) async {
    try {
      final db = await database;

      // 详细记录清理操作开始
      _logger.info('开始清理频道 $channelName 的空系统消息和系统类型消息');

      // 1. 首先删除所有类型为app_state、ping、pong等系统类型消息
      int systemTypeCount = await db.delete(
        'messages',
        where: 'type IN (?, ?, ?, ?, ?, ?, ?, ?, ?) AND channelName = ?',
        whereArgs: [
          'app_state',
          'ping',
          'pong',
          'connect',
          'delivery_strategy',
          'user_state',
          'register_fcm',
          'background_keepalive',
          'fetch_offline_messages',
          channelName
        ],
      );

      _logger.info('已删除 $systemTypeCount 条系统类型消息');

      // 2. 然后删除空内容的系统和通知类型消息
      int emptySystemCount = await db.delete(
        'messages',
        where:
            '(type IN (?, ?) OR (type = ? AND content LIKE ?)) AND content = "" AND channelName = ?',
        whereArgs: ['system', 'notification', 'msg', '%系统消息%', channelName],
      );

      _logger.info('已删除 $emptySystemCount 条空内容系统消息');

      // 3. 打印所有可能的空系统消息，用于调试
      final suspiciousMessages = await db.query(
        'messages',
        where: '(content = "" OR content IS NULL) AND channelName = ?',
        whereArgs: [channelName],
      );

      if (suspiciousMessages.isNotEmpty) {
        _logger.info('数据库中仍有 ${suspiciousMessages.length} 条可疑的空内容消息，示例:');
        for (int i = 0; i < min(5, suspiciousMessages.length); i++) {
          _logger.info('可疑消息 #$i: ${jsonEncode(suspiciousMessages[i])}');
        }
      }

      int totalRemoved = systemTypeCount + emptySystemCount;
      _logger.info('已从数据库中清理 $totalRemoved 条系统消息和空内容消息');
      return totalRemoved;
    } catch (e) {
      _logger.severe('清理系统消息失败: $e');
      return 0;
    }
  }
}
