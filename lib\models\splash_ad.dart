class SplashAd {
  final String imageUrl;
  final String linkUrl;
  final int displayDuration;  // 展示时长（秒）
  final bool skipable;        // 是否可跳过
  final bool isLocalImage;    // 是否为本地图片
  final double? width;        // 图片宽度
  final double? height;       // 图片高度
  final String? imageRatio;   // 图片比例建议 (例如: "16:9", "4:3", "1:1")

  SplashAd({
    required this.imageUrl,
    required this.linkUrl,
    this.displayDuration = 3,
    this.skipable = true,
    this.isLocalImage = false,
    this.width,
    this.height,
    this.imageRatio,
  });

  factory SplashAd.fromJson(Map<String, dynamic> json) {
    return SplashAd(
      imageUrl: json['imageUrl'] as String,
      linkUrl: json['linkUrl'] as String,
      displayDuration: json['displayDuration'] as int? ?? 3,
      skipable: json['skipable'] as bool? ?? true,
      isLocalImage: json['isLocalImage'] as bool? ?? false,
      width: json['width'] as double?,
      height: json['height'] as double?,
      imageRatio: json['imageRatio'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'imageUrl': imageUrl,
      'linkUrl': linkUrl,
      'displayDuration': displayDuration,
      'skipable': skipable,
      'isLocalImage': isLocalImage,
      'width': width,
      'height': height,
      'imageRatio': imageRatio,
    };
  }

  // 获取推荐的图片尺寸信息
  static String get recommendedImageSpecs => '''
推荐的启动页广告图片规格：
1. 分辨率：
   - 1242 x 2688 像素 (适用于 iPhone 12 Pro Max 等)
   - 1242 x 2208 像素 (适用于 iPhone 8 Plus 等)
   - 1080 x 1920 像素 (适用于大多数 Android 设备)

2. 比例：
   - 主要比例：9:19.5
   - 兼容比例：9:16

3. 格式要求：
   - 格式：PNG 或 JPG
   - 最大文件大小：2MB
   - 背景色：建议使用纯色背景

4. 设计建议：
   - 重要内容放在中央安全区域
   - 考虑不同机型的刘海屏和圆角
   - 避免在顶部和底部放置重要内容
''';
}
