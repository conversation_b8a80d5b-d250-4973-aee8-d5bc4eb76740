import 'package:flutter/material.dart';
import 'package:estockcafe/widgets/bottom_navbar.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:estockcafe/theme.dart';
import 'dart:convert';
import 'package:estockcafe/services/post_service.dart';
import 'package:estockcafe/models/post.dart';
import 'package:estockcafe/widgets/post/list_view.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  _SearchPageState createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final TextEditingController _searchController = TextEditingController();
  List<String> _searchHistory = [];
  bool _isLoading = true;
  static const String _historyKey = 'search_history';
  final FocusNode _focusNode = FocusNode();
  
  // 添加搜索结果相关变量
  final PostService _postService = PostService();
  List<PostItem> _searchResults = [];
  bool _isSearching = false;
  String _searchErrorMessage = '';
  int _currentPage = 1;
  bool _hasMoreResults = true;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadSearchHistory();
    // 自动聚焦到搜索框
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(_focusNode);
    });
    
    // 添加滚动监听器，用于加载更多结果
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  // 滚动监听器，用于检测是否需要加载更多结果
  void _scrollListener() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200 &&
        !_isSearching &&
        _hasMoreResults) {
      _loadMoreResults();
    }
  }

  // 加载搜索历史
  Future<void> _loadSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_historyKey);
      
      setState(() {
        if (historyJson != null) {
          final List<dynamic> decoded = jsonDecode(historyJson);
          _searchHistory = decoded.map((item) => item.toString()).toList();
        }
        _isLoading = false;
      });
    } catch (e) {
      print('加载搜索历史失败: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 保存搜索历史
  Future<void> _saveSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_historyKey, jsonEncode(_searchHistory));
    } catch (e) {
      print('保存搜索历史失败: $e');
    }
  }

  // 清空搜索历史
  Future<void> _clearSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_historyKey);
      setState(() {
        _searchHistory = [];
      });
    } catch (e) {
      print('清空搜索历史失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('搜索'),
        elevation: 0,
      ),
      body: Column(
        children: [
          // 美化的搜索框
          Container(
            margin: const EdgeInsets.fromLTRB(16, 8, 16, 16), // 记录搜索框的左右边距，以便后续对齐
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.2),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              focusNode: _focusNode,
              decoration: InputDecoration(
                hintText: '输入搜索关键词',
                hintStyle: TextStyle(color: Colors.grey[400]),
                prefixIcon: Icon(Icons.search, color: AppColors.primaryBlue),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.clear, color: Colors.grey),
                  onPressed: () {
                    _searchController.clear();
                  },
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 15),
              ),
              onSubmitted: (_) => _performSearch(),
              textInputAction: TextInputAction.search,
            ),
          ),
          
          // 搜索历史标题和清空按钮
          if (_searchHistory.isNotEmpty && _searchResults.isEmpty && !_isSearching)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    '搜索历史',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: const Text('确认清空'),
                          content: const Text('确定要清空所有搜索历史吗？'),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context),
                              child: const Text('取消'),
                            ),
                            TextButton(
                              onPressed: () {
                                _clearSearchHistory();
                                Navigator.pop(context);
                              },
                              child: const Text('确定'),
                            ),
                          ],
                        ),
                      );
                    },
                    child: const Text('清空历史'),
                  ),
                ],
              ),
            ),
          
          // 搜索历史列表或搜索结果
          Expanded(
            child: _isLoading
                ? const Center(
                    child: SpinKitPulsingGrid(
                      size: 20,
                      color: Colors.grey,
                    ),
                  )
                : (_isSearching && _searchResults.isEmpty) // 仅在初次搜索且没有结果时显示全屏加载
                    ? const Center(
                        child: SpinKitPulsingGrid(
                          size: 20,
                          color: Colors.grey,
                        ),
                      )
                    : (_searchController.text.isNotEmpty && _searchResults.isEmpty && !_isSearching)
                        ? Center(
                            child: Text(
                              '未找到相关结果',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          )
                        : _searchResults.isNotEmpty
                            ? _buildSearchResults()
                            : _searchHistory.isEmpty
                                ? Center(
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.history,
                                          size: 64,
                                          color: Colors.grey[300],
                                        ),
                                        const SizedBox(height: 16),
                                        Text(
                                          '暂无搜索历史',
                                          style: TextStyle(
                                            color: Colors.grey[500],
                                            fontSize: 16,
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                : ListView.builder(
                                    itemCount: _searchHistory.length,
                                    itemBuilder: (context, index) {
                                      return ListTile(
                                        leading: const Icon(Icons.history, color: Colors.grey),
                                        title: Text(_searchHistory[index]),
                                        trailing: IconButton(
                                          icon: const Icon(Icons.close, color: Colors.grey),
                                          onPressed: () {
                                            setState(() {
                                              _searchHistory.removeAt(index);
                                              _saveSearchHistory();
                                            });
                                          },
                                        ),
                                        onTap: () {
                                          _searchController.text = _searchHistory[index];
                                          _performSearch();
                                        },
                                      );
                                    },
                                  ),
          ),
          
          // 显示搜索错误信息
          if (_searchErrorMessage.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                _searchErrorMessage,
                style: const TextStyle(color: Colors.red),
                textAlign: TextAlign.center,
              ),
            ),
        ],
      ),
      bottomNavigationBar: const BottomNavBar(currentIndex: -1),
    );
  }

  // 构建搜索结果列表
  Widget _buildSearchResults() {
    if (_searchResults.isEmpty) {
      return Center(
        child: Text(
          '未找到相关结果',
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    // 使用PostListView组件替代自定义ListView
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: PostListView(
        articles: _searchResults,
        hasNext: _hasMoreResults,
        scrollController: _scrollController,
        cacheWidth: 300,
        cacheHeight: 200,
      ),
    );
  }

  // 加载更多搜索结果
  Future<void> _loadMoreResults() async {
    if (!_hasMoreResults || _isSearching) return;
    
    setState(() {
      _isSearching = true;
      _searchErrorMessage = ''; // 清空错误信息
    });
    
    try {
      final searchTerm = _searchController.text.trim();
      final nextPage = _currentPage + 1;
      final results = await _postService.searchPosts(searchTerm, page: nextPage);
      
      if (mounted) {
        setState(() {
          if (results.isEmpty) {
            _hasMoreResults = false;
          } else {
            _searchResults.addAll(results); // 使用addAll直接添加到列表末尾
            _currentPage = nextPage;
          }
          _isSearching = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSearching = false;
          _searchErrorMessage = '加载更多结果失败';
        });
      }
    }
  }

  void _performSearch() {
    final searchTerm = _searchController.text.trim();
    if (searchTerm.isNotEmpty) {
      setState(() {
        // 移除已存在的相同搜索词（如果有）
        _searchHistory.remove(searchTerm);
        // 将新搜索词添加到列表开头
        _searchHistory.insert(0, searchTerm);
        // 限制历史记录数量为10条
        if (_searchHistory.length > 10) {
          _searchHistory = _searchHistory.sublist(0, 10);
        }
        
        // 重置搜索状态
        _isSearching = true;
        _searchResults = [];
        _searchErrorMessage = '';
        _currentPage = 1;
        _hasMoreResults = true;
      });
      
      _saveSearchHistory();
      
      // 执行搜索
      _postService.searchPosts(searchTerm).then((results) {
        setState(() {
          _searchResults = results;
          _isSearching = false;
          if (results.isEmpty) {
            _searchErrorMessage = '';  // 不设置错误信息，使用专门的空结果UI显示
          }
        });
      }).catchError((error) {
        setState(() {
          _isSearching = false;
          _searchErrorMessage = '搜索失败: ${error.toString()}';
        });
      });
    }
  }
}
