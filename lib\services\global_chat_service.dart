import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:flutter/services.dart';
import 'package:path/path.dart' as path;
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

import '../models/chat_message.dart';
import '../models/message_type.dart'; // 添加导入
import '../utils/web_socket.dart';
import '../utils/event_bus.dart';
import '../utils/app_state.dart';
import '../utils/api_client.dart';
import '../config/config.dart';
import 'chat_notification_service.dart';
import 'notification_service.dart';
import 'chat_database_service.dart';
import 'system_notification_service.dart';
import 'app_state_manager.dart';
import 'fcm_service.dart';

class GlobalChatService with ChangeNotifier {
  static final GlobalChatService _instance = GlobalChatService._internal();
  factory GlobalChatService() => _instance;
  GlobalChatService._internal()
      : _connectionController = StreamController<bool>.broadcast() {
    // 初始化消息控制器
    _messageController = StreamController<ChatMessageData>.broadcast();
    _chatNotificationService = ChatNotificationService();
    // 设置日志级别为INFO，以便查看更多连接信息
    _logger.level = Level.INFO;
  }

  final Logger _logger = Logger('GlobalChatService');

  // 添加一个静态集合来跟踪已处理过的通知ID
  static final Set<String> _processedNotificationIds = <String>{};

  final List<ChatMessageData> _messages = [];
  WebSocketUtility? _webSocket;
  String? _channelName;
  String? _userId;
  String? _userName;
  bool _isConnected = false;
  late ChatNotificationService _chatNotificationService;
  ChatDatabaseService? _chatDatabaseService; // 添加数据库服务实例
  String? _lastError;
  bool _isDatabaseAvailable = true; // 添加数据库可用标志
  bool _initialized = false; // 添加初始化标志
  Timer? _reconnectTimer; // 添加重连定时器
  int _reconnectAttempts = 0; // 添加重连尝试次数
  Map<String, dynamic>? _chatroomInfo; // 添加聊天室信息
  bool _hasPermission = false; // 添加用户权限标志
  String get wsUrl => AppConfig.chatWsUrl; // WebSocket URL

  // 消息流控制器
  late StreamController<ChatMessageData> _messageController;
  late StreamController<bool> _connectionController;

  // 消息流
  Stream<ChatMessageData> get messageStream => _messageController.stream;
  Stream<bool> get connectionStream => _connectionController.stream;

  List<ChatMessageData> get messages => List.unmodifiable(_messages);
  bool get isConnected => _isConnected;
  String? get channelName => _channelName;
  String? get userId => _userId;
  String? get userName => _userName;
  String? get lastError => _lastError;
  bool get initialized => _initialized;
  bool get hasPermission => _hasPermission;

  @override
  void dispose() {
    _messageController.close();
    _connectionController.close();
    _reconnectTimer?.cancel();

    // 关闭数据库连接
    if (_chatDatabaseService != null) {
      _chatDatabaseService!.close();
    }

    super.dispose();
  }

  // 初始化方法
  Future<void> initialize() async {
    if (_initialized) {
      _logger.info('全局聊天服务已经初始化，跳过重复初始化');
      return;
    }

    try {
      // 注册应用生命周期监听
      SystemChannels.lifecycle.setMessageHandler(_handleAppLifecycleChange);

      // 订阅事件总线中的应用生命周期变化事件
      EventBus()
          .on(EventType.appLifecycleChange, _handleAppLifecycleChangeFromBus);

      _initialized = true;
      _logger.info('全局聊天服务初始化成功');
    } catch (e) {
      _logger.severe('全局聊天服务初始化失败: $e');
    }
  }

  // 处理应用生命周期变化
  Future<String?> _handleAppLifecycleChange(String? state) async {
    _logger.info('应用生命周期状态变化: $state');

    // 如果WebSocket实例存在，调用其生命周期处理方法
    if (_webSocket != null && state != null) {
      try {
        await _webSocket!.handleAppLifecycleChange(state);
      } catch (e) {
        _logger.warning('处理WebSocket生命周期变化时出错: $e');
      }
    }

    return state;
  }

  // 处理应用生命周期变化（从事件总线）
  void _handleAppLifecycleChangeFromBus(dynamic state) {
    if (state is String) {
      _logger.info('从事件总线接收到应用生命周期状态变化: $state');

      // 如果WebSocket实例存在，调用其生命周期处理方法
      if (_webSocket != null) {
        try {
          _webSocket!.handleAppLifecycleChange(state);
        } catch (e) {
          _logger.warning('处理WebSocket生命周期变化时出错: $e');
        }
      }

      // 应用恢复前台时，无论WebSocket状态如何，都尝试同步消息
      if (state == 'resumed') {
        // 清理可能存在的空系统消息
        cleanEmptySystemMessages();

        if (_hasPermission && _userId != null && _channelName != null) {
          _logger.info('应用恢复前台，开始同步消息');

          // 无论WebSocket连接状态如何，都主动同步消息
          _syncMessagesOnForeground();

          // 如果未连接，尝试重新连接WebSocket
          if (!_isConnected && _webSocket == null) {
            _logger.info('WebSocket未连接，尝试重新连接');
            _connectWithRetry();
          }
        }
      }
    }
  }

  // 添加前台恢复时同步消息的方法
  Future<void> _syncMessagesOnForeground() async {
    try {
      _logger.info('应用回到前台，开始同步离线消息');

      // 检查FCM服务是否可用
      final FCMService fcmService = FCMService();
      final bool isFcmAvailable = fcmService.isFcmAvailable;
      _logger.info('FCM可用性: $isFcmAvailable');

      // 1. 从数据库加载最近保存的消息
      List<ChatMessageData> dbMessages = [];

      if (_isDatabaseAvailable &&
          _chatDatabaseService != null &&
          _channelName != null &&
          _userId != null &&
          _userId != 'guest' &&
          _userId!.isNotEmpty) {
        dbMessages = await _chatDatabaseService!
            .getLatestMessages(_channelName!, limit: 50);
        _logger.info('从数据库加载了 ${dbMessages.length} 条最近消息');
      }

      // 2. 找出最近一条消息的时间，或者使用6小时前的时间
      DateTime syncFromTime;

      if (dbMessages.isNotEmpty) {
        // 找出最近一条消息的时间
        int? latestTimestamp = 0;

        for (var msg in dbMessages) {
          int msgTime =
              msg.createTime ?? (msg.timestamp.millisecondsSinceEpoch ~/ 1000);
          if (msgTime > latestTimestamp!) {
            latestTimestamp = msgTime;
          }
        }

        // 从最近一条消息的时间开始同步
        syncFromTime =
            DateTime.fromMillisecondsSinceEpoch(latestTimestamp! * 1000);
        _logger.info('从最近一条消息的时间开始同步: $syncFromTime');
      } else {
        // 如果没有最近消息，使用6小时前的时间
        syncFromTime = DateTime.now().subtract(const Duration(hours: 6));
        _logger.info('没有最近消息，从6小时前开始同步: $syncFromTime');
      }

      // 3. 从数据库加载离线消息
      List<ChatMessageData> offlineMessages = [];

      if (_isDatabaseAvailable &&
          _chatDatabaseService != null &&
          _channelName != null &&
          _userId != null &&
          _userId != 'guest' &&
          _userId!.isNotEmpty) {
        offlineMessages =
            await _chatDatabaseService!.getOfflineMessages(_channelName!);
        _logger.info('从数据库加载了 ${offlineMessages.length} 条离线消息');

        // 使用统一处理函数处理每条离线消息
        for (var message in offlineMessages) {
          // 将消息转换为Map以便处理
          Map<String, dynamic> messageMap = message.toJson();
          await processMessage(messageMap, MessageSource.OFFLINE_PULL);
        }

        // 清除已处理的离线消息
        if (offlineMessages.isNotEmpty) {
          await _chatDatabaseService!.clearOfflineMessages(_channelName!);
          _logger.info('已清除处理完的离线消息');
        }
      }

      // 4. 如果已连接WebSocket，请求历史消息
      if (_isConnected && _webSocket != null) {
        _logger.info('WebSocket已连接，请求历史消息');
        await requestHistoryMessages(
          beforeTime: syncFromTime.millisecondsSinceEpoch ~/ 1000,
          limit: 50,
        );
      } else {
        _logger.info('WebSocket未连接，跳过请求历史消息');
      }

      _logger.info('消息同步完成');
    } catch (e) {
      _logger.severe('同步消息失败: $e');
    }
  }

  // 初始化聊天数据库服务
  Future<void> _initChatDatabaseService() async {
    // 如果用户ID为空或guest，跳过数据库初始化
    if (_userId == null || _userId == 'guest' || _userId!.isEmpty) {
      _logger.info('用户未登录，跳过数据库初始化');
      _isDatabaseAvailable = false;
      return;
    }

    // 如果频道名称为空，跳过数据库初始化
    if (_channelName == null || _channelName!.isEmpty) {
      _logger.info('频道名为空，跳过数据库初始化');
      _isDatabaseAvailable = false;
      return;
    }

    // 如果数据库已经初始化，且用户ID和频道一致，则直接返回
    if (_isDatabaseAvailable &&
        _chatDatabaseService != null &&
        _chatDatabaseService!.userId == _userId &&
        _chatDatabaseService!.channelName == _channelName) {
      _logger.info('聊天数据库服务已经初始化，跳过重复初始化');
      return;
    }

    try {
      // 确保SQLite已初始化
      await _initSqlite();

      // 使用当前用户ID和频道名初始化数据库服务
      _chatDatabaseService = ChatDatabaseService(
        userId: _userId,
        channelName: _channelName,
      );

      // 初始化数据库连接
      await _chatDatabaseService!.database;
      _isDatabaseAvailable = true;
      _logger.info('用户 $_userId 在聊天室 $_channelName 的数据库服务初始化成功');
    } catch (e, stackTrace) {
      _logger.severe('聊天数据库服务初始化失败', e, stackTrace);
      _logger.warning('应用将继续运行，但本地消息存储功能将不可用');
      // 标记数据库不可用，后续操作将跳过数据库
      _isDatabaseAvailable = false;
    }
  }

  // 初始化SQLite
  Future<void> _initSqlite() async {
    final logger = Logger('SQLiteInit');

    try {
      // 针对不同平台进行初始化
      if (Platform.isWindows || Platform.isLinux) {
        // 在Windows和Linux上使用FFI
        logger.info('在Windows/Linux平台上初始化SQLite FFI');
        sqfliteFfiInit();
        databaseFactory = databaseFactoryFfi;
      } else if (Platform.isAndroid || Platform.isIOS) {
        // 在移动平台上，sqflite默认已经初始化好了
        logger.info('在移动平台上使用默认SQLite实现');
      } else {
        logger.warning('未知平台，SQLite可能无法正常工作');
      }

      // 确保数据库目录存在
      final databasesPath = await getDatabasesPath();
      logger.info('数据库路径: $databasesPath');

      // 测试数据库连接
      final testDbPath = path.join(databasesPath, 'test_connection.db');
      final db = await openDatabase(
        testDbPath,
        version: 1,
        onCreate: (db, version) async {
          await db.execute('CREATE TABLE Test (id INTEGER PRIMARY KEY)');
        },
      );

      await db.close();
      logger.info('SQLite连接测试成功');
    } catch (e, stackTrace) {
      logger.severe('SQLite初始化失败', e, stackTrace);
      // 在移动应用中，我们不希望因为数据库初始化失败而导致整个应用崩溃
      // 所以这里只记录错误，不重新抛出
      logger.warning('应用将继续运行，但本地存储功能可能不可用');
      throw e; // 重新抛出异常，让调用者知道初始化失败
    }
  }

  // 设置用户信息，但不自动连接
  Future<void> setUserInfo(
      String userId, String userName, Map<String, dynamic>? chatroomInfo,
      [bool hasPermission = true]) async {
    bool userChanged = _userId != userId;
    bool permissionChanged = _hasPermission != hasPermission;
    bool chatroomChanged = _chatroomInfo != chatroomInfo;

    // 检查用户是否已登录
    bool isLoggedIn = userId != null && userId.isNotEmpty && userId != 'guest';
    if (!isLoggedIn) {
      _logger.info('用户未登录，不进行初始化和连接');
      _hasPermission = false;

      // 确保未登录用户没有聊天相关的服务
      if (_isConnected) {
        await disconnect();
      }
      if (_chatDatabaseService != null) {
        await _chatDatabaseService!.close();
        _chatDatabaseService = null;
      }

      if (userChanged || permissionChanged) {
        notifyListeners();
      }
      return;
    }

    _userId = userId;
    _userName = userName;
    _chatroomInfo = chatroomInfo;
    _hasPermission = hasPermission && isLoggedIn; // 如果用户未登录，没有权限

    // 获取聊天室频道
    String? channelName;
    if (chatroomInfo != null && chatroomInfo['channel'] != null) {
      channelName = chatroomInfo['channel'].toString();
    }

    // 更新频道名称
    if (channelName != null && channelName.isNotEmpty) {
      _channelName = channelName;
    }

    // 如果用户ID变更或聊天室变更，且用户有聊天权限，才初始化数据库服务
    if ((userChanged || chatroomChanged || _channelName != null) &&
        _hasPermission &&
        isLoggedIn) {
      _logger.info('用户信息或聊天室变更且有聊天权限，初始化数据库服务');
      await _initChatDatabaseService();
    } else if (userChanged && (!_hasPermission || !isLoggedIn)) {
      _logger.info('用户ID已变更但无聊天权限或未登录，跳过数据库初始化');
    }

    // 设置当前用户ID到通知服务
    _chatNotificationService.currentUserId = userId;

    // 如果用户没有权限，断开连接
    if ((!_hasPermission || !isLoggedIn) && _isConnected) {
      _logger.info('用户无权限访问聊天室或未登录，断开连接');
      await disconnect();
    }

    // 如果用户信息或权限发生变化，通知监听器
    if (userChanged || permissionChanged || chatroomChanged) {
      notifyListeners();
    }

    // 确保服务已初始化
    if (!_initialized) {
      await initialize();
    }
  }

  // 带重试的连接方法
  Future<void> _connectWithRetry() async {
    if (_isConnected || !_hasPermission) {
      if (_isConnected) {
        _logger.info('聊天服务已连接，无需重新连接');
      } else if (!_hasPermission) {
        _logger.warning('用户无权限访问聊天室，取消重连');
      }
      return;
    }

    if (_userId == null || _channelName == null) {
      _logger.warning('无法重新连接：缺少频道名称或用户ID');
      return;
    }

    try {
      _logger.info('尝试连接聊天服务: 第${_reconnectAttempts + 1}次尝试');

      await connect(
        channelName: _channelName!,
        userId: _userId!,
        userName: _userName ?? 'User',
        chatroomInfo: _chatroomInfo,
      );

      // 连接成功后同步离线消息
      if (_chatDatabaseService != null) {
        try {
          final offlineMessages =
              await _chatDatabaseService!.getOfflineMessages(_channelName!);
          _logger.info('获取到${offlineMessages.length}条离线消息');

          // 处理离线消息
          for (var msg in offlineMessages) {
            _processMessage(msg);
          }

          // 清除已处理的离线消息
          await _chatDatabaseService!.clearOfflineMessages(_channelName!);
          _logger.info('已清除处理完的离线消息');
        } catch (e) {
          _logger.warning('处理离线消息失败: $e');
        }
      }

      _reconnectAttempts = 0;
      _logger.info('连接成功，已同步离线消息');
    } catch (e) {
      _logger.severe('连接聊天服务失败: $e');
      _scheduleReconnect();
    }
  }

  // 安排重连
  void _scheduleReconnect() {
    // 如果用户没有权限，不进行重连
    if (!_hasPermission) {
      _logger.info('用户无权限访问聊天室，取消重连');
      return;
    }

    // 取消现有的重连定时器
    _reconnectTimer?.cancel();

    // 计算重连延迟（指数退避策略）
    final delay = min(30, pow(2, _reconnectAttempts).toInt()) * 1000;
    _reconnectAttempts++;

    _logger.info('安排重连，延迟: ${delay}ms');

    // 创建新的重连定时器
    _reconnectTimer = Timer(Duration(milliseconds: delay), () {
      _reconnectTimer = null;
      _connectWithRetry();
    });
  }

  Future<void> connect({
    required String channelName,
    required String userId,
    required String userName,
    String? toUserId,
    String? toUserName,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? chatroomInfo,
  }) async {
    // 检查是否需要切换频道
    if (_isConnected && _channelName != channelName) {
      _logger.info('🔄 需要切换频道: 当前频道=${_channelName}, 目标频道=${channelName}');
      _logger.info('🔄 先断开当前连接，然后连接到新频道');
      await disconnect();
      // 等待断开完成
      await Future.delayed(const Duration(milliseconds: 500));
    } else if (_isConnected && _channelName == channelName) {
      _logger.info('聊天服务已连接到相同频道，无需重新连接');
      return;
    }

    // 检查用户是否登录
    bool isLoggedIn = userId != null && userId.isNotEmpty && userId != 'guest';
    if (!isLoggedIn) {
      _logger.warning('用户未登录，无法连接聊天室');
      return;
    }

    // 检查用户权限
    if (!_hasPermission) {
      _logger.warning('用户无权限访问聊天室，拒绝连接');
      return;
    }

    // 优先使用传入的参数，而不是依赖类属性
    String actualUserId = userId;
    String actualUserName = userName;
    String actualChannel = channelName;

    // 只有当没有明确传递channelName时，才使用chatroomInfo中的默认频道
    // 这样LiveChatRoom可以明确指定要连接的直播频道
    if (channelName.isEmpty &&
        chatroomInfo != null &&
        chatroomInfo['channel'] != null &&
        chatroomInfo['channel'].toString().isNotEmpty) {
      actualChannel = chatroomInfo['channel'];
      _logger.info('使用默认chatroom.channel: $actualChannel');
    } else {
      _logger.info('使用传入的频道名: $actualChannel');
    }

    // 更新类属性
    _channelName = actualChannel;
    _userId = actualUserId;
    _userName = actualUserName;
    _chatroomInfo = chatroomInfo;

    _logger.info('准备连接聊天服务，用户ID: $actualUserId, 频道: $actualChannel');

    // 设置当前用户ID到通知服务
    _chatNotificationService.currentUserId = actualUserId;
    _logger.info('已设置ChatNotificationService的用户ID: $actualUserId');

    // 初始化数据库服务（如果尚未初始化）
    if (_chatDatabaseService == null ||
        _chatDatabaseService!.userId != actualUserId ||
        _chatDatabaseService!.channelName != actualChannel) {
      _logger.info('初始化聊天数据库服务...');
      await _initChatDatabaseService();
    }

    // 构建WebSocket URL，确保格式正确与后端API一致
    String baseUrl = AppConfig.chatWsUrl;

    // 移除协议前缀并保存域名
    String domain = baseUrl;
    if (domain.startsWith('https://')) {
      domain = domain.substring(8);
    } else if (domain.startsWith('http://')) {
      domain = domain.substring(7);
    } else if (domain.startsWith('wss://')) {
      domain = domain.substring(6);
    } else if (domain.startsWith('ws://')) {
      domain = domain.substring(5);
    }

    // 清理域名，移除任何尾部斜杠
    if (domain.endsWith('/')) {
      domain = domain.substring(0, domain.length - 1);
    }

    // 如果域名包含路径，只保留域名部分
    if (domain.contains('/')) {
      domain = domain.substring(0, domain.indexOf('/'));
    }

    // 构建WebSocket URL
    final clientType = Platform.isAndroid ? 'android' : 'ios';
    String wsUrl =
        'wss://$domain/v1/ws/$actualChannel/$actualUserId/$clientType';

    _logger.info('最终构建的WebSocket URL: $wsUrl');

    // 从chatroomInfo中获取token
    String? chatToken = null;
    if (chatroomInfo != null && chatroomInfo['token'] != null) {
      chatToken = chatroomInfo['token'].toString();
      _logger.info('从chatroomInfo中提取token: ${chatToken!.substring(0, 8)}...');
    }

    _webSocket = WebSocketUtility(
      url: wsUrl,
      headers: headers,
      onOpen: _onConnectionOpen,
      onMessage: _onMessageReceived,
      onClose: _onConnectionClosed,
      onError: _onConnectionError,
    );

    await _webSocket?.connect(
      channelName: actualChannel,
      userId: actualUserId,
      token: chatToken,
    );
  }

  // 断开连接
  Future<void> disconnect() async {
    _logger.fine('正在断开聊天服务器连接');

    if (_webSocket != null) {
      try {
        // 关闭WebSocket连接
        await _webSocket!.close();
        _webSocket = null;
      } catch (e) {
        _logger.severe('断开聊天服务器连接时发生错误: $e');
      }
    }

    // 清空消息列表和聊天室信息
    _messages.clear();
    _isConnected = false;
    _channelName = null; // 确保清除频道名称
    _connectionController.add(false);
    notifyListeners();
  }

  // 手动重连方法
  Future<bool> manualReconnect() async {
    _logger.info('手动重新连接聊天服务');

    // 如果用户没有权限，不允许连接
    if (!_hasPermission) {
      _logger.warning('用户无权限访问聊天室，拒绝重连');
      return false;
    }

    // 如果已经连接，直接返回成功
    if (_isConnected && _webSocket != null) {
      _logger.info('聊天服务已连接，无需重新连接');
      _connectionController.add(true);
      notifyListeners();
      return true;
    }

    // 如果没有必要的连接信息，返回失败
    if (_userId == null || _channelName == null) {
      _logger.warning('无法重新连接：缺少频道名称或用户ID');
      return false;
    }

    // 重置重连尝试次数
    _reconnectAttempts = 0;

    try {
      // 调用WebSocketUtility的manualReconnect方法
      if (_webSocket != null) {
        await _webSocket!.manualReconnect();
      } else {
        // 如果WebSocket实例不存在，重新创建并连接
        await connect(
          channelName: _channelName!,
          userId: _userId!,
          userName: _userName ?? 'User',
          chatroomInfo: _chatroomInfo,
        );
      }
      return _isConnected;
    } catch (e) {
      _logger.severe('手动重新连接失败: $e');
      return false;
    }
  }

  void _onConnectionOpen() {
    _logger.fine('聊天服务器连接成功');
    _isConnected = true;
    _connectionController.add(true);
    _reconnectAttempts = 0; // 重置重连尝试次数

    // 清理可能存在的空系统消息
    cleanEmptySystemMessages();

    // 从本地数据库加载消息
    _loadMessagesFromDatabase().then((loadedMessages) {
      _logger.info('从数据库加载了 ${loadedMessages.length} 条消息');

      // 不再自动获取离线消息，而是依赖前台同步机制
      _logger.info('已禁用连接时自动获取历史消息功能，将依赖前台同步机制获取最新消息');
    });

    notifyListeners();
  }

  void _onConnectionClosed() {
    _logger.fine('聊天服务器连接关闭');
    _isConnected = false;
    _connectionController.add(false);
    notifyListeners();

    // 不再自动重连，需要用户手动触发重连
    // if (_userId != null && _channelName != null) {
    //   _scheduleReconnect();
    // }
  }

  void _onConnectionError(dynamic error) {
    _logger.severe('聊天服务器连接错误: $error');
    _isConnected = false;
    _lastError = error.toString();
    _connectionController.add(false);
    notifyListeners();

    // 不再自动重连，需要用户手动触发重连
    // if (_userId != null && _channelName != null) {
    //   _scheduleReconnect();
    // }
  }

  // 处理WebSocket消息
  void _onMessageReceived(dynamic data) {
    try {
      Map<String, dynamic> jsonData;
      if (data is String) {
        jsonData = json.decode(data);
      } else if (data is Map<String, dynamic>) {
        jsonData = data;
      } else {
        _logger.severe('收到无效的消息格式: ${data.runtimeType}');
        return;
      }

      // 详细记录收到的所有消息，便于调试
      _logger.info('收到WebSocket消息: ${jsonEncode(jsonData)}');

      // 添加更详细的日志记录
      String? messageType = jsonData['type'] as String?;
      if (messageType == 'connected' && jsonData['msgs'] is List) {
        final List<dynamic> msgs = jsonData['msgs'];
        _logger.info('收到connected类型消息，包含 ${msgs.length} 条历史消息');
        if (msgs.isNotEmpty) {
          _logger.info(
              '第一条历史消息类型: ${msgs.first['type']}, ID: ${msgs.first['msg_id'] ?? "无ID"}');
        }
      }

      // 首先检查消息类型
      if (messageType == null) {
        _logger.warning('消息缺少type字段，忽略');
        return;
      }

      // 过滤系统类型消息
      if (messageType == 'app_state' ||
          messageType == 'ping' ||
          messageType == 'pong' ||
          messageType == 'connect' ||
          messageType == 'delivery_strategy' ||
          messageType == 'user_state' ||
          messageType == 'register_fcm' ||
          messageType == 'background_keepalive' ||
          messageType == 'fetch_offline_messages' ||
          messageType == 'disconnect') {
        _logger.info(
            'GlobalChatService: 跳过处理系统类型消息: $messageType，完整内容: ${jsonEncode(jsonData)}');
        return;
      }

      // 过滤空内容系统消息
      final String content = jsonData['content'] as String? ?? '';
      if ((messageType == 'system' || messageType == 'notification') &&
          content.trim().isEmpty) {
        _logger.info(
            'GlobalChatService: 跳过处理空内容系统消息，完整内容: ${jsonEncode(jsonData)}');
        return;
      }

      _logger.fine('处理WebSocket消息: $messageType');

      // 处理connected类型消息（包含历史消息数组）
      if (messageType == 'connected' && jsonData['msgs'] is List) {
        _logger.info('收到connected消息，处理其中的历史消息');
        final List<dynamic> messages = jsonData['msgs'];

        // 使用统一处理函数处理每条历史消息
        for (var msg in messages) {
          if (msg is Map<String, dynamic>) {
            try {
              // 检查消息是否为系统类型消息
              String? msgType = msg['type'] as String?;
              if (msgType == 'app_state' ||
                  msgType == 'ping' ||
                  msgType == 'pong' ||
                  msgType == 'connect' ||
                  msgType == 'delivery_strategy' ||
                  msgType == 'user_state') {
                _logger.fine('历史消息中包含系统类型消息: $msgType，忽略');
                continue;
              }

              // 使用统一处理函数处理历史消息
              processMessage(msg, MessageSource.WEBSOCKET);
            } catch (e) {
              _logger.warning('处理历史消息失败: $e');
            }
          }
        }
        return;
      }

      // 处理历史消息
      if (messageType == 'history' && jsonData['messages'] is List) {
        final List<dynamic> messages = jsonData['messages'];

        // 使用统一处理函数处理每条历史消息
        for (var msg in messages) {
          if (msg is Map<String, dynamic>) {
            try {
              // 检查消息是否为系统类型消息
              String? msgType = msg['type'] as String?;
              if (msgType == 'app_state' ||
                  msgType == 'ping' ||
                  msgType == 'pong' ||
                  msgType == 'connect' ||
                  msgType == 'delivery_strategy' ||
                  msgType == 'user_state') {
                _logger.fine('历史消息中包含系统类型消息: $msgType，忽略');
                continue;
              }

              // 使用统一处理函数处理历史消息
              processMessage(msg, MessageSource.OFFLINE_PULL);
            } catch (e) {
              _logger.warning('处理历史消息失败: $e');
            }
          }
        }
        return;
      } else {
        // 处理单条消息（包括历史消息和实时消息）
        processMessage(jsonData, MessageSource.WEBSOCKET);
      }
    } catch (e, stackTrace) {
      _logger.severe('消息处理失败: $e\n$stackTrace');
    }
  }

  // 对消息进行排序
  void _sortMessages() {
    _messages.sort((a, b) {
      // 首先按照时间戳排序
      final timeA =
          a.createTime ?? (a.timestamp.millisecondsSinceEpoch ~/ 1000);
      final timeB =
          b.createTime ?? (b.timestamp.millisecondsSinceEpoch ~/ 1000);

      int timeCompare = timeB.compareTo(timeA); // 降序排列，新消息在前，旧消息在后

      // 如果时间戳相同，则使用消息ID作为次要排序条件
      if (timeCompare == 0) {
        // 尝试从消息ID提取时间戳部分进行比较
        // 许多消息ID格式为: [时间戳]_[随机数] 或 [前缀][时间戳][随机数]
        String idA = a.msgId ?? a.id;
        String idB = b.msgId ?? b.id;

        // 尝试提取数字部分进行比较
        try {
          // 如果ID包含下划线，可能是时间戳_随机数格式
          if (idA.contains('_') && idB.contains('_')) {
            int timePartA = int.tryParse(idA.split('_')[0]) ?? 0;
            int timePartB = int.tryParse(idB.split('_')[0]) ?? 0;
            if (timePartA > 0 && timePartB > 0) {
              return timePartB.compareTo(timePartA);
            }
          }
        } catch (e) {
          // 忽略解析错误，继续使用ID字符串比较
        }

        // 如果无法提取时间部分或格式不一致，直接比较整个ID字符串
        return idB.compareTo(idA); // 降序排列，保持与时间戳排序一致的顺序
      }

      return timeCompare;
    });
  }

  // 检查消息是否已存在
  bool _messageExists(ChatMessageData message) {
    // 创建消息指纹，用于更精确的去重
    String messageFingerprint =
        '${message.userId}_${message.content}_${message.type}';

    // 优先使用msgId进行检查，这是最可靠的
    if (message.msgId != null && message.msgId!.isNotEmpty) {
      bool existsByMsgId = _messages.any((m) =>
          m.msgId != null && m.msgId!.isNotEmpty && m.msgId == message.msgId);

      if (existsByMsgId) {
        _logger.fine('消息已存在(msgId匹配): ${message.msgId}');
        return true;
      }
    }

    // 然后检查ID
    if (message.id.isNotEmpty) {
      bool existsById =
          _messages.any((m) => m.id.isNotEmpty && m.id == message.id);

      if (existsById) {
        _logger.fine('消息已存在(id匹配): ${message.id}');
        return true;
      }
    }

    // 检查内容+用户ID+时间的组合匹配（精确匹配）
    if (message.content.isNotEmpty && message.userId.isNotEmpty) {
      // 检查完全匹配
      bool existsByContentAndTime = _messages.any((m) =>
          m.userId == message.userId &&
          m.content == message.content &&
          (m.createTime == message.createTime ||
              (m.timestamp.millisecondsSinceEpoch ~/ 1000) ==
                  (message.timestamp.millisecondsSinceEpoch ~/ 1000)));

      if (existsByContentAndTime) {
        int maxLength =
            message.content.length > 20 ? 20 : message.content.length;
        _logger.fine(
            '消息已存在(内容+时间+用户ID匹配): ${message.content.substring(0, maxLength)}...');
        return true;
      }

      // 检查消息指纹匹配（忽略时间戳）- 这对于FCM恢复后的消息特别有用
      // 因为FCM恢复后的消息可能会有相同内容但不同时间戳
      bool existsByFingerprint = _messages.any((m) {
        String existingFingerprint = '${m.userId}_${m.content}_${m.type}';
        return existingFingerprint == messageFingerprint;
      });

      if (existsByFingerprint) {
        _logger.fine('消息已存在(消息指纹匹配): $messageFingerprint');
        return true;
      }

      // 如果消息内容短，尝试更宽松的匹配（只匹配内容和用户ID，不匹配时间）
      // 这可以捕获那些时间戳略有不同但实际上是同一消息的情况
      if (message.content.length < 50) {
        bool existsByContentOnly = _messages.any(
            (m) => m.userId == message.userId && m.content == message.content);

        if (existsByContentOnly) {
          _logger.fine('消息已存在(内容+用户ID匹配，忽略时间): ${message.content}');
          return true;
        }
      }
    }

    // 对于回复消息，检查回复内容是否相同
    if (message.reply != null) {
      bool existsByReplyContent = _messages.any((m) =>
          m.userId == message.userId &&
          m.content == message.content &&
          m.reply != null &&
          m.reply!.content == message.reply!.content);

      if (existsByReplyContent) {
        _logger.fine('消息已存在(回复内容匹配): ${message.content}');
        return true;
      }
    }

    return false;
  }

  // 添加发送消息方法
  Future<void> send({
    required String content,
    String type = 'text',
    String? toUserId,
    String? toUserName,
    ChatMessageData? replyTo,
  }) async {
    if (!_isConnected) {
      _logger.warning('聊天服务器未连接，无法发送消息');
      return;
    }

    final message = {
      'userId': _userId,
      'userName': _userName,
      'channelName': _channelName,
      'content': content,
      'type': type,
      'timestamp': DateTime.now().toIso8601String(),
      'create_time':
          DateTime.now().millisecondsSinceEpoch ~/ 1000, // 添加create_time字段
      'client_type': Platform.isAndroid ? 'android' : 'ios', // 根据平台自动判断客户端类型
    };

    if (toUserId != null && toUserName != null) {
      message['toUserId'] = toUserId;
      message['toUserName'] = toUserName;
    }

    // 添加回复消息处理
    if (replyTo != null) {
      message['reply'] = replyTo.toJson();
      _logger.info('添加回复消息数据: ${replyTo.content}');
    }

    try {
      await _webSocket?.send(jsonEncode(message));

      // 将发送的消息添加到本地列表，但不保存到数据库
      try {
        final chatMessage = ChatMessageData.fromJson(message);
        if (!_messageExists(chatMessage)) {
          _messages.add(chatMessage);

          // 对所有消息进行排序
          _sortMessages();

          _messageController.add(chatMessage);

          // 不再保存到本地数据库，等待服务器返回消息后再保存
          // if (_channelName != null && _isDatabaseAvailable) {
          //   _chatDatabaseService.saveMessage(chatMessage, _channelName!);
          // }

          notifyListeners();
        }
      } catch (e) {
        _logger.warning('处理发送的消息失败: $e');
      }
    } catch (e) {
      _logger.severe('发送聊天消息失败: $e');
      _lastError = e.toString();
    }
  }

  // 添加发送消息方法
  Future<void> sendMessage(Map<String, dynamic> message) async {
    if (!_isConnected) {
      _logger.warning('聊天服务器未连接，无法发送消息');
      _lastError = '聊天服务器未连接';
      return;
    }

    // 确保消息有create_time字段
    if (!message.containsKey('create_time')) {
      message['create_time'] = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    }

    // 添加客户端类型标识
    message['client_type'] = Platform.isAndroid ? 'android' : 'ios';

    // 处理reply字段，确保它是一个对象而不是字符串
    if (message.containsKey('reply')) {
      if (message['reply'] is String) {
        try {
          message['reply'] = jsonDecode(message['reply']);
          _logger.info('成功将reply字段从字符串转换为对象');
        } catch (e) {
          _logger.warning('转换reply字段失败: $e');
        }
      }

      // 确保reply是一个完整的对象，包含所有必要字段
      if (message['reply'] is Map) {
        Map<String, dynamic> replyData =
            Map<String, dynamic>.from(message['reply']);

        // 确保reply中的user_id是字符串类型
        if (replyData.containsKey('user_id') && replyData['user_id'] != null) {
          replyData['user_id'] = replyData['user_id'].toString();
        }

        // 确保reply中包含必要的字段
        if (!replyData.containsKey('id') && replyData.containsKey('msg_id')) {
          replyData['id'] = replyData['msg_id'];
        }

        // 更新reply数据
        message['reply'] = replyData;
        _logger.info('处理回复消息数据: ${replyData['content']}');
      }
    }

    // 处理base64图片
    if (message.containsKey('pics') && message['pics'] is List) {
      List<String> pics = List<String>.from(message['pics']);
      List<String> base64Images = [];

      // 检查是否有base64编码的图片，并去重
      Set<String> uniquePics = <String>{};
      for (String pic in pics) {
        if (pic.startsWith('data:image')) {
          base64Images.add(pic);
        }
        uniquePics.add(pic);
      }

      // 如果有base64图片，记录日志
      if (base64Images.isNotEmpty) {
        _logger.info('发送包含${base64Images.length}张base64图片的消息');

        // 确保pics字段中包含所有图片（包括base64图片），并去重
        message['pics'] = uniquePics.toList();

        // 确保imgs字段与pics字段保持一致
        message['imgs'] = uniquePics.toList();
      }
    }

    // 特殊处理撤回消息
    if (message['type'] == 'revoke') {
      _logger.info('发送撤回消息: ${jsonEncode(message)}');

      // 确保msg_id字段存在且不为空
      if (message['msg_id'] == null || message['msg_id'].toString().isEmpty) {
        _logger.warning('撤回消息缺少msg_id字段');
        _lastError = '撤回消息缺少msg_id字段';
        return;
      }
    }

    // 打印发送的消息内容，便于调试
    _logger.info('发送消息: ${jsonEncode(message)}');

    try {
      await _webSocket?.send(jsonEncode(message));

      // 将发送的消息添加到本地列表，但不保存到数据库
      try {
        // 如果是撤回消息，不添加到本地消息列表
        if (message['type'] != 'revoke') {
          final chatMessage = ChatMessageData.fromJson(message);
          if (!_messageExists(chatMessage)) {
            _messages.add(chatMessage);

            // 对所有消息进行排序
            _sortMessages();

            _messageController.add(chatMessage);

            // 不再保存到本地数据库，等待服务器返回消息后再保存
            // if (_channelName != null && _isDatabaseAvailable) {
            //   _chatDatabaseService.saveMessage(chatMessage, _channelName!);
            // }

            notifyListeners();
          }
        }
      } catch (e) {
        _logger.warning('处理发送的消息失败: $e');
      }
    } catch (e) {
      _logger.severe('发送聊天消息失败: $e');
      _lastError = e.toString();
    }
  }

  // 发送自定义请求
  Future<bool> sendCustomRequest(Map<String, dynamic> params) async {
    if (!_isConnected) {
      _logger.warning('发送请求失败：WebSocket未连接');
      return false;
    }

    try {
      // 添加通用参数
      if (_channelName != null) {
        params['channel'] = _channelName;
      }

      // 发送请求
      try {
        await _webSocket?.send(jsonEncode(params));
        _logger.info('请求发送成功: ${params['action']}');
        return true;
      } catch (e) {
        _logger.warning('请求发送失败: ${params['action']} - $e');
        return false;
      }
    } catch (e, stackTrace) {
      _logger.severe('发送请求时发生错误', e, stackTrace);
      return false;
    }
  }

  // 获取历史消息
  Future<List<ChatMessageData>> getHistoryMessages() async {
    if (!_isConnected) {
      _logger.warning('聊天服务器未连接，尝试从本地数据库获取历史消息');
      return _loadMessagesFromDatabase();
    }

    try {
      // 保存当前消息列表的副本，用于后续合并
      final existingMessages = List<ChatMessageData>.from(_messages);
      _logger.info('当前已有 ${existingMessages.length} 条消息');

      // 创建一个Completer，用于异步返回结果
      final completer = Completer<List<ChatMessageData>>();

      // 创建一个临时的订阅，用于接收历史消息
      late StreamSubscription subscription;
      subscription = _messageController.stream.listen((message) {
        // 不再检查消息类型，任何新消息都视为历史消息加载完成的信号
        _logger.info('收到消息，继续等待更多历史消息');

        // 不立即完成completer，等待超时或所有消息接收完成
      });

      // 设置更长的超时时间
      Future.delayed(const Duration(seconds: 10), () {
        if (!completer.isCompleted) {
          // 不需要重新合并消息，因为_onMessageReceived已经处理了消息合并
          // 只需确保消息按时间排序
          _messages.sort((a, b) {
            final timeA =
                a.createTime ?? (a.timestamp.millisecondsSinceEpoch ~/ 1000);
            final timeB =
                b.createTime ?? (b.timestamp.millisecondsSinceEpoch ~/ 1000);
            return timeB.compareTo(timeA); // 降序排列，新消息在前，旧消息在后
          });

          _logger.info('历史消息加载完成或超时，返回 ${_messages.length} 条消息');
          completer.complete(_messages.toList());
          subscription.cancel();
        }
      });

      // 请求历史消息
      _logger.info('请求历史消息');
      await requestHistoryMessages();

      // 返回Completer的Future
      return completer.future;
    } catch (e) {
      _logger.severe('获取历史消息出错: $e');
      return _loadMessagesFromDatabase(); // 出错时尝试从本地数据库加载消息
    }
  }

  // 从本地数据库加载消息
  Future<List<ChatMessageData>> _loadMessagesFromDatabase() async {
    // 检查用户是否已登录
    if (_userId == null || _userId == 'guest' || _userId!.isEmpty) {
      _logger.warning('用户未登录，无法从数据库加载消息');
      return [];
    }

    // 检查聊天室频道是否存在
    if (_channelName == null || _channelName!.isEmpty) {
      _logger.warning('频道名为空，无法从数据库加载消息');
      return [];
    }

    // 检查数据库服务是否可用
    if (!_isDatabaseAvailable || _chatDatabaseService == null) {
      _logger.warning('数据库服务不可用，无法加载消息');
      return [];
    }

    try {
      _logger.info('从本地数据库加载消息');
      // 限制每次最多加载最新的200条消息
      final messages = await _chatDatabaseService!
          .getLatestMessages(_channelName!, limit: 200);

      if (messages.isNotEmpty) {
        _logger.info('从数据库加载了 ${messages.length} 条消息');

        // 清空当前消息列表
        _messages.clear();

        // 添加从数据库加载的消息
        _messages.addAll(messages);

        // 对消息进行排序，确保顺序正确
        _messages.sort((a, b) {
          final timeA =
              a.createTime ?? (a.timestamp.millisecondsSinceEpoch ~/ 1000);
          final timeB =
              b.createTime ?? (b.timestamp.millisecondsSinceEpoch ~/ 1000);
          return timeB.compareTo(timeA); // 降序排列，新消息在前，旧消息在后
        });

        // 通知监听器
        notifyListeners();

        // 记录最后一条消息的时间，用于增量同步
        if (messages.isNotEmpty) {
          final latestMessage = messages.last;
          final latestTime = latestMessage.createTime ??
              (latestMessage.timestamp.millisecondsSinceEpoch ~/ 1000);
          _logger.info(
              '数据库中最新消息时间: ${DateTime.fromMillisecondsSinceEpoch(latestTime * 1000)}，可用于增量同步');
        }
      } else {
        _logger.info('数据库中没有消息，将请求最新消息');
      }

      return messages;
    } catch (e) {
      _logger.severe('从数据库加载消息失败: $e');
      return [];
    }
  }

  // 清除所有消息
  void clearMessages() {
    _messages.clear();

    // 同时清除数据库中的消息
    if (_channelName != null &&
        _isDatabaseAvailable &&
        _chatDatabaseService != null &&
        _userId != null &&
        _userId != 'guest' &&
        _userId!.isNotEmpty) {
      _chatDatabaseService!.clearMessages(_channelName!);
      _logger.info('已清除频道 $_channelName 的数据库消息');
    } else {
      _logger.info('跳过数据库消息清除，可能是因为用户未登录或数据库不可用');
    }

    notifyListeners();
  }

  // 清除所有频道的消息
  Future<void> clearAllMessages() async {
    _messages.clear();

    // 清除数据库中的所有消息
    if (_isDatabaseAvailable &&
        _chatDatabaseService != null &&
        _userId != null &&
        _userId != 'guest' &&
        _userId!.isNotEmpty) {
      await _chatDatabaseService!.clearAllMessages();
      _logger.info('已清除所有频道的消息');
    } else {
      _logger.info('跳过数据库所有消息清除，可能是因为用户未登录或数据库不可用');
    }

    notifyListeners();
  }

  // 处理通知
  void _processNotification(ChatMessageData message) {
    try {
      // 获取当前用户
      final currentUser = _userId;

      // 如果是当前用户发送的消息，不显示通知
      if (message.userId == currentUser) {
        _logger.info('跳过自己发送的消息通知');
        return;
      }

      // 检查用户是否在当前聊天室 - 需要同时检查应用状态和频道名
      // 获取应用状态管理器
      AppStateManager appStateManager = AppStateManager();

      // 获取应用当前状态和频道
      final appState = appStateManager.currentState;
      final currentChannelName = appStateManager.currentChannelName;

      // 只有当应用状态为CHATROOM_ACTIVE且频道名匹配时，才认为用户在当前聊天室中
      bool isInCurrentChatroom = appState == AppState.CHATROOM_ACTIVE &&
          currentChannelName != null &&
          currentChannelName == message.channelName;

      // 如果用户在当前聊天室，不显示通知
      if (isInCurrentChatroom) {
        _logger.info('用户在当前聊天室(${message.channelName})，不显示通知');
        return;
      }

      // 检查是否是系统消息，并且已经处理过
      if (message.isSystem) {
        // 使用消息ID和内容的组合作为唯一标识符
        String notificationId = '${message.id}_${message.content}';

        // 如果已经处理过该消息，跳过
        if (_processedNotificationIds.contains(notificationId)) {
          _logger.info('跳过重复的系统消息通知: ${message.content}');
          return;
        }

        // 添加到已处理集合中
        _processedNotificationIds.add(notificationId);

        // 限制集合大小，避免内存泄漏
        if (_processedNotificationIds.length > 100) {
          _processedNotificationIds.remove(_processedNotificationIds.first);
        }
      }

      // 判断消息是否与用户相关
      final isRelevant =
          _chatNotificationService.isMessageRelevantToUser(message);

      // 根据设置决定是否显示通知
      if (isRelevant || !_chatNotificationService.onlyShowRelevantMessages) {
        _chatNotificationService.addMessage(message, currentUser: null);

        try {
          // 使用系统通知服务处理消息
          final notificationService = SystemNotificationService();
          notificationService.showChatNotification(message);
          _logger.info('已发送系统通知: ${message.userName} - ${message.content}');
        } catch (e, stackTrace) {
          _logger.warning('显示系统通知失败: $e');
          _logger.warning('错误堆栈: $stackTrace');
        }
      }
    } catch (e) {
      _logger.severe('处理通知失败: $e');
    }
  }

  // 显示通知（保留此方法以兼容现有代码）
  void showNotification(BuildContext context, ChatMessageData message) {
    try {
      // 不再使用应用内通知，改为使用系统通知
      // _chatNotificationService.showInAppNotification(context, message);
    } catch (e) {
      _logger.severe('显示通知失败: $e');
    }
  }

  // 请求历史消息（增量同步）
  Future<void> requestHistoryMessages({
    int? beforeTime,
    int limit = 200,
  }) async {
    if (!_isConnected) {
      _logger.warning('聊天服务器未连接，无法请求历史消息');
      return;
    }

    try {
      // 实现增量同步逻辑
      int? syncFromTime;

      if (beforeTime != null) {
        // 如果指定了时间，使用指定时间
        syncFromTime = beforeTime;
      } else {
        // 检查数据库中最后一条消息的时间
        if (_isDatabaseAvailable &&
            _chatDatabaseService != null &&
            _channelName != null) {
          final latestTimestamp = await _chatDatabaseService!
              .getLatestMessageTimestamp(_channelName!);
          if (latestTimestamp > 0) {
            // 数据库有消息，从最后一条消息时间开始同步
            syncFromTime = latestTimestamp;
            _logger.info(
                '数据库有消息，从最后一条消息时间开始同步: ${DateTime.fromMillisecondsSinceEpoch(latestTimestamp * 1000)}');
          } else {
            // 数据库为空，拉取最新消息（不指定时间）
            syncFromTime = null;
            _logger.info('数据库为空，拉取最新消息');
          }
        }
      }

      final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      // 根据后端实现，历史消息通过connect消息获取
      final message = {
        'type': 'connect',
        'id': _userId, // 后端期望的字段名
        'msg': syncFromTime != null ? '请求增量消息' : '请求最新消息', // 后端期望的消息内容字段
        'name': _userName ?? 'User$_userId', // 用户名
        'token': _chatroomInfo?['token']?.toString() ?? '',
        'channel_name': _channelName,
        'timestamp': (syncFromTime ?? currentTime).toString(),
        'client_type': Platform.isAndroid ? 'android' : 'ios',
      };

      if (syncFromTime != null) {
        message['afterTime'] = syncFromTime.toString(); // 请求此时间之后的消息
      }

      _logger.info(
          '请求历史消息（增量同步）: ${syncFromTime != null ? "从时间点${DateTime.fromMillisecondsSinceEpoch(syncFromTime * 1000)}" : "最新消息"}');
      await _webSocket?.send(jsonEncode(message));
    } catch (e) {
      _logger.severe('请求历史消息失败: $e');
      _lastError = e.toString();
    }
  }

  // 静态方法，用于关闭所有数据库连接
  static Future<void> closeAllDatabases() async {
    await ChatDatabaseService.closeAll();
  }

  /// 处理来自FCM的消息
  Future<void> processFcmMessage(Map<String, dynamic> data) async {
    _logger.info('处理FCM消息: $data');

    try {
      // 使用统一的消息处理函数处理FCM消息
      await processMessage(data, MessageSource.FCM);
    } catch (e) {
      _logger.severe('处理FCM消息失败: $e');
    }
  }

  // 处理消息的核心方法
  void _processMessage(ChatMessageData message) {
    try {
      // 过滤系统类型消息和空内容消息
      if (message.content.trim().isEmpty) {
        _logger.fine('跳过处理空内容消息');
        return;
      }

      // 过滤系统类型消息
      if (message.type == 'app_state' ||
          message.type == 'ping' ||
          message.type == 'pong' ||
          message.type == 'connect' ||
          message.type == 'delivery_strategy' ||
          message.type == 'user_state' ||
          message.type == 'register_fcm' ||
          message.type == 'background_keepalive' ||
          message.type == 'fetch_offline_messages' ||
          message.type == 'disconnect') {
        _logger.fine('跳过处理系统类型消息: ${message.type}');
        return;
      }

      // 过滤空内容的系统消息
      if ((message.type == 'system' || message.type == 'notification') &&
          message.content.trim().isEmpty) {
        _logger.fine('跳过处理空内容的系统消息');
        return;
      }

      // 获取当前应用状态和频道名称
      AppStateManager appStateManager = AppStateManager();
      final appState = appStateManager.currentState;
      final currentChannelName = appStateManager.currentChannelName;

      // 检查当前是否在聊天室中，且通道名称匹配
      bool isInChatroom = appState == AppState.CHATROOM_ACTIVE &&
          currentChannelName != null &&
          currentChannelName == message.channelName;

      // 检查消息是否已存在
      if (!_messageExists(message)) {
        // 添加消息到列表
        _messages.add(message);

        // 对所有消息进行排序
        _sortMessages();

        // 发送消息到流
        _messageController.add(message);

        // 只有当用户不在对应的聊天室中时，才处理通知
        if (!isInChatroom) {
          _processNotification(message);
        } else {
          _logger.info('用户当前在聊天室(${message.channelName})中，跳过通知处理');
        }

        // 保存消息到本地数据库
        if (_channelName != null &&
            _isDatabaseAvailable &&
            _chatDatabaseService != null &&
            _userId != null &&
            _userId != 'guest' &&
            _userId!.isNotEmpty) {
          // 确保使用正确的频道名
          String channelToUse = message.channelName ?? _channelName!;
          _chatDatabaseService!.saveMessage(message, channelToUse);
          _logger.fine('消息已保存到数据库，频道: $channelToUse');
        } else {
          _logger.fine('跳过保存消息到数据库，可能是因为用户未登录或数据库不可用');
        }
      }

      notifyListeners();
    } catch (e) {
      _logger.severe('处理消息失败: $e');
    }
  }

  // 添加消息撤回确认机制(修复方法实现)
  Future<bool> revokeMessage(String messageId) async {
    _logger.info('撤回消息: $messageId');

    try {
      if (!_isConnected) {
        _logger.warning('未连接到服务器，无法撤回消息');
        return false;
      }

      final completer = Completer<bool>();

      // 发送撤回请求
      await _webSocket?.send(jsonEncode({
        'type': 'revoke',
        'msg_id': messageId,
        'user_id': _userId,
        'channel': _channelName,
        'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        'client_type': Platform.isAndroid ? 'android' : 'ios', // 根据平台自动判断客户端类型
      }));

      // 设置超时
      Timer(const Duration(seconds: 5), () {
        if (!completer.isCompleted) {
          _logger.warning('撤回消息超时');
          completer.complete(false);
        }
      });

      // 等待服务器确认
      late StreamSubscription subscription;
      subscription = _messageController.stream.listen((message) {
        // 检查是否是撤回消息的确认
        if (message.metadata != null &&
            message.metadata!['type'] == 'revoke_ack' &&
            message.metadata!['msg_id'] == messageId) {
          if (!completer.isCompleted) {
            _logger.info('收到消息撤回确认');
            completer.complete(true);
            subscription.cancel();
          }
        }
      });

      return completer.future;
    } catch (e) {
      _logger.severe('撤回消息失败: $e');
      return false;
    }
  }

  // 清理内存和数据库中的空系统消息
  Future<int> cleanEmptySystemMessages() async {
    int removedCount = 0;

    try {
      // 从内存中删除空系统消息
      _logger.info('开始清理内存中的空系统消息');
      _logger.info('清理前消息数量: ${_messages.length}');

      // 记录几个系统消息示例
      List<ChatMessageData> systemMessages = _messages
          .where((msg) =>
              msg.type == 'app_state' ||
              msg.type == 'ping' ||
              msg.type == 'pong' ||
              msg.type == 'connect' ||
              msg.type == 'delivery_strategy' ||
              msg.type == 'user_state' ||
              msg.isSystem)
          .toList();

      if (systemMessages.isNotEmpty) {
        _logger.info('内存中存在 ${systemMessages.length} 条系统消息，示例:');
        for (int i = 0; i < min(5, systemMessages.length); i++) {
          _logger.info('系统消息 #$i: ${systemMessages[i].toJson()}');
        }
      }

      // 从列表中移除空系统消息
      _messages.removeWhere((message) {
        bool shouldRemove = (message.type == 'app_state' ||
            message.type == 'ping' ||
            message.type == 'pong' ||
            message.type == 'connect' ||
            message.type == 'delivery_strategy' ||
            message.type == 'user_state' ||
            (message.isSystem && message.content.trim().isEmpty));

        if (shouldRemove) {
          removedCount++;
        }

        return shouldRemove;
      });

      _logger.info('已从内存中清理 $removedCount 条空系统消息');
      _logger.info('清理后消息数量: ${_messages.length}');

      // 如果数据库可用，也清理数据库中的空系统消息
      if (_isDatabaseAvailable &&
          _chatDatabaseService != null &&
          _channelName != null &&
          _userId != null &&
          _userId != 'guest' &&
          _userId!.isNotEmpty) {
        int dbRemovedCount =
            await _chatDatabaseService!.cleanEmptySystemMessages(_channelName!);
        _logger.info('已从数据库中清理 $dbRemovedCount 条空系统消息');
        removedCount += dbRemovedCount;
      }

      // 如果有消息被删除，通知监听器
      if (removedCount > 0) {
        notifyListeners();
      }

      return removedCount;
    } catch (e) {
      _logger.severe('清理空系统消息出错: $e');
      return 0;
    }
  }

  // 处理外部消息（FCM或其他来源）
  Future<void> processExternalMessage(Map<String, dynamic> messageData) async {
    try {
      // 确保服务已初始化
      if (!_initialized) {
        await initialize();
      }

      // 调用内部消息处理方法
      _onMessageReceived(messageData);

      _logger.info('已处理外部消息: ${messageData['type']}');
    } catch (e) {
      _logger.severe('处理外部消息失败: $e');
    }
  }

  /// 统一处理消息的方法
  /// 所有消息处理都通过这个方法，根据消息来源采取不同的处理策略
  Future<void> processMessage(
      Map<String, dynamic> data, MessageSource source) async {
    _logger.info('处理${source.toString()}消息: ${jsonEncode(data)}');

    try {
      // 1. 标准化消息格式
      ChatMessageData message = await _standardizeMessage(data, source);

      // 2. 检查消息是否已存在（使用唯一消息指纹）
      if (_messageExists(message)) {
        _logger.info('消息已存在，跳过处理: ${message.msgId ?? message.id}');
        return;
      }

      // 3. 根据消息源优先级处理
      switch (source) {
        case MessageSource.WEBSOCKET:
          // WebSocket消息优先级最高，直接添加到消息列表和数据库
          _addMessageToState(message);
          _saveMessageToDB(message);
          break;

        case MessageSource.OFFLINE_PULL:
          // 离线拉取消息，检查是否有相同消息ID的FCM消息已处理
          _addMessageToState(message);
          _saveMessageToDB(message);
          break;

        case MessageSource.FCM:
          // FCM消息，检查是否已通过WebSocket或离线拉取处理过
          // 获取应用状态和频道信息
          AppStateManager appStateManager = AppStateManager();
          final appState = appStateManager.currentState;
          final currentChannelName = appStateManager.currentChannelName;

          // 检查用户是否在聊天室中
          bool isInChatroom = appState == AppState.CHATROOM_ACTIVE &&
              currentChannelName != null &&
              currentChannelName == message.channelName;

          // 只有当用户不在对应的聊天室中时，才使用SystemNotificationService显示通知
          if (!isInChatroom) {
            // 使用SystemNotificationService处理FCM消息（显示通知）
            final notificationService = SystemNotificationService();
            await notificationService.processFcmMessage(data);
            _logger.info('FCM消息已由SystemNotificationService处理（显示通知）');
          } else {
            _logger.info('用户当前在聊天室中，跳过通知显示');
          }

          // 添加消息到状态和数据库
          _addMessageToState(message);
          _saveMessageToDB(message);
          break;
      }
    } catch (e) {
      _logger.severe('处理消息失败: $e');
    }
  }

  // 标准化消息格式
  Future<ChatMessageData> _standardizeMessage(
      Map<String, dynamic> data, MessageSource source) async {
    try {
      // 处理可能的类型转换问题
      String? messageId = data['messageId'] ?? data['msg_id'];
      String? content = data['content'] ?? data['message'];

      // 处理user_id可能是int类型的情况
      String senderId;
      if (data['senderId'] != null) {
        senderId = data['senderId'].toString();
      } else if (data['user_id'] != null) {
        senderId = data['user_id'].toString();
      } else {
        senderId = 'unknown';
      }

      final String? senderName = data['senderName'] ?? data['user_name'];

      // 确保必要字段存在
      if (content == null || content.isEmpty) {
        content = '';
      }

      if (senderId.isEmpty || senderName == null || senderName.isEmpty) {
        throw Exception('消息缺少必要字段');
      }

      // 正确处理消息时间戳
      DateTime timestamp;
      int? createTimeInt;

      // 首先尝试使用原始create_time
      if (data['create_time'] != null) {
        try {
          createTimeInt = int.tryParse(data['create_time'].toString());
          if (createTimeInt != null) {
            timestamp =
                DateTime.fromMillisecondsSinceEpoch(createTimeInt * 1000);
          } else {
            // 如果无法解析create_time，尝试使用timestamp
            if (data['timestamp'] != null) {
              int? timestampInt = int.tryParse(data['timestamp'].toString());
              if (timestampInt != null) {
                timestamp =
                    DateTime.fromMillisecondsSinceEpoch(timestampInt * 1000);
                createTimeInt = timestampInt;
              } else {
                timestamp = DateTime.now();
                createTimeInt = timestamp.millisecondsSinceEpoch ~/ 1000;
              }
            } else {
              // 如果没有可用的时间戳，使用当前时间
              timestamp = DateTime.now();
              createTimeInt = timestamp.millisecondsSinceEpoch ~/ 1000;
            }
          }
        } catch (e) {
          // 如果解析失败，使用当前时间
          timestamp = DateTime.now();
          createTimeInt = timestamp.millisecondsSinceEpoch ~/ 1000;
          _logger.warning('解析消息时间戳失败: $e，使用当前时间');
        }
      } else if (data['timestamp'] != null) {
        // 如果没有create_time但有timestamp
        try {
          int? timestampInt = int.tryParse(data['timestamp'].toString());
          if (timestampInt != null) {
            timestamp =
                DateTime.fromMillisecondsSinceEpoch(timestampInt * 1000);
            createTimeInt = timestampInt;
          } else {
            timestamp = DateTime.now();
            createTimeInt = timestamp.millisecondsSinceEpoch ~/ 1000;
          }
        } catch (e) {
          timestamp = DateTime.now();
          createTimeInt = timestamp.millisecondsSinceEpoch ~/ 1000;
          _logger.warning('解析消息timestamp失败: $e，使用当前时间');
        }
      } else {
        // 如果没有任何时间字段，使用当前时间
        timestamp = DateTime.now();
        createTimeInt = timestamp.millisecondsSinceEpoch ~/ 1000;
      }

      // 如果消息ID包含时间戳（格式如：1747899630808_1394），尝试提取
      if (messageId != null && messageId.contains('_')) {
        try {
          String timePartStr = messageId.split('_')[0];
          int? timePart = int.tryParse(timePartStr);
          if (timePart != null) {
            // 检查是否是毫秒时间戳（13位）或秒时间戳（10位）
            if (timePartStr.length >= 13) {
              // 毫秒时间戳
              timestamp = DateTime.fromMillisecondsSinceEpoch(timePart);
              createTimeInt = timePart ~/ 1000;
            } else if (timePartStr.length >= 10) {
              // 秒时间戳
              timestamp = DateTime.fromMillisecondsSinceEpoch(timePart * 1000);
              createTimeInt = timePart;
            }
          }
        } catch (e) {
          // 忽略解析错误，使用之前设置的时间戳
          _logger.fine('从消息ID提取时间戳失败: $e');
        }
      }

      // 处理is_admin可能是int类型的情况
      bool isAdmin = false;
      if (data['is_admin'] != null) {
        isAdmin = data['is_admin'] == 'true' ||
            data['is_admin'] == true ||
            data['is_admin'] == 1;
      } else if (data['admin'] != null) {
        isAdmin = data['admin'] == 'true' ||
            data['admin'] == true ||
            data['admin'] == 1;
      }

      // 处理回复消息
      ChatMessageData? replyMessage;
      if (data['reply'] != null) {
        try {
          if (data['reply'] is String) {
            Map<String, dynamic> replyJson = jsonDecode(data['reply']);
            replyMessage = ChatMessageData.fromJson(replyJson);
          } else if (data['reply'] is Map) {
            replyMessage = ChatMessageData.fromJson(
                Map<String, dynamic>.from(data['reply']));
          }
        } catch (e) {
          _logger.warning('解析回复消息失败: $e');
        }
      }

      // 处理频道名称
      String? channelName =
          data['channel_name'] ?? data['channelName'] ?? _channelName;

      // 添加详细的频道调试日志
      _logger.info(
          '🔍 频道名称处理: channel_name=${data['channel_name']}, channelName=${data['channelName']}, _channelName=$_channelName, 最终频道=$channelName');

      // 创建标准化的消息对象
      return ChatMessageData(
        id: messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
        userId: senderId,
        userName: senderName,
        content: content,
        timestamp: timestamp,
        type: data['msg_type'] ?? data['type'] ?? 'text',
        isAdmin: isAdmin,
        channelName: channelName,
        read: data['is_unread'] != true, // 使用传入的未读标记
        createTime: createTimeInt,
        reply: replyMessage,
        imgs: data['imgs'] is List ? List<String>.from(data['imgs']) : null,
        source: source, // 设置消息来源
      );
    } catch (e) {
      _logger.severe('标准化消息失败: $e');
      throw e;
    }
  }

  // 添加消息到状态
  void _addMessageToState(ChatMessageData message) {
    // 检查消息是否已存在
    if (_messageExists(message)) {
      _logger.info('消息已存在，跳过添加: ${message.msgId ?? message.id}');
      return;
    }

    // 添加消息到列表
    _messages.add(message);

    // 对所有消息进行排序
    _sortMessages();

    // 发送消息到流
    _messageController.add(message);

    // 获取当前应用状态和频道名称
    AppStateManager appStateManager = AppStateManager();
    final appState = appStateManager.currentState;
    final currentChannelName = appStateManager.currentChannelName;

    // 检查当前是否在聊天室中，且通道名称匹配
    bool isInChatroom = appState == AppState.CHATROOM_ACTIVE &&
        currentChannelName != null &&
        currentChannelName == message.channelName;

    // 只有当用户不在对应的聊天室中时，才处理通知
    if (!isInChatroom) {
      _processNotification(message);
    } else {
      _logger.info('用户当前在聊天室(${message.channelName})中，跳过通知处理');
    }

    // 通知监听者
    notifyListeners();
    _logger.info('消息已添加到状态: ${message.msgId ?? message.id}');
  }

  // 保存消息到数据库
  Future<void> _saveMessageToDB(ChatMessageData message) async {
    if (_chatDatabaseService != null &&
        _userId != null &&
        _userId != 'guest' &&
        _userId!.isNotEmpty &&
        _isDatabaseAvailable) {
      try {
        String channelToUse = message.channelName ?? _channelName!;

        if (channelToUse == _channelName) {
          // 如果是当前频道，保存为普通消息
          await _chatDatabaseService!.saveMessage(message, channelToUse);
          _logger.info('消息已保存到数据库: ${message.id}, 频道: $channelToUse');
        } else {
          // 如果不是当前频道，保存为离线消息
          await _chatDatabaseService!.saveOfflineMessage(message, channelToUse);
          _logger.info('消息已保存为离线消息: ${message.id}, 频道: $channelToUse');
        }
      } catch (e) {
        _logger.warning('保存消息到数据库失败: $e');
      }
    } else {
      _logger.fine('跳过保存消息到数据库，可能是因为用户未登录或数据库不可用');
    }
  }

  /// 获取内部的WebSocketUtility实例
  WebSocketUtility? getWebSocketUtility() {
    return _webSocket;
  }
}
