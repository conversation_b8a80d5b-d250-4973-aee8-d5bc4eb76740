import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import 'package:logging/logging.dart';
import '../utils/logging.dart';

class RouteGuards {
  static final Logger _logger = Logging.getLogger('RouteGuards');
  static final Map<String, bool> _accessCache = {};

  static bool isAuthenticated(AuthProvider authProvider) {
    return authProvider.isAuthenticated;
  }

  static bool requiresAuth(String routeName) {
    final authRequiredRoutes = [
      '/profile',
      '/chat',
      '/live',
      // 添加其他需要身份验证的路由
    ];
    return authRequiredRoutes.contains(routeName);
  }

  static Future<bool> canAccess(BuildContext context, String routeName) async {
    // 如果已经缓存了访问权限，直接返回
    if (_accessCache.containsKey(routeName)) {
      return _accessCache[routeName]!;
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    bool hasAccess = true;

    // 如果路由需要认证
    if (requiresAuth(routeName)) {
      // 如果用户已认证，刷新用户信息
      if (isAuthenticated(authProvider)) {
        final refreshSuccess =
            await authProvider.refreshUserInfo(notify: false);
        if (!refreshSuccess) {
          _logger.warning('刷新用户信息失败');
          hasAccess = false;
        }
      } else {
        _logger.info('用户未认证，无法访问 $routeName');
        hasAccess = false;
      }

      // 如果用户已认证且刷新成功，进行特定路由的权限检查
      if (hasAccess) {
        final user = authProvider.user;
        if (routeName == '/chat') {
          final vipLevel = user?.vip['level'] as int?;
          final vipEnd = user?.vip['end'] as int?;
          final currentTime = authProvider.getCurrentTimestamp();
          _logger.info(
              '聊天室权限检查: vipLevel=$vipLevel, vipEnd=$vipEnd, currentTime=$currentTime');
          
          // 修改权限检查逻辑，添加更详细的日志
          if (vipLevel == null || vipLevel <= 0) {
            _logger.info('聊天室权限检查失败: VIP等级不足 (vipLevel=$vipLevel)');
            hasAccess = false;
          } else if (vipEnd == null) {
            _logger.info('聊天室权限检查失败: VIP结束时间为空');
            hasAccess = false;
          } else if (vipEnd <= currentTime) {
            _logger.info('聊天室权限检查失败: VIP已过期 (vipEnd=$vipEnd, currentTime=$currentTime)');
            hasAccess = false;
          } else {
            _logger.info('聊天室权限检查通过: vipLevel=$vipLevel, vipEnd=$vipEnd, currentTime=$currentTime');
            hasAccess = true;
          }
          
          // 清除访问缓存，确保每次都重新检查权限
          _accessCache.remove(routeName);
        } else if (routeName == '/live') {
          final assetsLive = user?.assets['live'] as int?;
          hasAccess = assetsLive != null && assetsLive > 0;
          _logger.info('直播室权限检查: $hasAccess');
        }
      }
    }

    // 缓存访问权限结果
    _accessCache[routeName] = hasAccess;
    return hasAccess;
  }

  static Future<String?> redirectPath(
      BuildContext context, String routeName) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // 如果路由需要认证但用户未认证，重定向到登录页面
    if (requiresAuth(routeName) && !isAuthenticated(authProvider)) {
      _logger.info('重定向到登录页面');
      return '/user/login';
    }

    // 如果用户已登录但尝试访问登录页面，重定向到首页
    if (routeName == '/user/login' && isAuthenticated(authProvider)) {
      _logger.info('用户已登录，重定向到首页');
      return '/home';
    }

    // 对于聊天和直播页面，进行特殊的权限检查
    if (routeName == '/chat' || routeName == '/live') {
      final hasAccess = await canAccess(context, routeName);
      if (!hasAccess) {
        _logger.info('用户无权限访问 $routeName，重定向到无权限页面');
        return routeName == '/chat' ? '/chat/check' : '/live/check';
      }
    }

    // 如果不需要重定向，返回 null
    return null;
  }
}
