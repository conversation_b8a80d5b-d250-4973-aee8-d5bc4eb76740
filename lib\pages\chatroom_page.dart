import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:provider/provider.dart';
import '../services/app_state_manager.dart';
import '../utils/app_state.dart';
import '../utils/web_socket.dart';
import '../config/config.dart';

class ChatRoomPage extends StatefulWidget {
  final String channelName;

  const ChatRoomPage({
    Key? key,
    required this.channelName,
  }) : super(key: key);

  @override
  State<ChatRoomPage> createState() => _ChatRoomPageState();
}

class _ChatRoomPageState extends State<ChatRoomPage> {
  final Logger _logger = Logger('ChatRoomPage');
  final TextEditingController _messageController = TextEditingController();
  final List<Map<String, dynamic>> _messages = [];
  bool _isConnected = false;
  WebSocketUtility? _webSocket;
  late AppStateManager _appStateManager;

  @override
  void initState() {
    super.initState();
    _logger.info('ChatRoomPage.initState - 频道: ${widget.channelName}');

    _appStateManager = Provider.of<AppStateManager>(context, listen: false);

    _appStateManager.updateState(AppState.CHATROOM_ACTIVE,
        channelName: widget.channelName);

    _initWebSocket();
  }

  @override
  void dispose() {
    _logger.info('ChatRoomPage.dispose - 频道: ${widget.channelName}');

    _appStateManager.updateState(AppState.APP_ACTIVE);

    _webSocket?.disconnect();

    _messageController.dispose();
    super.dispose();
  }

  Future<void> _initWebSocket() async {
    try {
      _webSocket = WebSocketUtility(
        url: AppConfig.chatWsUrl,
        onOpen: _handleSocketOpen,
        onMessage: _handleSocketMessage,
        onClose: _handleSocketClose,
        onError: _handleSocketError,
      );

      await _webSocket!.connect(
        channelName: widget.channelName,
        userId: '123456',
      );
    } catch (e) {
      _logger.severe('WebSocket初始化失败: $e');
    }
  }

  void _handleSocketOpen() {
    _logger.info('WebSocket连接已打开');
    setState(() {
      _isConnected = true;
    });

    _webSocket!.connectChatRoom();
  }

  void _handleSocketMessage(dynamic data) {
    _logger.info('收到WebSocket消息: $data');

    try {
      Map<String, dynamic> message;
      if (data is String) {
        message = json.decode(data);
      } else if (data is Map) {
        message = Map<String, dynamic>.from(data);
      } else {
        return;
      }

      final messageType = message['type'] as String?;

      if (messageType == 'chat' || messageType == 'msg') {
        setState(() {
          _messages.add(message);
        });
      }
    } catch (e) {
      _logger.warning('处理消息失败: $e');
    }
  }

  void _handleSocketClose() {
    _logger.info('WebSocket连接已关闭');
    setState(() {
      _isConnected = false;
    });
  }

  void _handleSocketError(dynamic error) {
    _logger.severe('WebSocket错误: $error');
    setState(() {
      _isConnected = false;
    });
  }

  void _sendMessage() {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    try {
      if (_webSocket != null && _webSocket!.isConnected) {
        final chatMessage = {
          'type': 'chat',
          'content': message,
          'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000,
          'user_id': '123456',
          'username': '测试用户',
        };

        _webSocket!.send(chatMessage);

        _messageController.clear();

        setState(() {
          _messages.add(chatMessage);
        });
      } else {
        _logger.warning('无法发送消息：WebSocket未连接');
      }
    } catch (e) {
      _logger.severe('发送消息失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('聊天室: ${widget.channelName}'),
        actions: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Icon(
              _isConnected ? Icons.wifi : Icons.wifi_off,
              color: _isConnected ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: _messages.isEmpty
                ? const Center(child: Text('暂无消息，开始聊天吧！'))
                : ListView.builder(
                    itemCount: _messages.length,
                    itemBuilder: (context, index) {
                      final message = _messages[index];
                      final isCurrentUser = message['user_id'] == '123456';

                      return Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 4.0,
                          horizontal: 8.0,
                        ),
                        child: Align(
                          alignment: isCurrentUser
                              ? Alignment.centerRight
                              : Alignment.centerLeft,
                          child: Container(
                            constraints: BoxConstraints(
                              maxWidth: MediaQuery.of(context).size.width * 0.7,
                            ),
                            padding: const EdgeInsets.symmetric(
                              vertical: 8.0,
                              horizontal: 12.0,
                            ),
                            decoration: BoxDecoration(
                              color: isCurrentUser
                                  ? Colors.blue[100]
                                  : Colors.grey[300],
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  message['username'] ?? '未知用户',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12.0,
                                  ),
                                ),
                                const SizedBox(height: 4.0),
                                Text(message['content'] ?? ''),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: const InputDecoration(
                      hintText: '输入消息...',
                      border: OutlineInputBorder(),
                    ),
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.send),
                  onPressed: _sendMessage,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
