import 'dart:convert';
import 'dart:io';
import 'package:estockcafe/config/config.dart';
import 'package:estockcafe/utils/logging.dart';
import 'package:logging/logging.dart';
import 'package:estockcafe/models/user.dart';
import 'package:estockcafe/services/token_service.dart';
import 'package:estockcafe/utils/functions.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

/// 用户服务类，管理用户登录状态和令牌
class UserService {
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;

  final Logger _logger = Logger('UserService');
  final TokenService _tokenService = TokenService();
  final String baseUrl = AppConfig.baseUrl;

  // 用户信息
  String? _userId;
  String? _userName;
  String? _userToken;
  bool _isLoggedIn = false;

  // Getters
  String? get userId => _userId;
  String? get userName => _userName;
  String? get userToken => _userToken;
  bool get isLoggedIn => _isLoggedIn;

  UserService._internal();

  /// 初始化用户服务，从持久化存储加载用户信息
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      _userId = prefs.getString('user_id');
      _userName = prefs.getString('user_name');
      _userToken = await _tokenService.getAccessToken();
      _isLoggedIn = _userId != null && _userToken != null;

      _logger.fine('用户服务初始化完成, 登录状态: $_isLoggedIn');
    } catch (e) {
      _logger.severe('初始化用户服务失败: $e');
      _isLoggedIn = false;
    }
  }

  /// 设置用户信息
  Future<void> setUserInfo({
    required String userId,
    required String userName,
    required String token,
  }) async {
    try {
      _userId = userId;
      _userName = userName;
      _userToken = token;
      _isLoggedIn = true;

      // 保存到持久化存储
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_id', userId);
      await prefs.setString('user_name', userName);
      await prefs.setString('user_token', token);
    } catch (e) {
      _logger.severe('保存用户信息失败: $e');
    }
  }

  /// 清除用户信息（登出）
  Future<void> clearUserInfo() async {
    try {
      _userId = null;
      _userName = null;
      _userToken = null;
      _isLoggedIn = false;

      // 从持久化存储中删除
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('user_id');
      await prefs.remove('user_name');
      await prefs.remove('user_token');
    } catch (e) {
      _logger.severe('清除用户信息失败: $e');
    }
  }

  // 设置刷新令牌的失效时间为30天（以秒为单位）
  static const int refreshTokenExpiresIn = 30 * 24 * 60 * 60; // 30天

  static const int accessTokenExpiresIn = 60 * 60; // 1小时

  // 添加一个标志，用于防止短时间内重复刷新令牌
  bool _isRefreshingToken = false;
  DateTime? _lastRefreshAttempt;
  static const _refreshCooldown = Duration(seconds: 5); // 设置5秒的冷却时间

  Future<Map<String, String>> _getHeaders({String? token}) async {
    final headers = {'Content-Type': 'application/json'};
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }

  Future<Map<String, dynamic>> login(String login, String password) async {
    try {
      final headers = await _getHeaders();
      final response = await jsonRequest(
        '$baseUrl/user/login',
        method: 'POST',
        headers: headers,
        body: jsonEncode({'login': login, 'password': password}),
      );

      if (response['code'] == 0) {
        if (response['data'] is! Map<String, dynamic>) {
          throw Exception('登录响应数据格式不正确');
        }

        User user;
        try {
          user = User.fromJson(response['data']);
        } catch (e) {
          _logger.severe('创建User对象时发生错误: $e');
          rethrow;
        }

        final accessToken = response['data']['accessToken'] as String?;
        final refreshToken = response['data']['refreshToken'] as String?;

        if (accessToken != null && refreshToken != null) {
          await _tokenService.setAccessToken(accessToken, accessTokenExpiresIn);
          await _tokenService.setRefreshToken(
              refreshToken, refreshTokenExpiresIn);
          return {
            'user': user,
            'tokens': {'accessToken': accessToken, 'refreshToken': refreshToken}
          };
        } else {
          throw Exception('登录成功但令牌信息缺失');
        }
      } else {
        throw Exception('登录失败: ${response['msg']}');
      }
    } catch (e) {
      _logger.severe('登录过程中发生错误: $e');
      rethrow;
    }
  }

  Future<String> register(
      String username, String email, String password, String nickname) async {
    try {
      final headers = await _getHeaders();
      final response = await jsonRequest(
        '$baseUrl/user/register',
        method: 'POST',
        headers: headers,
        body: jsonEncode({
          'username': username,
          'email': email,
          'password': password,
          'nickname': nickname
        }),
      );

      if (response['code'] == 0) {
        return response['msg'] ?? '注册成功，请检查邮箱进行验证';
      } else {
        return '注册失败: ${response['msg']}';
      }
    } catch (e) {
      _logger.severe('注册过程中发生错误: $e');
      return '注册失败: $e';
    }
  }

  Future<void> logout() async {
    try {
      await _tokenService.removeTokens();
    } catch (e) {
      _logger.severe('登出时发生错误: $e');
      rethrow;
    }
  }

  Future<Map<String, String>?> refreshAccessToken(
      {bool forceRefresh = false}) async {
    int retryCount = 0;
    const maxRetries = 2; // 最多重试2次
    const retryDelay = Duration(seconds: 1); // 重试间隔1秒

    Future<Map<String, String>?> attemptRefresh() async {
      try {
        // 首先检查用户是否已登出（通过检查refreshToken是否存在）
        final refreshToken = await _tokenService.getRefreshToken();
        if (refreshToken == null) {
          // 清除可能残留的访问令牌
          await _tokenService.removeAccessToken();
          _isRefreshingToken = false;
          return null;
        }

        // 检查是否正在刷新令牌或者是否在冷却期内
        final now = DateTime.now();
        if (_isRefreshingToken && !forceRefresh) {
          _logger.fine('令牌刷新已在进行中，跳过本次刷新请求');
          return null;
        }

        // 如果不是强制刷新，才检查冷却期
        if (!forceRefresh &&
            _lastRefreshAttempt != null &&
            now.difference(_lastRefreshAttempt!) < _refreshCooldown) {
          _logger.fine('令牌刷新冷却期内，跳过本次刷新请求');
          return null;
        }

        // 设置标志和时间戳
        _isRefreshingToken = true;
        _lastRefreshAttempt = now;

        // 尝试使用TokenService的refreshToken方法
        final newToken = await _tokenService.refreshToken();
        if (newToken != null) {
          _isRefreshingToken = false;
          return {'accessToken': newToken, 'refreshToken': refreshToken};
        }

        // 尝试向服务器发送刷新请求
        final headers = await _getHeaders();
        final response = await jsonRequest(
          '$baseUrl/user/refresh-token',
          method: 'POST',
          headers: headers,
          body: jsonEncode({'refreshToken': refreshToken}),
        );

        if (response['code'] == 0) {
          final accessToken = response['data']?['access_token'] as String?;
          final newRefreshToken = response['data']?['refresh_token'] as String?;
          if (accessToken != null && newRefreshToken != null) {
            await _tokenService.setAccessToken(
                accessToken, accessTokenExpiresIn);
            await _tokenService.setRefreshToken(
                newRefreshToken, refreshTokenExpiresIn);
            _isRefreshingToken = false;
            return {
              'accessToken': accessToken,
              'refreshToken': newRefreshToken
            };
          } else {
            _logger.warning('刷新令牌成功但新令牌信息缺失');
            _isRefreshingToken = false;
            return null;
          }
        } else {
          _logger.warning('刷新令牌失败: ${response['msg']}');
          _isRefreshingToken = false;
          return null;
        }
      } catch (e) {
        _logger.severe('刷新令牌时发生错误: $e');
        _isRefreshingToken = false;
        return null;
      } finally {
        // 确保在所有情况下都重置标志
        _isRefreshingToken = false;
      }
    }

    // 首次尝试
    Map<String, String>? result = await attemptRefresh();

    // 如果失败且用户未登出，进行重试
    while (result == null && retryCount < maxRetries) {
      // 再次检查用户是否已登出
      final refreshToken = await _tokenService.getRefreshToken();
      if (refreshToken == null) {
        break;
      }

      retryCount++;
      _logger.fine('刷新令牌失败，进行第$retryCount次重试...');
      await Future.delayed(retryDelay);
      result = await attemptRefresh();
    }

    // 如果所有重试都失败，记录警告
    if (result == null && retryCount >= maxRetries) {
      _logger.warning('刷新令牌多次尝试失败，用户需要重新登录');
    }

    return result;
  }

  Future<User?> getCurrentUser() async {
    try {
      String? token = await _tokenService.getAccessToken();

      if (token == null) {
        final refreshResult = await refreshAccessToken();
        if (refreshResult == null) {
          return null;
        }
        token = refreshResult['accessToken'];
      }

      final headers = await _getHeaders(token: token);
      final response = await jsonRequest(
        '$baseUrl/user/info',
        headers: headers,
      );

      if (response['code'] == 0) {
        return User.fromJson(response['data']);
      } else {
        throw Exception('获取用户信息失败: ${response['msg']}');
      }
    } catch (e) {
      _logger.severe('获取当前用户信息时发生错误: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>> loadMembershipData() async {
    try {
      String? token = await _tokenService.getAccessToken();

      if (token == null) {
        getCurrentUser();
        token = await _tokenService.getAccessToken();
      }

      final headers = await _getHeaders(token: token);
      final response = await jsonRequest(
        '$baseUrl/user/vipdata',
        headers: headers,
      );

      if (response['code'] == 0) {
        return {
          'membershipTypes': List<Map<String, dynamic>>.from(
              response['data']['membershipTypes']),
          'benefits': List<String>.from(response['data']['benefits']),
          'premiumBenefits':
              List<String>.from(response['data']['premiumBenefits']),
          'paymentMethods': List<Map<String, dynamic>>.from(
              response['data']['paymentMethods']),
        };
      } else {
        throw Exception('加载会员数据失败: ${response['msg']}');
      }
    } catch (e) {
      _logger.severe('加载会员数据时发生错误: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> initiatePayment(
      Map<String, dynamic> params) async {
    try {
      String? token = await _tokenService.getAccessToken();
      if (token == null) {
        final refreshResult = await refreshAccessToken();
        if (refreshResult == null) {
          throw Exception('用户未登录或登录已过期');
        }
        token = refreshResult['accessToken'];
      }

      final headers = await _getHeaders(token: token);
      final response = await jsonRequest(
        '$baseUrl/payment',
        method: 'POST',
        headers: headers,
        body: jsonEncode(params),
      );

      if (response['code'] == 0) {
        return response['data'];
      } else {
        throw Exception('发起支付失败: ${response['msg']}');
      }
    } catch (e) {
      _logger.severe('发起支付时发生错误: $e');
      rethrow;
    }
  }

  /// 更新用户资料
  Future<Map<String, dynamic>> updateProfile({
    required String name,
    String? url,
    String? description,
    File? avatarFile,
  }) async {
    try {
      String? token = await _tokenService.getAccessToken();
      if (token == null) {
        final refreshResult = await refreshAccessToken();
        if (refreshResult == null) {
          throw Exception('用户未登录或登录已过期');
        }
        token = refreshResult['accessToken'];
      }

      final headers = await _getHeaders(token: token);

      // 构建请求参数
      final params = {
        'mm_name': name,
        'mm_url': url ?? '',
        'mm_desc': description ?? '',
      };

      // 打印请求参数
      _logger.info('=== UserService.updateProfile 请求参数 ===');
      _logger.info('API URL: $baseUrl/user/update-profile');
      _logger.info('请求参数: $params');
      _logger.info('是否有头像文件: ${avatarFile != null}');
      if (avatarFile != null) {
        _logger.info('头像文件路径: ${avatarFile.path}');
        final fileSize = await avatarFile.length();
        _logger.info('头像文件大小: $fileSize bytes');
      }
      _logger.info('==========================================');

      // 如果有头像文件，需要使用multipart请求
      if (avatarFile != null) {
        return await _uploadProfileWithAvatar(params, avatarFile, token!);
      }

      final response = await jsonRequest(
        '$baseUrl/user/update-profile',
        method: 'POST',
        headers: headers,
        body: jsonEncode(params),
      );

      _logger.info('=== JSON 响应数据 ===');
      _logger.info('响应内容: $response');
      _logger.info('==================');

      if (response['code'] == 0) {
        return {
          'success': true,
          'message': response['msg'] ?? '修改成功',
        };
      } else {
        return {
          'success': false,
          'message': response['msg'] ?? '修改失败',
        };
      }
    } catch (e) {
      _logger.severe('更新用户资料时发生错误: $e');
      return {
        'success': false,
        'message': '网络错误: $e',
      };
    }
  }

  /// 上传头像并更新资料
  Future<Map<String, dynamic>> _uploadProfileWithAvatar(
    Map<String, dynamic> params,
    File avatarFile,
    String token,
  ) async {
    try {
      final uri = Uri.parse('$baseUrl/user/update-profile');
      final request = http.MultipartRequest('POST', uri);

      _logger.info('=== Multipart 头像上传请求 ===');
      _logger.info('请求URL: $uri');
      _logger.info('请求方法: POST (multipart/form-data)');

      // 添加认证头
      final headers = await _getHeaders(token: token);
      request.headers.addAll(headers);
      _logger.info('请求头: ${request.headers}');

      // 添加表单参数
      params.forEach((key, value) {
        request.fields[key] = value.toString();
      });
      _logger.info('表单字段: ${request.fields}');

      // 添加头像文件，强制使用.jpg扩展名让后端识别为图片
      final bytes = await avatarFile.readAsBytes();
      final multipartFile = http.MultipartFile.fromBytes(
        'avatar',
        bytes,
        filename: 'avatar.jpg', // 强制使用.jpg扩展名
      );
      request.files.add(multipartFile);
      _logger.info('头像文件字段名: avatar');
      _logger.info('头像文件路径: ${avatarFile.path}');
      _logger.info('头像文件大小: ${multipartFile.length} bytes');
      _logger.info('使用的文件名: avatar.jpg');
      _logger.info('头像文件类型: ${multipartFile.contentType}');
      _logger.info('=============================');

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      _logger.info('=== Multipart 响应数据 ===');
      _logger.info('响应状态码: ${response.statusCode}');
      _logger.info('响应头: ${response.headers}');
      _logger.info('响应体: ${response.body}');
      _logger.info('========================');

      final responseData = json.decode(response.body);

      if (responseData['code'] == 0) {
        return {
          'success': true,
          'message': responseData['msg'] ?? '修改成功',
        };
      } else {
        return {
          'success': false,
          'message': responseData['msg'] ?? '修改失败',
        };
      }
    } catch (e) {
      _logger.severe('上传头像时发生错误: $e');
      return {
        'success': false,
        'message': '上传头像失败: $e',
      };
    }
  }

  /// 修改密码
  Future<Map<String, dynamic>> changePassword({
    required String oldPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    try {
      String? token = await _tokenService.getAccessToken();
      if (token == null) {
        final refreshResult = await refreshAccessToken();
        if (refreshResult == null) {
          throw Exception('用户未登录或登录已过期');
        }
        token = refreshResult['accessToken'];
      }

      final headers = await _getHeaders(token: token);

      // 获取当前用户信息
      final user = await getCurrentUser();
      if (user == null) {
        throw Exception('无法获取用户信息');
      }

      // 构建请求参数
      final params = {
        'mm_usrname': user.name,
        'mm_pass_old': oldPassword,
        'mm_pass_new': newPassword,
        'mm_pass_new2': confirmPassword,
      };

      final response = await jsonRequest(
        '$baseUrl/user/change-password',
        method: 'POST',
        headers: headers,
        body: jsonEncode(params),
      );

      if (response['code'] == 0) {
        return {
          'success': true,
          'message': response['msg'] ?? '密码修改成功',
        };
      } else {
        return {
          'success': false,
          'message': response['msg'] ?? '密码修改失败',
        };
      }
    } catch (e) {
      _logger.severe('修改密码时发生错误: $e');
      return {
        'success': false,
        'message': '网络错误: $e',
      };
    }
  }

  /// 发送找回密码邮件
  Future<Map<String, dynamic>> sendResetPasswordEmail(String email) async {
    try {
      final headers = await _getHeaders();
      final response = await jsonRequest(
        '$baseUrl/user/forgot-password',
        method: 'POST',
        headers: headers,
        body: jsonEncode({'email': email}),
      );

      if (response['code'] == 0) {
        return {
          'success': true,
          'message': response['data']?['message'] ??
              response['msg'] ??
              '重置密码邮件已发送，请检查您的邮箱',
        };
      } else {
        return {
          'success': false,
          'message': response['msg'] ?? '发送重置密码邮件失败',
        };
      }
    } catch (e) {
      _logger.severe('发送重置密码邮件时发生错误: $e');
      return {
        'success': false,
        'message': '网络连接失败，请检查网络后重试',
      };
    }
  }

  /// 重置密码
  Future<Map<String, dynamic>> resetPassword({
    required String token,
    required String newPassword,
    required String confirmPassword,
  }) async {
    try {
      final headers = await _getHeaders();
      final response = await jsonRequest(
        '$baseUrl/user/reset-password',
        method: 'POST',
        headers: headers,
        body: jsonEncode({
          'token': token,
          'password': newPassword,
          'confirm_password': confirmPassword, // 根据API文档调整参数名
        }),
      );

      if (response['code'] == 0) {
        // 根据API文档，成功时可能返回新的令牌
        final data = response['data'];
        final result = {
          'success': true,
          'message': data?['message'] ?? response['msg'] ?? '密码重置成功',
        };

        // 如果返回了新的访问令牌，保存它们
        if (data != null) {
          if (data['accessToken'] != null) {
            result['accessToken'] = data['accessToken'];
          }
          if (data['refreshToken'] != null) {
            result['refreshToken'] = data['refreshToken'];
          }
          if (data['expiresIn'] != null) {
            result['expiresIn'] = data['expiresIn'];
          }
        }

        return result;
      } else {
        return {
          'success': false,
          'message': response['msg'] ?? '密码重置失败',
        };
      }
    } catch (e) {
      _logger.severe('重置密码时发生错误: $e');
      return {
        'success': false,
        'message': '网络连接失败，请检查网络后重试',
      };
    }
  }

  // 查询支付结果
  Future<Map<String, dynamic>> checkPaymentStatus(String orderId) async {
    try {
      String? token = await _tokenService.getAccessToken();
      if (token == null) {
        final refreshResult = await refreshAccessToken();
        if (refreshResult == null) {
          throw Exception('用户未登录或登录已过期');
        }
        token = refreshResult['accessToken'];
      }

      final headers = await _getHeaders(token: token);
      final response = await jsonRequest(
        '$baseUrl/payment/status/$orderId',
        headers: headers,
      );

      if (response['code'] == 0) {
        // 确保返回的数据包含支付类型信息
        final data = response['data'];
        // 如果返回数据中没有type字段，根据productType推断支付类型
        if (data['type'] == null && data['productType'] != null) {
          data['type'] = data['productType'];
        }
        return data;
      } else {
        throw Exception('查询支付状态失败: ${response['msg']}');
      }
    } catch (e) {
      _logger.severe('查询支付状态时发生错误: $e');
      rethrow;
    }
  }

  // 获取用户支付订单列表
  Future<Map<String, dynamic>> getPaymentOrders(int page,
      {int pageSize = 10}) async {
    try {
      String? token = await _tokenService.getAccessToken();
      if (token == null) {
        final refreshResult = await refreshAccessToken();
        if (refreshResult == null) {
          throw Exception('用户未登录或登录已过期');
        }
        token = refreshResult['accessToken'];
      }

      final headers = await _getHeaders(token: token);
      final response = await jsonRequest(
        '$baseUrl/payment/orders?page=$page&pageSize=$pageSize',
        headers: headers,
      );

      if (response['code'] == 0) {
        return {
          'orders': List<Map<String, dynamic>>.from(response['data']['orders']),
          'hasMore': response['data']['hasMore'] ?? false,
          'total': response['data']['total'] ?? 0,
        };
      } else {
        throw Exception('获取支付订单失败: ${response['msg']}');
      }
    } catch (e) {
      _logger.severe('获取支付订单时发生错误: $e');
      rethrow;
    }
  }

  Future<bool> checkContentPurchase(int postId) async {
    try {
      String? token = await _tokenService.getAccessToken();
      if (token == null) {
        final refreshResult = await refreshAccessToken();
        if (refreshResult == null) {
          return false;
        }
        token = refreshResult['accessToken'];
      }

      final headers = await _getHeaders(token: token);
      final response = await jsonRequest(
        '$baseUrl/user/check-content-purchase/$postId',
        headers: headers,
      );

      if (response['code'] == 0) {
        return response['data']['purchased'] ?? false;
      } else {
        _logger.warning('检查内容购买状态失败: ${response['msg']}');
        return false;
      }
    } catch (e) {
      _logger.severe('检查内容购买状态时发生错误: $e');
      return false;
    }
  }
}
