import 'package:logging/logging.dart';
import '../models/chat_message.dart';
import 'dart:convert';
import 'package:flutter/material.dart';

class LocalNotificationService {
  static final Logger _logger = Logger('LocalNotificationService');
  static final LocalNotificationService _instance = LocalNotificationService._internal();
  factory LocalNotificationService() => _instance;

  LocalNotificationService._internal();

  Future<void> initialize() async {
    _logger.info('初始化本地通知服务');
    // 不需要请求权限，因为我们使用应用内通知
    _logger.info('本地通知服务初始化完成');
  }

  void _onNotificationTap(Map<String, dynamic> payload) {
    _logger.info('用户点击了通知: $payload');
    // 处理通知点击事件
    // 这里可以添加导航逻辑
  }

  Future<void> showChatNotification(ChatMessageData message) async {
    _logger.info('显示聊天通知: ${message.userName}');
    // 使用应用内通知，不需要系统通知
  }

  Future<void> requestPermissions() async {
    _logger.info('本地通知不需要请求系统权限');
    // 使用应用内通知，不需要请求系统权限
  }

  // 显示应用内通知
  void showInAppNotification(BuildContext context, String title, String message) {
    if (context.mounted) {
      _logger.info('显示应用内通知: $title');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(message),
            ],
          ),
          duration: const Duration(seconds: 3),
          action: SnackBarAction(
            label: '关闭',
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    }
  }
}
