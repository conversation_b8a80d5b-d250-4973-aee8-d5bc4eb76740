import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:provider/provider.dart';
import '../../widgets/bottom_navbar.dart';
import '../../widgets/unauthorized_access.dart';
import '../../providers/auth_provider.dart';
import 'chat_room.dart'; // 添加ChatRoomPage的导入

class ChatCheckPage extends StatefulWidget {
  final String? messageId; // 添加messageId参数

  const ChatCheckPage({
    super.key,
    this.messageId, // 可选参数，用于导航到特定消息
  });

  @override
  _ChatCheckPageState createState() => _ChatCheckPageState();
}

class _ChatCheckPageState extends State<ChatCheckPage>
    with WidgetsBindingObserver {
  bool _isLoading = true;
  bool _isAuthorized = false;
  bool _isFirstLoad = true;
  bool _isCheckingAuth = false;
  String? _messageId; // 用于保存消息ID

  @override
  void initState() {
    super.initState();
    // 保存消息ID
    _messageId = widget.messageId;
    // 注册生命周期监听器
    WidgetsBinding.instance.addObserver(this);
    _checkAuthorization();
  }

  @override
  void dispose() {
    // 移除生命周期监听器
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  // 监听应用生命周期变化
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed && !_isCheckingAuth) {
      // 应用从后台恢复到前台时，刷新权限
      _checkAuthorization();
    }
  }

  // 当依赖关系变化时调用（例如从其他页面返回时）
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 检查路由参数中是否有messageId
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args is Map<String, dynamic> && args.containsKey('messageId')) {
      _messageId = args['messageId'];
    }

    // 如果不是首次加载，且当前没有正在检查权限，则刷新权限
    if (!_isFirstLoad && !_isCheckingAuth) {
      _checkAuthorization();
    }
    _isFirstLoad = false;
  }

  Future<void> _checkAuthorization() async {
    // 如果已经在检查权限，则直接返回，避免重复检查
    if (_isCheckingAuth) return;

    setState(() {
      _isLoading = true;
      _isCheckingAuth = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // 确保AuthProvider已初始化
      if (!authProvider.isInitialized) {
        await authProvider.initialize();
      }

      // 强制刷新用户信息，确保获取最新状态
      await authProvider.refreshUserInfo(notify: false);

      if (!mounted) {
        _isCheckingAuth = false;
        return;
      }

      // 获取用户信息和当前时间
      final user = authProvider.user;
      final currentTime = authProvider.getCurrentTimestamp();

      // 详细检查用户权限
      bool hasPermission = false;
      if (authProvider.isAuthenticated && user?.vip != null) {
        final vipLevel = user!.vip['level'] as int? ?? 0;
        final vipEnd = user.vip['end'] as int? ?? 0;

        // 记录详细的权限检查日志
        debugPrint(
            '聊天室权限检查: 用户ID=${user.id}, vipLevel=$vipLevel, vipEnd=$vipEnd, currentTime=$currentTime');

        hasPermission = vipLevel > 0 && vipEnd > currentTime;
      }

      if (!mounted) return;

      setState(() {
        _isAuthorized = hasPermission;
        _isLoading = false;
        _isCheckingAuth = false; // 检查完成，重置标志
      });

      // 只有当确认用户有权限时才导航到聊天室
      if (_isAuthorized && mounted) {
        debugPrint('用户有权限访问聊天室，准备导航');
        // 使用延迟执行，避免在构建过程中导航
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!mounted) return;
          // 使用MaterialPageRoute直接导航，避免再次触发路由守卫
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => ChatRoomPage(messageId: _messageId),
              settings:
                  const RouteSettings(name: '/chat_room'), // 使用不同的路由名称，避免触发路由守卫
            ),
          );
        });
      } else {
        debugPrint('用户无权限访问聊天室，停留在检查页面');
      }
    } catch (e) {
      debugPrint('检查权限时发生错误: $e');
      if (mounted) {
        setState(() {
          _isAuthorized = false;
          _isLoading = false;
          _isCheckingAuth = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: null,
        title: const Center(child: Text('聊天室')),
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(
              child: SpinKitPulsingGrid(
                size: 20,
                color: Colors.grey,
              ),
            )
          : _isAuthorized
              ? Container() // This will be replaced by navigation in _checkAuthorization
              : _buildUnauthorizedContent(context),
      bottomNavigationBar: BottomNavBar(currentIndex: 1, messageId: _messageId),
    );
  }

  Widget _buildUnauthorizedContent(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final isLoggedIn = authProvider.isAuthenticated;

    return RefreshIndicator(
      onRefresh: () async {
        setState(() {
          _isLoading = true;
        });
        await _checkAuthorization();
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: SizedBox(
          height: MediaQuery.of(context).size.height -
              AppBar().preferredSize.height -
              kBottomNavigationBarHeight,
          child: UnauthorizedAccess(
            title: '聊天室访问受限',
            message: isLoggedIn
                ? '您的账户暂无权限访问聊天室，\n 请升级为VIP会员'
                : '聊天室仅对注册会员开放，\n 请先登录或注册',
            contactInfo: '如有疑问，请联系客服',
            buttonText: isLoggedIn ? '升级VIP' : '立即登录/注册',
            onButtonPressed: () {
              if (isLoggedIn) {
                Navigator.pushNamed(context, '/vip');
              } else {
                Navigator.pushNamed(context, '/user/login');
              }
            },
            isLoggedIn: isLoggedIn,
          ),
        ),
      ),
    );
  }
}
