import 'package:flutter/foundation.dart';

class NavigationProvider with ChangeNotifier {
  // 标记用户是否是从首页链接进入的其他页面
  bool _fromHomePage = false;
  
  bool get fromHomePage => _fromHomePage;
  
  // 设置导航来源为首页
  void setFromHomePage(bool value) {
    _fromHomePage = value;
    notifyListeners();
  }
  
  // 当用户点击底部导航栏时，重置导航来源
  void resetNavigation() {
    _fromHomePage = false;
    notifyListeners();
  }
}
