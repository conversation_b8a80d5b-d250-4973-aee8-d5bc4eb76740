import 'dart:convert';

import 'package:estockcafe/config/config.dart';
import 'package:estockcafe/services/token_service.dart';

import 'package:estockcafe/theme.dart';
import 'package:estockcafe/utils/logging.dart';
import 'package:estockcafe/widgets/bottom_navbar.dart';
import 'package:estockcafe/widgets/unauthorized_access.dart';

import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:logging/logging.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import 'live_room.dart';
import 'package:intl/intl.dart';
import 'package:estockcafe/utils/functions.dart';

class LiveCheckPage extends StatefulWidget {
  const LiveCheckPage({super.key});

  @override
  _LiveCheckPageState createState() => _LiveCheckPageState();
}

class _LiveCheckPageState extends State<LiveCheckPage> {
  late Future<Map<String, dynamic>> _liveInfoFuture;
  final String baseUrl = AppConfig.baseUrl;
  final TokenService _tokenService = TokenService();

  final Logger _logger = Logging.getLogger('UserService');
  late AuthProvider _authProvider;

  // 支付相关状态
  List<Map<String, dynamic>> _paymentMethods = [];
  int? _selectedPaymentMethod;
  bool _isLoadingPayment = false;

  // 直播信息缓存
  Map<String, dynamic>? _currentLiveInfo;

  @override
  void initState() {
    super.initState();
    _liveInfoFuture = _fetchLiveInfo();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _authProvider = Provider.of<AuthProvider>(context, listen: false);
      _authProvider.addListener(_onAuthStateChanged);
      // 页面加载完成后自动检查是否可以直接进入直播间
      _autoCheckAndEnterLive();
    });
  }

  @override
  void dispose() {
    _authProvider.removeListener(_onAuthStateChanged);
    super.dispose();
  }

  void _onAuthStateChanged() {
    if (mounted) {
      setState(() {
        _liveInfoFuture = _fetchLiveInfo();
      });
    }
  }

  // 导航到直播间的辅助方法
  void _navigateToLiveRoom() {
    if (!mounted) return;

    String? channelName;
    String? liveUrl;

    // 从缓存的直播信息中获取频道名称和直播URL
    if (_currentLiveInfo != null && _currentLiveInfo!['data'] != null) {
      final data = _currentLiveInfo!['data'];
      channelName = data['channel'] as String?;
      liveUrl = data['liveurl'] as String?;
    }

    _logger.info('导航到直播间，频道: $channelName, URL: $liveUrl');

    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => LiveRoomPage(
          channelName: channelName,
          liveUrl: liveUrl,
        ),
      ),
    );
  }

  // 自动检查并进入直播间
  Future<void> _autoCheckAndEnterLive() async {
    try {
      // 等待直播信息加载完成
      final liveInfo = await _liveInfoFuture;
      _logger.info('直播信息加载完成: $liveInfo');

      if (liveInfo['code'] != 0) {
        _logger.info('直播信息加载失败，不自动进入');
        return;
      }

      // 缓存直播信息
      _currentLiveInfo = liveInfo;

      final data = liveInfo['data'];
      final istLive = data['istLive'];

      // 检查是否有直播（支持布尔值和数字值）
      bool hasLive = false;
      if (istLive is bool) {
        hasLive = istLive;
      } else if (istLive is int) {
        hasLive = istLive > 0;
      }

      if (!hasLive) {
        _logger.info('当前无直播，不自动进入');
        return;
      }

      // 检查用户是否登录
      final user = _authProvider.user;
      if (user == null) {
        _logger.info('用户未登录，不自动进入');
        return;
      }

      _logger.info('基础条件满足，通过API检查直播权限');

      // 延迟一下，让页面完全加载
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        // 静默调用live/go API检查权限
        await _silentCheckLivePermission();
      }
    } catch (e) {
      _logger.severe('自动检查进入直播间时发生错误: $e');
      // 发生错误时不影响正常流程，用户仍可手动点击进入
    }
  }

  // 静默检查直播权限（不显示加载弹窗）
  Future<void> _silentCheckLivePermission() async {
    try {
      String? token = await _tokenService.getAccessToken();
      if (token == null) {
        _logger.info('获取token失败，不自动进入');
        return;
      }

      final response = await httpRequest(
        '$baseUrl/live/go',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final jsonRes = json.decode(response.body);
        _logger.info('静默权限检查结果: $jsonRes');

        if (jsonRes['code'] == 0) {
          // 有权限，直接进入直播间
          _logger.info('用户有权限，自动进入直播间');
          if (mounted) {
            _navigateToLiveRoom();
          }
        } else {
          // 没有权限，停留在check页面
          _logger.info('用户无权限，停留在check页面: ${jsonRes['msg']}');
        }
      } else {
        _logger.warning('权限检查API调用失败: ${response.statusCode}');
      }
    } catch (e) {
      _logger.severe('静默权限检查时发生异常: $e');
      // 发生错误时不影响正常流程
    }
  }

  Future<Map<String, dynamic>> _fetchLiveInfo() async {
    String? token = await _tokenService.getAccessToken();

    if (token == null) {
      return {'error': 'No token available', 'code': -1};
    }

    try {
      final res = await jsonRequest(
        '$baseUrl/live/info',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (res.isNotEmpty && res['code'] == 0) {
        return res;
      } else {
        return {'error': 'Failed to load live info', 'code': -1};
      }
    } catch (e) {
      _logger.severe('Error in _fetchLiveInfo: $e');
      return {'error': 'An error occurred: $e', 'code': -1};
    }
  }

  Future<void> _refreshLiveInfo() async {
    await _authProvider.refreshUserInfo(notify: false);
    setState(() {
      _liveInfoFuture = _fetchLiveInfo();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          leading: const SizedBox.shrink(),
          title: const Center(child: Text('直播间')),
          actions: const [SizedBox(width: 48)]),
      body: RefreshIndicator(
        onRefresh: _refreshLiveInfo,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: MediaQuery.of(context).size.height -
                AppBar().preferredSize.height -
                kBottomNavigationBarHeight,
            child: Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                if (!authProvider.isAuthenticated) {
                  return _buildLoginPrompt();
                }
                return FutureBuilder<Map<String, dynamic>>(
                  future: _liveInfoFuture,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(
                        child: SpinKitPulsingGrid(
                          size: 20,
                          color: Colors.grey,
                        ),
                      );
                    } else if (snapshot.hasError) {
                      return Center(child: Text('发生错误：${snapshot.error}'));
                    } else if (!snapshot.hasData || snapshot.data == null) {
                      return const Center(child: Text('没有直播信息'));
                    }

                    final liveInfo = snapshot.data!;

                    if (liveInfo['error'] != null) {
                      return Center(child: Text('错误：${liveInfo['error']}'));
                    }

                    final user = authProvider.user;
                    if (user == null) {
                      return const Center(child: Text('用户信息不可用'));
                    }

                    final hasUsedLiveToday =
                        user.assets['used_live_today'] == true;

                    if (hasUsedLiveToday) {
                      // 缓存直播信息并导航到直播间
                      _currentLiveInfo = liveInfo;
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        _navigateToLiveRoom();
                      });
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    return _buildAccessView(liveInfo);
                  },
                );
              },
            ),
          ),
        ),
      ),
      bottomNavigationBar: const BottomNavBar(currentIndex: 3),
    );
  }

  Widget _buildLoginPrompt() {
    return UnauthorizedAccess(
      title: '直播间访问受限',
      message: '直播间仅对注册会员开放，\n 请先登录或注册',
      contactInfo: '如有疑问，请联系客服',
      buttonText: '立即登录/注册',
      onButtonPressed: () async {
        final result = await Navigator.pushNamed(context, '/user/login');
        if (result == true) {
          // 登录成功，重新获取直播信息
          setState(() {
            _liveInfoFuture = _fetchLiveInfo();
          });
        }
      },
      isLoggedIn: false,
    );
  }

  Widget _buildAccessView(Map<String, dynamic> liveInfo) {
    final user = _authProvider.user;
    // 检查是否有直播（支持布尔值和数字值）
    final rawIstLive = liveInfo['data']['istLive'];
    bool hasLive = false;
    if (rawIstLive is bool) {
      hasLive = rawIstLive;
    } else if (rawIstLive is int) {
      hasLive = rawIstLive > 0;
    } else {
      // 如果不是布尔值或数字，使用原来的时间计算方法
      hasLive = _isTodayLive(liveInfo['data']) > 0;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('您的直播次数还有：${user?.assets['live'] ?? '未知'} 次',
              style:
                  const TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (user?.assets['live'] > 0)
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryBlue,
                  ),
                  onPressed: () {
                    hasLive ? _goLive() : null;
                  },
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.live_tv, color: Colors.white, size: 14),
                      const SizedBox(width: 5),
                      Text(hasLive ? '进入直播间' : '今日无直播',
                          style: const TextStyle(
                              fontSize: 12, color: Colors.white)),
                    ],
                  ),
                ),
              const SizedBox(width: 10),
              ElevatedButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/live/purchase');
                },
                child: const Text('购买直播次数',
                    style:
                        TextStyle(fontSize: 12, color: AppColors.primaryBlue)),
              ),
            ],
          ),
          const SizedBox(height: 40),
          _buildLiveInfoBox(liveInfo['data']),
        ],
      ),
    );
  }

  // 简化流程：直接尝试进入直播间
  Future<void> _goLive() async {
    _logger.info('_goLive 方法开始执行');

    // 显示加载对话框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SpinKitPulsingGrid(
                color: AppColors.primaryBlue,
                size: 50.0,
              ),
              SizedBox(height: 20),
              Text("正在进入直播间..."),
            ],
          ),
        );
      },
    );

    try {
      String? token = await _tokenService.getAccessToken();
      if (token == null) {
        throw Exception('登录状态失效，请下拉刷新重试');
      }

      final response = await httpRequest(
        '$baseUrl/live/go',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (!mounted) return;
      Navigator.of(context).pop(); // 关闭加载对话框

      if (response.statusCode == 200) {
        final jsonRes = json.decode(response.body);
        _logger.info('live/go 响应结果: $jsonRes');

        if (jsonRes['code'] == 0) {
          // 成功进入直播间，从响应中获取直播信息
          _logger.info('成功进入直播间，响应数据: ${jsonRes['data']}');

          // 从live/go API响应中获取直播URL和频道信息
          final responseData = jsonRes['data'];
          String? liveUrl;
          String? channelName;

          if (responseData != null) {
            liveUrl = responseData['liveurl'] as String?;
            channelName = responseData['channel'] as String?;
          }

          // 如果live/go没有返回URL，使用缓存的live/info数据
          if ((liveUrl == null || liveUrl.isEmpty) &&
              _currentLiveInfo != null) {
            final cachedData = _currentLiveInfo!['data'];
            if (cachedData != null) {
              liveUrl = cachedData['liveurl'] as String?;
              channelName = cachedData['channel'] as String?;
            }
          }

          _logger.info('准备进入直播间 - URL: $liveUrl, 频道: $channelName');

          // 直接导航到直播间，传递URL和频道信息
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => LiveRoomPage(
                channelName: channelName,
                liveUrl: liveUrl,
              ),
            ),
          );
        } else if (jsonRes['code'] == 1001) {
          // 假设1001表示需要支付
          // 需要支付，显示支付弹窗
          _logger.info('需要支付，显示支付弹窗');
          _showPaymentDialog();
        } else {
          // 其他错误
          final errorMessage = jsonRes['msg'] ?? '进入直播间失败';
          _showErrorSnackBar(errorMessage);
        }
      } else {
        _showErrorSnackBar('进入直播间失败，请稍后再试');
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // 确保加载对话框被关闭
        _logger.severe('进入直播间时发生异常: $e');
        _showErrorSnackBar('进入直播间时发生错误，请稍后再试');
      }
    }
  }

  // 第三步：显示支付弹窗
  Future<void> _showPaymentDialog() async {
    _logger.info('显示支付弹窗');

    // 先加载支付方式
    await _loadPaymentMethods();

    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text(
                '购买直播权限',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              content: SizedBox(
                width: double.maxFinite,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '您需要购买直播权限才能进入直播间',
                      style: TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      '选择支付方式：',
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 12),
                    _buildPaymentOptions(setState),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('取消'),
                ),
                ElevatedButton(
                  onPressed:
                      _selectedPaymentMethod != null && !_isLoadingPayment
                          ? () => _processPayment(setState)
                          : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryBlue,
                  ),
                  child: _isLoadingPayment
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Text(
                          '确认支付',
                          style: TextStyle(color: Colors.white),
                        ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // 加载支付方式
  Future<void> _loadPaymentMethods() async {
    try {
      // 获取用户信息
      final user = _authProvider.user;
      final liveCount = user?.assets['live'] as int? ?? 0;

      // 创建支付方式列表，模拟web端的按钮信息
      _paymentMethods = [];

      // 添加直播次数支付选项（如果用户有直播次数）
      if (liveCount > 0) {
        _paymentMethods.add({
          'pay': 'live',
          'paytype': 'live',
          'title': '直播次数支付',
          'subtitle': '可用：$liveCount',
          'icon': null,
          'usertype': 100,
          'do': 'live',
        });
      }

      // 添加微信支付
      _paymentMethods.add({
        'pay': 'wxpay',
        'paytype': '4',
        'title': '微信支付',
        'subtitle': null,
        'icon': null,
        'usertype': 100,
        'do': 'live',
      });

      // 添加余额支付
      _paymentMethods.add({
        'pay': 'yuer',
        'paytype': 'yuer',
        'title': '余额支付',
        'subtitle': null,
        'icon': null,
        'usertype': 100,
        'do': 'live',
      });

      _logger.info('加载支付方式成功，共${_paymentMethods.length}种方式');
    } catch (e) {
      _logger.severe('加载支付方式失败: $e');
      _showErrorSnackBar('加载支付方式失败');
    }
  }

  // 构建支付选项
  Widget _buildPaymentOptions(StateSetter setState) {
    if (_paymentMethods.isEmpty) {
      return const Text('暂无可用支付方式');
    }

    return Column(
      children: _paymentMethods.asMap().entries.map((entry) {
        final index = entry.key;
        final method = entry.value;
        final isSelected = _selectedPaymentMethod == index;
        final isLiveCountPayment = method['pay'] == 'live';

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? AppColors.primaryBlue : Colors.grey[300]!,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ListTile(
            leading: isLiveCountPayment
                ? const Icon(Icons.live_tv, color: AppColors.primaryBlue)
                : method['pay'] == 'wxpay'
                    ? const Icon(Icons.wechat, color: Colors.green)
                    : method['pay'] == 'yuer'
                        ? const Icon(Icons.account_balance_wallet,
                            color: Colors.orange)
                        : const Icon(Icons.payment),
            title: Text(method['title'] ?? ''),
            subtitle:
                method['subtitle'] != null ? Text(method['subtitle']) : null,
            trailing: Radio<int>(
              value: index,
              groupValue: _selectedPaymentMethod,
              onChanged: (value) {
                setState(() {
                  _selectedPaymentMethod = value;
                });
              },
              activeColor: AppColors.primaryBlue,
            ),
            onTap: () {
              setState(() {
                _selectedPaymentMethod = index;
              });
            },
          ),
        );
      }).toList(),
    );
  }

  // 第四步：处理支付
  Future<void> _processPayment(StateSetter setState) async {
    if (_selectedPaymentMethod == null) return;

    setState(() {
      _isLoadingPayment = true;
    });

    try {
      String? token = await _tokenService.getAccessToken();
      if (token == null) {
        throw Exception('登录状态失效');
      }

      // 获取选中的支付方式
      final selectedMethod = _paymentMethods[_selectedPaymentMethod!];

      // 构建支付参数，参考payment_widget.dart的格式
      Map<String, dynamic> paymentParams;

      if (selectedMethod['pay'] == 'live') {
        // 直播次数支付
        paymentParams = {
          'payment_method': 100, // 特殊ID表示直播次数支付
          'type': 'live',
          'live_num': 1, // 默认购买1次
        };
      } else if (selectedMethod['pay'] == 'wxpay') {
        // 微信支付
        paymentParams = {
          'payment_method': 4,
          'type': 'live',
          'live_num': 1, // 默认购买1次
        };
      } else if (selectedMethod['pay'] == 'yuer') {
        // 余额支付
        paymentParams = {
          'payment_method': 99,
          'type': 'live',
          'live_num': 1, // 默认购买1次
        };
      } else {
        throw Exception('不支持的支付方式');
      }

      _logger.info('发送支付请求，参数: $paymentParams');

      final response = await httpRequest(
        '$baseUrl/payment',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(paymentParams),
      );

      setState(() {
        _isLoadingPayment = false;
      });

      if (response.statusCode == 200) {
        final jsonRes = json.decode(response.body);
        _logger.info('支付结果: $jsonRes');

        if (jsonRes['code'] == 0) {
          // 支付成功
          _logger.info('支付成功，准备进入直播间');

          if (mounted) {
            Navigator.of(context).pop(); // 关闭支付弹窗

            // 刷新用户信息
            await _authProvider.refreshUserInfo();

            // 重新尝试进入直播间
            _goLive();
          }
        } else {
          final errorMessage = jsonRes['msg'] ?? '支付失败';
          _showErrorSnackBar(errorMessage);
        }
      } else {
        _showErrorSnackBar('支付请求失败，请稍后再试');
      }
    } catch (e) {
      setState(() {
        _isLoadingPayment = false;
      });
      _logger.severe('支付过程中发生异常: $e');
      _showErrorSnackBar('支付过程中发生错误，请稍后再试');
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message)),
      );
    }
  }

  Widget _buildLiveInfoBox(Map<String, dynamic> liveInfo) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          const Text('直播提醒',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
          const Text('(直播时间均为美国东部时间)'),
          const SizedBox(height: 10),
          Text(
              '当前时间: ${_formatTime(DateTime.now().toUtc().subtract(const Duration(hours: 4)).millisecondsSinceEpoch ~/ 1000)}'),
          Text('开播时间: ${_formatTime(liveInfo['start_time'])}'),
          Text('下一场直播时间: ${_formatTime(liveInfo['next_time'])}'),
        ],
      ),
    );
  }

  int _isTodayLive(Map<String, dynamic> liveInfo) {
    final now = DateTime.now().toUtc().subtract(const Duration(hours: 12));
    final liveTime = DateTime.fromMillisecondsSinceEpoch(
        liveInfo['start_time'] * 1000,
        isUtc: true);
    if (now.year == liveTime.year &&
        now.month == liveTime.month &&
        now.day == liveTime.day) {
      if (now.isBefore(liveTime)) {
        return 1;
      } else {
        return 2;
      }
    } else {
      return 0;
    }
  }

  String _formatTime(dynamic timestamp) {
    if (timestamp == null || timestamp == '') {
      return '无';
    }
    int timeInSeconds;
    if (timestamp is String) {
      timeInSeconds = int.tryParse(timestamp) ?? 0;
    } else if (timestamp is int) {
      timeInSeconds = timestamp;
    } else {
      return '无效时间';
    }

    final dateTime =
        DateTime.fromMillisecondsSinceEpoch(timeInSeconds * 1000, isUtc: true);
    return DateFormat('yyyy-MM-dd HH点').format(dateTime);
  }
}
