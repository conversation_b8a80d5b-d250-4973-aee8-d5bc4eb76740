import 'package:flutter/scheduler.dart';
import 'package:logging/logging.dart';
import '../models/user.dart';
import '../services/user_service.dart';
import '../services/token_service.dart';
import 'package:flutter/material.dart'; // 导入 material.dart，包含 BuildContext
import 'package:provider/provider.dart'; // 导入 Provider
import '../services/global_chat_service.dart'; // 导入 GlobalChatService
import '../services/chat_notification_service.dart'; // 导入 ChatNotificationService
import '../services/navigation_service.dart'; // 导入NavigationService
import 'package:shared_preferences/shared_preferences.dart'; // 导入 SharedPreferences
import '../services/cache_service.dart';
import 'dart:convert';

class AuthProvider with ChangeNotifier {
  final Logger _logger = Logger('AuthProvider');
  final CacheService _cacheService = CacheService();
  final NavigationService _navigationService =
      NavigationService(); // 添加NavigationService实例
  final UserService _userService = UserService();
  final TokenService _tokenService = TokenService();

  bool _isLoading = false;
  bool _isInitialized = false; // 添加初始化标志
  String? _accessToken;
  User? _user;
  bool _chatConnected = false;

  // 添加防抖动机制的时间戳
  static DateTime? _lastCheckAuthTime;
  static DateTime? _lastRefreshUserInfoTime;
  static const Duration _debounceTime = Duration(seconds: 3);

  User? get user => _user;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _accessToken != null;
  bool get isLoggedIn => _user != null;
  bool get isInitialized => _isInitialized; // 添加初始化状态getter
  int get userType => _user?.vip['level'] ?? 0;
  bool get isChatConnected => _chatConnected;
  String? get token => _accessToken;
  Map<String, dynamic>? get userData => _user?.toJson();

  void _setLoading(bool value) {
    if (_isLoading != value) {
      _isLoading = value;
      // 添加一个检查，确保不在构建过程中调用 notifyListeners
      if (SchedulerBinding.instance.schedulerPhase !=
          SchedulerPhase.persistentCallbacks) {
        notifyListeners();
      }
    }
  }

  void setIsBanned(bool isBanned) {
    if (_user != null) {
      // _user = _user!.copyWith(isBanned: isBanned);
      notifyListeners();
    }
  }

  Future<String?> getAccessToken() async {
    return await _tokenService.getAccessToken();
  }

  Future<void> _clearTokens() async {
    await _tokenService.removeTokens();
  }

  Future<bool> _refreshAccessToken({bool force = false}) async {
    try {
      final result = await _userService.refreshAccessToken(forceRefresh: force);
      if (result != null) {
        _logger.info('成功刷新访问令牌');
        if (result['accessToken'] != null) {
          _accessToken = result['accessToken']; // 更新内存中的访问令牌
        }
        return true;
      } else {
        _logger.warning('刷新访问令牌失败');
        return false;
      }
    } catch (e) {
      _logger.severe('刷新访问令牌时发生错误: $e');
      return false;
    }
  }

  Future<void> initialize() async {
    if (_isInitialized) {
      _logger.info('认证提供者已初始化，跳过');
      return;
    }

    try {
      _setLoading(true);

      // 从缓存中恢复用户会话
      _accessToken = await _tokenService.getAccessToken();
      final refreshToken = await _tokenService.getRefreshToken();
      final userDataStr = _cacheService.getData<String>('user_data');

      // 验证我们是否有令牌
      if (_accessToken == null || refreshToken == null) {
        _logger.info('未找到有效令牌，用户未登录');
        _accessToken = null;
        _user = null;
        _isInitialized = true;
        notifyListeners();
        return;
      }

      // 如果有缓存的用户数据，先加载它
      if (userDataStr != null) {
        try {
          final userData = Map<String, dynamic>.from(
              const JsonDecoder().convert(userDataStr));
          _user = User.fromJson(userData);
          _logger.info('从缓存加载用户数据成功');
        } catch (e) {
          _logger.warning('解析缓存的用户数据失败: $e');
        }
      }

      // 验证令牌有效性并获取最新用户信息
      final isTokenValid = await _verifyAndRefreshToken();
      if (!isTokenValid) {
        _logger.warning('令牌无效，清除用户状态');
        _accessToken = null;
        _user = null;
        await _clearUserCache();
      } else if (_user == null) {
        // 如果令牌有效但没有用户数据，尝试获取用户信息
        await _fetchUserInfo();
      }

      _isInitialized = true;
      _logger.info('认证提供者初始化完成: ${isAuthenticated ? '已登录' : '未登录'}');
    } catch (e) {
      _logger.warning('认证提供者初始化失败: $e');
      _accessToken = null;
      _user = null;
      _isInitialized = true;
    } finally {
      _setLoading(false);
    }
  }

  // 新增：验证并刷新令牌
  Future<bool> _verifyAndRefreshToken() async {
    try {
      // 先尝试使用现有令牌获取用户信息
      final user = await _userService.getCurrentUser();
      if (user != null) {
        _user = user;
        return true;
      }

      // 如果失败，尝试刷新令牌
      _logger.info('令牌可能已过期，尝试刷新');
      final refreshSuccess = await _refreshAccessToken(force: true);
      if (refreshSuccess) {
        return true;
      }

      _logger.warning('令牌刷新失败，用户需要重新登录');
      return false;
    } catch (e) {
      _logger.warning('验证令牌时发生错误: $e');
      return false;
    }
  }

  // 新增：获取最新用户信息
  Future<bool> _fetchUserInfo() async {
    try {
      final user = await _userService.getCurrentUser();
      if (user != null) {
        _user = user;

        // 保存到缓存
        await _cacheService.setData(
            'user_data', const JsonEncoder().convert(_user!.toJson()));

        _logger.info('成功获取和缓存用户信息');
        return true;
      }
      return false;
    } catch (e) {
      _logger.warning('获取用户信息失败: $e');
      return false;
    }
  }

  // 新增：清除用户缓存
  Future<void> _clearUserCache() async {
    await _cacheService.removeData('user_token');
    await _cacheService.removeData('user_data');
    await _tokenService.removeTokens();
  }

  Future<bool> login(
      String username, String password, BuildContext context) async {
    if (_isLoading) return false;
    _setLoading(true);

    try {
      final result = await _userService.login(username, password);

      if (result['user'] is User && result['tokens'] is Map<String, dynamic>) {
        _user = result['user'] as User;
        _accessToken = result['tokens']['accessToken'] as String;

        // 保存到缓存
        await _cacheService.setData('user_token', _accessToken!);
        await _cacheService.setData(
            'user_data', const JsonEncoder().convert(_user!.toJson()));

        // 保存用户ID到SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('last_logged_in_user_id', _user!.id.toString());

        // 设置通知服务
        if (context.mounted) {
          final chatNotificationService =
              Provider.of<ChatNotificationService>(context, listen: false);
          chatNotificationService.currentUserId = _user!.id.toString();

          // 检查聊天权限并连接
          await _handleChatConnection(context);
        }

        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _logger.warning('登录失败: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> logout([BuildContext? context]) async {
    if (_isLoading) return;
    _setLoading(true);

    try {
      // 断开聊天连接
      await disconnectFromChat();

      // 清除通知服务数据
      final chatNotificationService = ChatNotificationService();
      await chatNotificationService.clearUnreadMessages();
      chatNotificationService.currentUserId = null;

      // 调用登出API
      await _userService.logout();

      // 清除令牌
      await _tokenService.removeTokens();

      // 清除SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('last_logged_in_user_id');

      // 清除缓存
      await _cacheService.removeData('user_token');
      await _cacheService.removeData('user_data');

      // 清除内存中的用户数据
      _accessToken = null;
      _user = null;
      _chatConnected = false;

      notifyListeners();
    } catch (e) {
      _logger.warning('登出失败: $e');
      // 即使API调用失败，也清除本地状态
      _accessToken = null;
      _user = null;
      _chatConnected = false;
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _handleChatConnection(BuildContext context) async {
    try {
      if (_user == null) return;

      final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final vipLevel = (_user!.vip['level'] as int?) ?? 0;
      final vipEndTime = (_user!.vip['end'] as int?) ?? 0;
      final hasPermission = vipLevel > 0 && vipEndTime > currentTime;

      if (!hasPermission) {
        _logger.info('用户无权限访问聊天室');
        return;
      }

      final globalChatService =
          Provider.of<GlobalChatService>(context, listen: false);
      final userName = _user!.name.isEmpty ? 'User${_user!.id}' : _user!.name;

      await globalChatService.setUserInfo(
        _user!.id.toString(),
        userName,
        _user!.chatroom,
        hasPermission,
      );

      if (!globalChatService.isConnected && hasPermission) {
        await connectToChat(context);
      }
    } catch (e) {
      _logger.warning('处理聊天连接失败: $e');
    }
  }

  Future<bool> connectToChat(BuildContext context) async {
    try {
      if (_user == null || _user!.chatroom.isEmpty) {
        _logger.warning('无法连接聊天室：用户未登录或无聊天室信息');
        return false;
      }

      final globalChatService =
          Provider.of<GlobalChatService>(context, listen: false);
      final channelName = _user!.chatroom['channel'] ?? 'default';
      final userName = _user!.name.isEmpty ? 'User${_user!.id}' : _user!.name;

      final headers = {
        'Authorization': 'Bearer ${_user!.chatroom['token']}',
      };

      await globalChatService.connect(
        channelName: channelName,
        userId: _user!.id.toString(),
        userName: userName,
        headers: headers,
        chatroomInfo: _user!.chatroom,
      );

      _chatConnected = globalChatService.isConnected;
      notifyListeners();
      return _chatConnected;
    } catch (e) {
      _logger.severe('连接聊天室失败: $e');
      _chatConnected = false;
      notifyListeners();
      return false;
    }
  }

  Future<void> disconnectFromChat() async {
    try {
      final globalChatService = GlobalChatService();
      await globalChatService.disconnect();
      await globalChatService.setUserInfo('', '', {}, false);

      _chatConnected = false;
      notifyListeners();
    } catch (e) {
      _logger.warning('断开聊天连接失败: $e');
      _chatConnected = false;
      notifyListeners();
    }
  }

  Future<String> register(
      String username, String email, String password, String nickname) async {
    if (_isLoading) return '操作正在进行中';
    _setLoading(true);
    try {
      final result =
          await _userService.register(username, email, password, nickname);
      _logger.info('注册结果: $result');
      return result;
    } catch (e) {
      _logger.warning('注册失败: $e');
      return '注册失败: $e';
    } finally {
      _setLoading(false);
    }
  }

  /// 发送找回密码邮件
  Future<Map<String, dynamic>> sendResetPasswordEmail(String email) async {
    if (_isLoading) return {'success': false, 'message': '操作正在进行中'};
    _setLoading(true);
    try {
      final result = await _userService.sendResetPasswordEmail(email);
      _logger.info('发送找回密码邮件结果: $result');
      return result;
    } catch (e) {
      _logger.warning('发送找回密码邮件失败: $e');
      return {'success': false, 'message': '发送失败: $e'};
    } finally {
      _setLoading(false);
    }
  }

  /// 重置密码
  Future<Map<String, dynamic>> resetPassword({
    required String token,
    required String newPassword,
    required String confirmPassword,
  }) async {
    if (_isLoading) return {'success': false, 'message': '操作正在进行中'};
    _setLoading(true);
    try {
      final result = await _userService.resetPassword(
        token: token,
        newPassword: newPassword,
        confirmPassword: confirmPassword,
      );
      _logger.info('重置密码结果: $result');

      // 如果重置成功且返回了新的访问令牌，更新用户状态
      if (result['success'] == true) {
        if (result['accessToken'] != null) {
          _accessToken = result['accessToken'];
          await _tokenService.setAccessToken(
            result['accessToken'],
            result['expiresIn'] ?? 3600, // 默认1小时
          );
          if (result['refreshToken'] != null) {
            await _tokenService.setRefreshToken(
              result['refreshToken'],
              7 * 24 * 3600, // 默认7天
            );
          }

          // 获取用户信息
          final user = await _userService.getCurrentUser();
          if (user != null) {
            _user = user;
            await _cacheService.setData(
                'user_data', const JsonEncoder().convert(_user!.toJson()));
            _logger.info('密码重置后自动登录成功');
            notifyListeners();
          }
        }
      }

      return result;
    } catch (e) {
      _logger.warning('重置密码失败: $e');
      return {'success': false, 'message': '重置失败: $e'};
    } finally {
      _setLoading(false);
    }
  }

  Future<void> checkAuthStatus() async {
    if (_isLoading) return;

    // 如果尚未初始化，先初始化
    if (!_isInitialized) {
      await initialize();
    }

    // 添加防抖动机制，避免短时间内多次调用
    final now = DateTime.now();
    if (_lastCheckAuthTime != null &&
        now.difference(_lastCheckAuthTime!).inMilliseconds <
            _debounceTime.inMilliseconds) {
      _logger.info('短时间内重复调用checkAuthStatus，跳过本次调用');
      return;
    }
    _lastCheckAuthTime = now;

    _setLoading(true);
    try {
      String? accessToken = await getAccessToken();
      String? refreshToken = await _tokenService.getRefreshToken();

      // 如果没有刷新令牌，说明用户未登录，清除所有状态
      if (refreshToken == null) {
        _logger.info('刷新令牌不存在，用户未登录');
        _user = null;
        _accessToken = null;
        notifyListeners();
        _setLoading(false);
        return;
      }

      if (accessToken == null) {
        _logger.info('访问令牌不存在，尝试刷新');
        final refreshSuccess = await _refreshAccessToken(force: true);
        if (refreshSuccess) {
          accessToken = await getAccessToken();
        } else {
          // 刷新失败，清除所有认证状态
          _logger.warning('刷新令牌失败，清除用户状态');
          _user = null;
          _accessToken = null;
          await _clearUserCache();
          notifyListeners();
          _setLoading(false);
          return;
        }
      }

      if (accessToken != null) {
        _accessToken = accessToken; // 确保内存中的令牌更新
        final user = await _userService.getCurrentUser();
        if (user != null) {
          _user = user;
          _logger.info('成功获取用户信息');

          // 更新缓存
          await _cacheService.setData(
              'user_data', const JsonEncoder().convert(_user!.toJson()));

          // 检查用户是否有权限访问聊天室
          final currentTime = getCurrentTimestamp();
          final vipLevel = (_user!.vip['level'] as int?) ?? 0;
          final vipEndTime = (_user!.vip['end'] as int?) ?? 0;
          final hasPermission = vipLevel > 0 && vipEndTime > currentTime;

          // 设置聊天通知服务的用户ID
          final chatNotificationService = ChatNotificationService();
          chatNotificationService.currentUserId = _user!.id.toString();
          _logger.info('已设置ChatNotificationService的用户ID: ${_user!.id}');

          // 设置聊天服务用户信息
          final globalChatService = GlobalChatService();
          String userName;
          if (_user!.name.isEmpty) {
            userName = 'User${_user!.id}';
          } else {
            userName = _user!.name;
          }
          await globalChatService.setUserInfo(
            _user!.id.toString(),
            userName,
            _user!.chatroom,
            hasPermission, // 传递权限信息
          );
          _logger.info('已设置GlobalChatService的用户信息，权限状态: $hasPermission');

          // 更新聊天连接状态
          _chatConnected = globalChatService.isConnected && hasPermission;

          // 如果用户有权限但未连接，尝试自动连接聊天室
          if (!_chatConnected && hasPermission) {
            _logger.info('用户已登录且有权限，但聊天室未连接，尝试自动连接...');

            // 使用_navigationService.navigatorKey替代全局navigatorKey
            if (_navigationService.navigatorKey.currentContext != null) {
              try {
                final connected = await connectToChat(
                    _navigationService.navigatorKey.currentContext!);
                _logger.info('自动连接聊天室结果: $connected');
              } catch (e) {
                _logger.warning('自动连接聊天室失败: $e');
              }
            } else {
              _logger.warning('无法获取BuildContext，跳过自动连接聊天室');
            }
          }
        } else {
          // 获取用户信息失败，尝试刷新令牌
          _logger.warning('获取用户信息失败，尝试刷新令牌');
          final refreshSuccess = await _refreshAccessToken(force: true);
          if (refreshSuccess) {
            final refreshedUser = await _userService.getCurrentUser();
            if (refreshedUser != null) {
              _user = refreshedUser;
              await _cacheService.setData(
                  'user_data', const JsonEncoder().convert(_user!.toJson()));
              _logger.info('刷新令牌后成功获取用户信息');
            } else {
              // 仍然无法获取用户信息，清除状态
              _logger.warning('刷新令牌后仍无法获取用户信息，清除用户状态');
              _user = null;
              _accessToken = null;
              await _clearUserCache();
            }
          } else {
            // 刷新令牌失败，清除状态
            _logger.warning('刷新令牌失败，清除用户状态');
            _user = null;
            _accessToken = null;
            await _clearUserCache();
          }
        }
      } else {
        // 无有效访问令牌，清除状态
        _logger.warning('无有效访问令牌，清除用户状态');
        _user = null;
        _accessToken = null;
        await _clearUserCache();
      }

      // 通知监听器状态已更改
      notifyListeners();
    } catch (e) {
      _logger.warning('检查认证状态时发生错误: $e');
      // 发生错误时，清除状态以确保安全
      _user = null;
      _accessToken = null;
      await _clearUserCache();
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> refreshUserInfo({bool notify = true}) async {
    final now = DateTime.now();
    if (_lastRefreshUserInfoTime != null &&
        now.difference(_lastRefreshUserInfoTime!).inMilliseconds <
            _debounceTime.inMilliseconds) {
      _logger.info('短时间内重复调用refreshUserInfo，跳过本次调用');
      return false;
    }
    _lastRefreshUserInfoTime = now;

    _setLoading(true);
    bool success = false;
    try {
      final user = await _userService.getCurrentUser();
      if (user != null) {
        _user = user;
        // 更新缓存
        await _cacheService.setData(
            'user_data', const JsonEncoder().convert(_user!.toJson()));

        _logger.info('用户信息刷新成功');
        success = true;

        // 如果成功获取用户信息，确保通知监听器
        if (notify) {
          notifyListeners();
        }
      } else {
        _logger.warning('刷新用户信息失败，尝试刷新令牌');
        final refreshSuccess = await _refreshAccessToken(force: true);
        if (refreshSuccess) {
          final refreshedUser = await _userService.getCurrentUser();
          if (refreshedUser != null) {
            _user = refreshedUser;
            // 更新缓存
            await _cacheService.setData(
                'user_data', const JsonEncoder().convert(_user!.toJson()));

            _logger.info('刷新令牌后成功获取用户信息');
            success = true;

            // 如果成功获取用户信息，确保通知监听器
            if (notify) {
              notifyListeners();
            }
          } else {
            // 刷新令牌后仍无法获取用户信息，清除状态
            _logger.warning('刷新令牌后仍无法获取用户信息，清除用户状态');
            _user = null;
            _accessToken = null;
            await _clearUserCache();
            if (notify) notifyListeners();
          }
        } else {
          // 刷新令牌失败，清除状态
          _logger.warning('刷新令牌失败，清除用户状态');
          _user = null;
          _accessToken = null;
          await _clearUserCache();
          if (notify) notifyListeners();
        }
      }
    } catch (e) {
      _logger.severe('刷新用户信息时发生错误: $e');
      // 发生错误时，清除状态以确保安全
      _user = null;
      _accessToken = null;
      await _clearUserCache();
      if (notify) notifyListeners();
    } finally {
      _setLoading(false);
    }
    return success;
  }

  int getCurrentTimestamp() {
    return DateTime.now().millisecondsSinceEpoch ~/ 1000;
  }

  Future<bool> canAccessContent(int postId, int memberDown) async {
    if (!isLoggedIn) {
      return false;
    }

    // 检查用户是否已购买内容
    bool hasPurchased = await _userService.checkContentPurchase(postId);
    if (hasPurchased) {
      return true;
    }

    // 检查用户的会员等级是否满足要求
    return userType >= memberDown;
  }

  Future<void> signIn(String token, Map<String, dynamic> userData) async {
    try {
      _setLoading(true);

      _accessToken = token;
      _user = User.fromJson(userData);

      // 保存到缓存
      await _cacheService.setData('user_token', token);
      await _cacheService.setData(
          'user_data', const JsonEncoder().convert(userData));

      _logger.info('用户登录成功');
    } catch (e) {
      _logger.severe('用户登录失败: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> signOut() async {
    try {
      _setLoading(true);

      // 清除内存中的用户数据
      _accessToken = null;
      _user = null;

      // 清除缓存
      await _cacheService.removeData('user_token');
      await _cacheService.removeData('user_data');

      _logger.info('用户登出成功');
    } catch (e) {
      _logger.warning('用户登出失败: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // 检查用户权限
  bool checkPermission(int requiredLevel) {
    if (!isLoggedIn || _user == null) return false;
    return _user!.vip['level'] >= requiredLevel;
  }

  bool canAccessMemberContent(int memberDown) {
    if (!isLoggedIn) return false;
    return userType >= memberDown;
  }
}
