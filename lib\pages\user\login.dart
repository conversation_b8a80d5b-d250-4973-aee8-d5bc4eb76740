import 'package:estockcafe/theme.dart';
import 'package:estockcafe/utils/logging.dart';
import 'package:estockcafe/widgets/bottom_navbar.dart';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  String _login = '';
  String _password = '';
  bool _isLoading = false; // 添加加载状态变量
  bool _obscurePassword = true; // 密码显示开关

  final Logger _logger = Logging.getLogger('LoginPage');

  void _submit() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      // 设置加载状态为true
      if (mounted) {
        setState(() {
          _isLoading = true;
        });
      }

      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      _logger.info('登录中: $_login, $_password');
      try {
        final success = await authProvider.login(_login, _password, context);

        // 检查组件是否仍然挂载
        if (!mounted) return;

        // 设置加载状态为false
        setState(() {
          _isLoading = false;
        });

        if (success) {
          _logger.info('登录成功');
          // 检查是否可以返回上一个页面
          if (Navigator.of(context).canPop()) {
            Navigator.of(context).pop(true);
          } else {
            // 如果没有上一个页面，跳转到主页
            Navigator.of(context).pushReplacementNamed('/');
          }
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('登录失败，请检查您的邮箱和密码')),
          );
        }
      } catch (e) {
        // 检查组件是否仍然挂载
        if (!mounted) return;

        // 设置加载状态为false
        setState(() {
          _isLoading = false;
        });

        _logger.severe('登录失败: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('登录失败，请检查您的邮箱和密码')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('登录'),
      ),
      body: Center(
        child: SingleChildScrollView(
          child: Container(
            padding:
                const EdgeInsets.symmetric(horizontal: 64.0, vertical: 16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const Text(
                  '请登录',
                  style: AppTextStyles.headingStyle,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 44),
                Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      TextFormField(
                        decoration: InputDecoration(
                          labelText: '用户名或邮箱',
                          labelStyle: const TextStyle(color: AppColors.grey),
                          filled: true,
                          fillColor: Colors.grey[100],
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: BorderSide(color: theme.primaryColor),
                          ),
                        ),
                        style: TextStyle(color: theme.primaryColor),
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return '请输入用户名或邮箱';
                          }
                          return null;
                        },
                        onSaved: (value) => _login = value!,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        decoration: InputDecoration(
                          labelText: '密码',
                          labelStyle: const TextStyle(color: AppColors.grey),
                          filled: true,
                          fillColor: Colors.grey[100],
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: BorderSide(color: theme.primaryColor),
                          ),
                          suffixIcon: IconButton(
                            icon: Icon(
                              _obscurePassword
                                  ? Icons.visibility
                                  : Icons.visibility_off,
                              color: AppColors.grey,
                            ),
                            onPressed: () {
                              setState(() {
                                _obscurePassword = !_obscurePassword;
                              });
                            },
                          ),
                        ),
                        style: TextStyle(color: theme.primaryColor),
                        obscureText: _obscurePassword,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return '请输入密码';
                          }
                          return null;
                        },
                        onSaved: (value) => _password = value!,
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _isLoading ? null : _submit, // 禁用按钮当正在加载时
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.primaryColor,
                          foregroundColor: AppColors.primaryWhite,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        child: _isLoading
                            ? const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                          AppColors.primaryWhite),
                                    ),
                                  ),
                                  SizedBox(width: 10),
                                  Text('登录中...')
                                ],
                              )
                            : const Text('登录'),
                      ),
                      const SizedBox(height: 16),
                      TextButton(
                        onPressed: _isLoading
                            ? null
                            : () {
                                // 禁用按钮当正在加载时
                                Navigator.of(context)
                                    .pushNamed('/user/register');
                              },
                        style: TextButton.styleFrom(
                          foregroundColor: theme.primaryColor,
                        ),
                        child: const Text(
                          '还没有账号？点击注册',
                          style: TextStyle(fontSize: 14, color: AppColors.grey),
                        ),
                      ),
                      const SizedBox(height: 8),
                      // 找回密码链接
                      TextButton(
                        onPressed: _isLoading
                            ? null
                            : () {
                                Navigator.of(context)
                                    .pushNamed('/user/forgot-password');
                              },
                        style: TextButton.styleFrom(
                          foregroundColor: theme.primaryColor,
                        ),
                        child: const Text(
                          '忘记密码？',
                          style: TextStyle(fontSize: 14, color: AppColors.grey),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: const BottomNavBar(currentIndex: 4),
    );
  }
}
