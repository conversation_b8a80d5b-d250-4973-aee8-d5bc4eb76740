<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">127.0.0.1</domain>
        <!-- 允许腾讯云视频播放域名的HTTP流量 -->
        <domain includeSubdomains="true">vod2.myqcloud.com</domain>
        <domain includeSubdomains="true">myqcloud.com</domain>
        <!-- 允许腾讯云CDN域名 -->
        <domain includeSubdomains="true">qcloudcdn.com</domain>
        <domain includeSubdomains="true">tcdnlive.com</domain>
        <domain includeSubdomains="true">tlivecdn.com</domain>
        <!-- 添加直播域名支持 -->
        <domain includeSubdomains="true">live.estockcafe.cn</domain>
        <domain includeSubdomains="true">estockcafe.cn</domain>
        <!-- 添加腾讯云直播相关域名 -->
        <domain includeSubdomains="true">liveplay.myqcloud.com</domain>
        <domain includeSubdomains="true">live.myqcloud.com</domain>
        <!-- 添加腾讯云WebRTC信令服务器域名 -->
        <domain includeSubdomains="true">tlivesource.com</domain>
        <domain includeSubdomains="true">webrtc-signal-scheduler.tlivesource.com</domain>
        <!-- 添加腾讯云其他相关域名 -->
        <domain includeSubdomains="true">trtc.io</domain>
        <domain includeSubdomains="true">tencentcloudapi.com</domain>
    </domain-config>
</network-security-config>
