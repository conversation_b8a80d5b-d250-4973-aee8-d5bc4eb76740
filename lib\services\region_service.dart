import 'package:http/http.dart' as http;
import 'package:logging/logging.dart';
import 'dart:convert';

class RegionService {
  static final Logger _logger = Logger('RegionService');
  static bool? _isInChina;

  // 检查用户是否在中国
  static Future<bool> isInChina({bool forceCheck = false}) async {
    // 临时修改：固定返回false，表示地区为us
    _logger.info('临时修改：地区统一为us');
    _isInChina = false;
    return false;

    // 以下是原始代码，暂时注释掉
    /*
    // 如果强制检查或者_isInChina为null，则进行检测
    if (forceCheck || _isInChina == null) {
      _logger.info('开始检测用户地区...');

      _logger.info(forceCheck ? '强制重新检测用户地区' : '检测用户地区');

      // 默认假设在中国（安全起见）
      bool result = true;

      // 使用IP地址查询服务
      try {
        // 尝试多个IP查询服务，提高可靠性
        result = await _checkWithIpApi() || await _checkWithIpInfo();
        _isInChina = result;
      } catch (e) {
        _logger.warning('IP地址查询失败: $e');
        _isInChina = true; // 出错时默认为中国
      }

      return _isInChina!;
    }

    return _isInChina!;
    */
  }

  // 使用ipapi.co查询
  static Future<bool> _checkWithIpApi() async {
    try {
      final response = await http
          .get(
            Uri.parse('https://ipapi.co/json/'),
          )
          .timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final country = data['country'];
        _logger.info('ipapi.co - IP地址所在国家: $country');

        if (country == 'CN') {
          _logger.info('根据IP地址判断为中国地区');
          return true;
        } else {
          _logger.info('根据IP地址判断为非中国地区: $country');
          return false;
        }
      }
    } catch (e) {
      _logger.warning('ipapi.co查询失败: $e');
    }

    return true; // 查询失败默认为中国
  }

  // 使用ipinfo.io查询（备用）
  static Future<bool> _checkWithIpInfo() async {
    try {
      final response = await http
          .get(
            Uri.parse('https://ipinfo.io/json'),
          )
          .timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final country = data['country'];
        _logger.info('ipinfo.io - IP地址所在国家: $country');

        if (country == 'CN') {
          _logger.info('根据IP地址判断为中国地区');
          return true;
        } else {
          _logger.info('根据IP地址判断为非中国地区: $country');
          return false;
        }
      }
    } catch (e) {
      _logger.warning('ipinfo.io查询失败: $e');
    }

    return true; // 查询失败默认为中国
  }

  // 重置检测结果，强制重新检测
  static Future<void> resetDetection() async {
    _isInChina = null;
    _logger.info('地区检测已重置，下次检测将重新查询IP');
  }
}
