class ChatroomInfo {
  final String token;
  final String channel;
  final int pageId;

  ChatroomInfo({
    required this.token,
    required this.channel,
    required this.pageId,
  });

  factory ChatroomInfo.fromJson(Map<String, dynamic> json) {
    return ChatroomInfo(
      token: json['token'] ?? '',
      channel: json['channel'] ?? '',
      pageId: json['page_id'] ?? 0,
    );
  }
}

class LiveroomInfo {
  final String token;
  final String? channel;
  final int pageId;

  LiveroomInfo({
    required this.token,
    this.channel,
    required this.pageId,
  });

  factory LiveroomInfo.fromJson(Map<String, dynamic> json) {
    return LiveroomInfo(
      token: json['token'] ?? '',
      channel: json['channel'],
      pageId: json['page_id'] ?? 0,
    );
  }
}

class LiveInfo {
  final bool isAdmin;
  final bool isBan;
  final double price;

  LiveInfo({
    required this.isAdmin,
    required this.isBan,
    required this.price,
  });

  factory LiveInfo.fromJson(Map<String, dynamic> json) {
    return LiveInfo(
      isAdmin: json['is_admin'] ?? false,
      isBan: json['is_ban'] ?? false,
      price: (json['price'] ?? 0.0).toDouble(),
    );
  }
}

class VipInfo {
  final int level;
  final int end;

  VipInfo({
    required this.level,
    required this.end,
  });

  factory VipInfo.fromJson(Map<String, dynamic> json) {
    return VipInfo(
      level: json['level'] ?? 0,
      end: json['end'] ?? 0,
    );
  }
}

class AssetsInfo {
  final int live;
  final double used;
  final double money;

  AssetsInfo({
    required this.live,
    required this.used,
    required this.money,
  });

  factory AssetsInfo.fromJson(Map<String, dynamic> json) {
    return AssetsInfo(
      live: json['live'] ?? 0,
      used: (json['used'] ?? 0.0).toDouble(),
      money: (json['money'] ?? 0.0).toDouble(),
    );
  }
}

class User {
  final int id;
  final String email;
  final String name;
  final String? avatar;
  final Map<String, dynamic> chatroom;
  final Map<String, dynamic> liveroom;
  final Map<String, dynamic> vip;
  final Map<String, dynamic> live;
  final Map<String, dynamic> assets;

  User({
    required this.id,
    required this.email,
    required this.name,
    this.avatar,
    required this.chatroom,
    required this.liveroom,
    required this.vip,
    required this.live,
    required this.assets,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? 0,
      email: json['email'] ?? '',
      name: json['name'] ?? '',
      avatar: json['avatar'],
      chatroom: Map<String, dynamic>.from(json['chatroom'] ??
          {
            'token': '',
            'channel': '',
            'page_id': 0,
          }),
      liveroom: Map<String, dynamic>.from(json['liveroom'] ??
          {
            'token': '',
            'channel': '',
            'page_id': 0,
          }),
      vip: Map<String, dynamic>.from(json['vip'] ??
          {
            'level': 0,
            'end': 0,
          }),
      live: Map<String, dynamic>.from(json['live'] ??
          {
            'is_ban': false,
            'is_admin': false,
            'price': 0.0,
          }),
      assets: Map<String, dynamic>.from(json['assets'] ??
          {
            'live': 0,
            'used': 0.0,
            'money': 0.0,
          }),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'avatar': avatar,
      'chatroom': chatroom,
      'liveroom': liveroom,
      'vip': vip,
      'live': live,
      'assets': assets,
    };
  }

  bool get isVip => (vip['level'] as int? ?? 0) > 0;

  @override
  String toString() {
    return 'User(id: $id, email: $email, name: $name, avatar: $avatar, chatroom: $chatroom, liveroom: $liveroom, vip: $vip, live: $live, assets: $assets)';
  }
}
