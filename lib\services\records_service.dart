import 'dart:convert';
import 'package:logging/logging.dart';
import '../utils/functions.dart';
import '../config/config.dart';
import '../services/token_service.dart';
import '../services/user_service.dart';
import 'package:flutter/material.dart';

class RecordsService {
  final Logger _logger = Logger('RecordsService');
  final String baseUrl = AppConfig.baseUrl;
  final TokenService _tokenService = TokenService();
  final UserService _userService = UserService();

  // 获取请求头（包含token）
  Future<Map<String, String>> _getHeaders() async {
    final token = await _tokenService.getAccessToken();
    
    final headers = {
      'Content-Type': 'application/json',
    };
    
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    
    return headers;
  }

  // 处理token无效的情况
  Future<bool> _handleTokenError(BuildContext context) async {
    try {
      // 尝试刷新token
      final result = await _userService.refreshAccessToken();
      if (result != null && result['code'] == 0) {
        _logger.info('刷新token成功');
        return true;
      } else {
        _logger.warning('刷新token失败: ${result?['msg'] ?? '未知错误'}');
        // 如果刷新失败，跳转到登录页面
        Navigator.of(context).pushReplacementNamed('user/login');
        return false;
      }
    } catch (e) {
      _logger.severe('刷新token异常: $e');
      // 如果刷新失败，跳转到登录页面
      Navigator.of(context).pushReplacementNamed('user/login');
      return false;
    }
  }

  // 获取购买记录
  Future<Map<String, dynamic>> getPurchaseRecords({int page = 1, int limit = 10, BuildContext? context}) async {
    try {
      final headers = await _getHeaders();
      final response = await jsonRequest(
        '$baseUrl/records/purchase',
        method: 'POST',
        headers: headers,
        body: jsonEncode({'page': page, 'limit': limit}),
      );
      
      _logger.fine('获取购买记录响应: $response');
      
      if (response['code'] == 0) {
        return response['data'] ?? {'records': [], 'total': 0};
      } else if (response['code'] == 401 && context != null) {
        // token无效，尝试刷新
        final refreshSuccess = await _handleTokenError(context);
        if (refreshSuccess) {
          // 刷新成功，重新获取记录
          return getPurchaseRecords(page: page, limit: limit, context: context);
        } else {
          return {
            'success': false,
            'message': '登录已过期，请重新登录',
            'records': [],
            'total': 0
          };
        }
      } else {
        _logger.warning('获取购买记录失败: ${response['msg']}');
        return {
          'success': false,
          'message': response['msg'] ?? '获取数据失败，请稍后重试',
          'records': [],
          'total': 0
        };
      }
    } catch (e) {
      _logger.severe('获取购买记录异常: $e');
      return {
        'success': false,
        'message': '网络异常，请检查网络连接',
        'records': [],
        'total': 0
      };
    }
  }

  // 获取会员记录
  Future<Map<String, dynamic>> getVipRecords({int page = 1, int limit = 10, BuildContext? context}) async {
    try {
      final headers = await _getHeaders();
      final response = await jsonRequest(
        '$baseUrl/records/vip',
        method: 'POST',
        headers: headers,
        body: jsonEncode({'page': page, 'limit': limit}),
      );
      
      _logger.fine('获取会员记录响应: $response');
      
      if (response['code'] == 0) {
        return response['data'] ?? {'records': [], 'total': 0};
      } else if (response['code'] == 401 && context != null) {
        // token无效，尝试刷新
        final refreshSuccess = await _handleTokenError(context);
        if (refreshSuccess) {
          // 刷新成功，重新获取记录
          return getVipRecords(page: page, limit: limit, context: context);
        } else {
          return {
            'success': false,
            'message': '登录已过期，请重新登录',
            'records': [],
            'total': 0
          };
        }
      } else {
        _logger.warning('获取会员记录失败: ${response['msg']}');
        return {
          'success': false,
          'message': response['msg'] ?? '获取数据失败，请稍后重试',
          'records': [],
          'total': 0
        };
      }
    } catch (e) {
      _logger.severe('获取会员记录异常: $e');
      return {
        'success': false,
        'message': '网络异常，请检查网络连接',
        'records': [],
        'total': 0
      };
    }
  }

  // 获取直播记录
  Future<Map<String, dynamic>> getLiveRecords({int page = 1, int limit = 10, BuildContext? context}) async {
    try {
      final headers = await _getHeaders();
      final response = await jsonRequest(
        '$baseUrl/records/live',
        method: 'POST',
        headers: headers,
        body: jsonEncode({'page': page, 'limit': limit}),
      );
      
      _logger.fine('获取直播记录响应: $response');
      
      if (response['code'] == 0) {
        return response['data'] ?? {'records': [], 'total': 0};
      } else if (response['code'] == 401 && context != null) {
        // token无效，尝试刷新
        final refreshSuccess = await _handleTokenError(context);
        if (refreshSuccess) {
          // 刷新成功，重新获取记录
          return getLiveRecords(page: page, limit: limit, context: context);
        } else {
          return {
            'success': false,
            'message': '登录已过期，请重新登录',
            'records': [],
            'total': 0
          };
        }
      } else {
        _logger.warning('获取直播记录失败: ${response['msg']}');
        return {
          'success': false,
          'message': response['msg'] ?? '获取数据失败，请稍后重试',
          'records': [],
          'total': 0
        };
      }
    } catch (e) {
      _logger.severe('获取直播记录异常: $e');
      return {
        'success': false,
        'message': '网络异常，请检查网络连接',
        'records': [],
        'total': 0
      };
    }
  }

  // 获取充值记录
  Future<Map<String, dynamic>> getRechargeRecords({int page = 1, int limit = 10, BuildContext? context}) async {
    try {
      final headers = await _getHeaders();
      final response = await jsonRequest(
        '$baseUrl/records/recharge',
        method: 'POST',
        headers: headers,
        body: jsonEncode({'page': page, 'limit': limit}),
      );
      
      _logger.fine('获取充值记录响应: $response');
      
      if (response['code'] == 0) {
        return response['data'] ?? {'records': [], 'total': 0};
      } else if (response['code'] == 401 && context != null) {
        // token无效，尝试刷新
        final refreshSuccess = await _handleTokenError(context);
        if (refreshSuccess) {
          // 刷新成功，重新获取记录
          return getRechargeRecords(page: page, limit: limit, context: context);
        } else {
          return {
            'success': false,
            'message': '登录已过期，请重新登录',
            'records': [],
            'total': 0
          };
        }
      } else {
        _logger.warning('获取充值记录失败: ${response['msg']}');
        return {
          'success': false,
          'message': response['msg'] ?? '获取数据失败，请稍后重试',
          'records': [],
          'total': 0
        };
      }
    } catch (e) {
      _logger.severe('获取充值记录异常: $e');
      return {
        'success': false,
        'message': '网络异常，请检查网络连接',
        'records': [],
        'total': 0
      };
    }
  }
}
