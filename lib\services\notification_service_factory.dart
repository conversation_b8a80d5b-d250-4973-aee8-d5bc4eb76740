import 'package:logging/logging.dart';
import 'notification_service.dart';
import 'local_notification_service.dart';
import 'region_service.dart';

class NotificationServiceFactory {
  static final Logger _logger = Logger('NotificationServiceFactory');
  static NotificationService? _firebaseNotificationService;
  static LocalNotificationService? _localNotificationService;

  static Future<dynamic> getNotificationService() async {
    final isInChina = await RegionService.isInChina();

    if (isInChina) {
      _logger.info('用户在中国，使用本地通知服务');
      return await getLocalNotificationService();
    } else {
      _logger.info('用户不在中国，使用Firebase通知服务');
      _firebaseNotificationService ??= NotificationService();
      await _firebaseNotificationService!.initialize();
      return _firebaseNotificationService!;
    }
  }

  static Future<LocalNotificationService> getLocalNotificationService() async {
    _logger.info('初始化本地通知服务');
    _localNotificationService ??= LocalNotificationService();
    await _localNotificationService!.initialize();
    return _localNotificationService!;
  }
}
