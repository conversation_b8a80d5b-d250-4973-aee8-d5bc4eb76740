import 'package:flutter/widgets.dart';

// 一个简单的导航服务，用于获取当前路由
class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;

  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  String? _currentRoute;

  NavigationService._internal();

  // 获取当前路由
  String? get currentRoute => _currentRoute;

  // 设置当前路由
  void setCurrentRoute(String route) {
    _currentRoute = route;
  }

  // 导航到指定路由
  Future<dynamic> navigateTo(String routeName, {Object? arguments}) {
    return navigatorKey.currentState!
        .pushNamed(routeName, arguments: arguments);
  }

  // 返回
  void goBack() {
    return navigatorKey.currentState!.pop();
  }
}
