import 'package:flutter/material.dart';
import '../../widgets/bottom_navbar.dart';

class LiveUnauthorizedPage extends StatelessWidget {
  const LiveUnauthorizedPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('直播室'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('您需要购买直播次数才能进入直播室'),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                // 导航到购买直播次数的页面
                Navigator.pushNamed(context, '/live/purchase');
              },
              child: const Text('购买直播次数'),
            ),
          ],
        ),
      ),
      bottomNavigationBar: const BottomNavBar(currentIndex: 3),
    );
  }
}
