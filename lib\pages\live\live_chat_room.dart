import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:provider/provider.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:image_picker/image_picker.dart';

import '../../models/chat_message.dart';
import '../../providers/auth_provider.dart';
import '../../services/chat_database_service.dart';
import '../../services/live_chat_service.dart';
import '../../theme.dart';
import '../chat/chat_input.dart';
import '../chat/chat_message.dart' as chat_widget;

class LiveChatRoom extends StatefulWidget {
  final String channelName;

  const LiveChatRoom({
    Key? key,
    required this.channelName,
  }) : super(key: key);

  @override
  State<LiveChatRoom> createState() => _LiveChatRoomState();
}

class _LiveChatRoomState extends State<LiveChatRoom> {
  final _logger = Logger('LiveChatRoom');
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();
  List<ChatMessageData> _messages = [];
  ChatMessageData? _replyTo;
  bool _isConnected = false;
  bool _isLoading = false;
  bool _liveChatSoundEnabled = false; // 直播聊天室声音默认关闭
  late LiveChatService _liveChatService; // 专用于直播间的聊天服务
  late AuthProvider _authProvider;
  StreamSubscription? _messageSubscription;
  StreamSubscription? _connectionSubscription;

  // 音频播放器
  AudioPlayer? _audioPlayer;
  bool _isAudioPlayerReady = false;

  // 图片选择相关
  final List<String> _selectedImages = [];
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _logger.info('LiveChatRoom: 初始化，频道: ${widget.channelName}');

    _isLoading = true;
    _authProvider = Provider.of<AuthProvider>(context, listen: false);

    // 使用独立的直播聊天服务
    _liveChatService = Provider.of<LiveChatService>(context, listen: false);

    // 检查连接状态
    _isConnected = _liveChatService.isConnected;

    // 加载直播聊天室声音设置
    _loadLiveChatSoundSettings();

    // 初始化音频播放器
    _initAudioPlayer();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeLiveChatRoom();
    });
  }

  // 加载直播聊天室声音设置
  Future<void> _loadLiveChatSoundSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _liveChatSoundEnabled =
            prefs.getBool('live_chat_sound_enabled') ?? false; // 默认关闭
      });
      _logger.info('直播聊天室声音设置已加载: $_liveChatSoundEnabled');
    } catch (e) {
      _logger.warning('加载直播聊天室声音设置失败: $e');
    }
  }

  // 保存直播聊天室声音设置
  Future<void> _saveLiveChatSoundSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('live_chat_sound_enabled', _liveChatSoundEnabled);
      _logger.info('直播聊天室声音设置已保存: $_liveChatSoundEnabled');
    } catch (e) {
      _logger.warning('保存直播聊天室声音设置失败: $e');
    }
  }

  // 初始化音频播放器
  Future<void> _initAudioPlayer() async {
    try {
      _audioPlayer = AudioPlayer();
      _isAudioPlayerReady = true;
      _logger.info('直播聊天室音频播放器初始化成功');
    } catch (e) {
      _logger.severe('直播聊天室音频播放器初始化失败: $e');
      _isAudioPlayerReady = false;
    }
  }

  // 播放通知声音
  Future<void> _playNotificationSound() async {
    // 检查声音设置是否开启，如果关闭则不播放声音
    if (!_isAudioPlayerReady ||
        _audioPlayer == null ||
        !_liveChatSoundEnabled) {
      _logger.info('直播聊天室声音设置已关闭或音频播放器未就绪，不播放声音');
      return;
    }

    try {
      // 使用资源文件播放声音
      await _audioPlayer!.play(AssetSource('sounds/notification.wav'));
      _logger.info('播放直播聊天室通知声音');
    } catch (e) {
      _logger.warning('播放直播聊天室通知声音失败: $e');
      // 如果播放失败，尝试重新初始化音频播放器
      _initAudioPlayer();
    }
  }

  // 图片选择方法
  Future<List<String>> _pickImage() async {
    try {
      final pickedFiles = await _picker.pickMultiImage(
        imageQuality: 80, // 压缩图片质量以减小文件大小
      );

      if (pickedFiles.isEmpty) {
        return [];
      }

      List<String> base64Images = [];

      // 处理每张图片
      for (int i = 0; i < pickedFiles.length; i++) {
        final pickedFile = pickedFiles[i];

        // 读取图片文件并转换为base64
        final bytes = await pickedFile.readAsBytes();
        final base64Image = base64Encode(bytes);

        // 获取文件扩展名
        final fileExt = pickedFile.path.split('.').last.toLowerCase();
        final mimeType = 'image/$fileExt';

        // 创建base64图片URL
        final base64Url = 'data:$mimeType;base64,$base64Image';
        base64Images.add(base64Url);
      }

      // 添加到选择的图片列表
      setState(() {
        _selectedImages.addAll(base64Images);
      });

      _logger.info('直播聊天室选择了${base64Images.length}张图片');
      return base64Images;
    } catch (e) {
      _logger.severe('直播聊天室选择图片失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('选择图片失败: $e')),
        );
      }
      return [];
    }
  }

  Future<void> _initializeLiveChatRoom() async {
    try {
      _logger.info('初始化直播聊天室，频道: ${widget.channelName}');

      // 连接到指定的直播频道
      await _connectToLiveChannel();

      // 加载历史消息
      await _loadHistoryMessages();

      // 设置消息监听
      _setupMessageListeners();

      // 同步LiveChatService中已有的消息
      if (_liveChatService.messages.isNotEmpty) {
        _logger
            .info('同步LiveChatService中的${_liveChatService.messages.length}条消息');
        if (mounted) {
          setState(() {
            // 合并消息，避免重复
            for (final serviceMessage in _liveChatService.messages) {
              bool exists = _messages.any((m) {
                if (serviceMessage.msgId != null && m.msgId != null) {
                  return m.msgId == serviceMessage.msgId;
                }
                if (serviceMessage.id.isNotEmpty && m.id.isNotEmpty) {
                  return m.id == serviceMessage.id;
                }
                return m.content == serviceMessage.content &&
                    m.userId == serviceMessage.userId &&
                    (m.createTime ?? 0) == (serviceMessage.createTime ?? 0);
              });

              if (!exists) {
                _messages.add(serviceMessage);
              }
            }

            // 按时间排序
            _messages.sort((a, b) {
              final timeA =
                  a.createTime ?? (a.timestamp.millisecondsSinceEpoch ~/ 1000);
              final timeB =
                  b.createTime ?? (b.timestamp.millisecondsSinceEpoch ~/ 1000);
              return timeA.compareTo(timeB);
            });
          });
        }
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      _logger.severe('初始化直播聊天室失败: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _connectToLiveChannel() async {
    try {
      _logger.info('连接到直播频道: ${widget.channelName}');

      final user = _authProvider.user;
      if (user == null) {
        _logger.warning('用户未登录，无法连接直播聊天室');
        return;
      }

      // 检查当前连接的频道是否与目标频道不同
      if (_liveChatService.isConnected &&
          _liveChatService.channelName != widget.channelName) {
        _logger.info(
            '🔄 当前连接频道: ${_liveChatService.channelName}, 目标频道: ${widget.channelName}');
        _logger.info('🔄 频道不同，先断开当前连接');
        await _liveChatService.disconnect();

        // 等待断开完成
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // 调试用户信息
      _logger.info('🔍 调试用户信息:');
      _logger.info('  - 用户ID: ${user.id}');
      _logger.info('  - 用户名: ${user.name}');
      _logger.info('  - 直播权限: ${user.live}');
      _logger.info('  - has_permission: ${user.live['has_permission']}');
      _logger.info('  - 聊天室信息: ${user.chatroom}');
      _logger.info('  - 全局聊天室频道: ${user.chatroom['channel']}');
      _logger.info('  - 直播频道: ${widget.channelName}');
      _logger.info(
          '🚨 频道冲突检查: ${user.chatroom['channel'] == widget.channelName ? "存在冲突!" : "无冲突"}');

      // 先更新用户信息 - 临时给予权限用于调试
      final hasPermission = user.live['has_permission'] == true;
      _logger.info('🔑 计算的权限状态: $hasPermission');

      await _liveChatService.updateUserInfo(
        userId: user.id.toString(),
        userName: user.name.isEmpty ? 'User${user.id}' : user.name,
        hasPermission: true, // 临时强制给予权限用于调试
        chatroomInfo: user.chatroom,
      );

      _logger.info('✅ 用户信息更新完成，强制权限: true');
      _logger.info('🚨 重要：直播聊天室使用独立的WebSocket连接，不会影响全局聊天室');

      // 连接到直播频道
      _logger.info('🔗 开始连接到直播频道: ${widget.channelName}');

      // 构建认证头，使用用户的聊天室token
      final headers = {
        'Authorization': 'Bearer ${user.chatroom['token']}',
      };
      _logger.info(
          '🔑 使用认证令牌: ${user.chatroom['token']?.toString().substring(0, 8)}...');

      await _liveChatService.connect(
        channelName: widget.channelName,
        userId: user.id.toString(),
        userName: user.name.isEmpty ? 'User${user.id}' : user.name,
        headers: headers,
        chatroomInfo: user.chatroom,
      );

      if (mounted) {
        setState(() {
          _isConnected = _liveChatService.isConnected;
        });
      }

      _logger.info(
          '直播频道连接结果: ${_liveChatService.isConnected}, 当前频道: ${_liveChatService.channelName}');
    } catch (e) {
      _logger.severe('连接直播频道失败: $e');
    }
  }

  Future<void> _loadHistoryMessages() async {
    try {
      _logger.info('加载直播频道历史消息（增量同步）: ${widget.channelName}');

      final user = _authProvider.user;
      if (user == null) {
        _logger.warning('用户未登录，无法加载历史消息');
        return;
      }

      // 先从本地数据库加载现有消息
      final dbService = ChatDatabaseService(
        userId: user.id.toString(),
        channelName: widget.channelName,
      );

      // 获取本地数据库中的消息
      final localMessages = await dbService.getLatestMessages(
        widget.channelName,
        limit: 200, // 增加本地消息加载数量
      );

      _logger.info('从本地数据库加载了${localMessages.length}条消息');

      // 先显示本地消息
      if (mounted && localMessages.isNotEmpty) {
        setState(() {
          _messages = localMessages;
        });

        // 滚动到底部
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_scrollController.hasClients) {
            _scrollController.animateTo(
              _scrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        });
      }

      // 然后请求服务器增量消息
      if (_liveChatService.isConnected) {
        _logger.info('请求服务器增量消息');
        try {
          // 使用增量同步请求历史消息
          await _liveChatService.requestHistoryMessages(limit: 50);

          // 等待一段时间让服务器响应
          await Future.delayed(const Duration(seconds: 2));

          // 获取服务中的所有消息（包括新同步的）
          final allMessages = _liveChatService.messages;
          _logger.info('服务器同步后总共有${allMessages.length}条消息');

          // 添加详细的消息预览
          if (allMessages.isNotEmpty) {
            _logger.info('📋 LiveChatService消息列表预览:');
            for (int i = 0; i < allMessages.length && i < 3; i++) {
              final msg = allMessages[i];
              _logger.info(
                  '  [$i] ${msg.userName}: ${msg.content} (时间: ${msg.createTime})');
            }
            if (allMessages.length > 3) {
              _logger.info('  ... 还有${allMessages.length - 3}条消息');
            }
          }

          // 更新界面显示
          if (mounted) {
            setState(() {
              // 清空现有消息并添加所有消息
              _messages.clear();
              _messages.addAll(allMessages);

              // 按时间排序
              _messages.sort((a, b) {
                final timeA = a.createTime ??
                    (a.timestamp.millisecondsSinceEpoch ~/ 1000);
                final timeB = b.createTime ??
                    (b.timestamp.millisecondsSinceEpoch ~/ 1000);
                return timeA.compareTo(timeB);
              });

              // 添加界面消息列表状态日志
              _logger.info('🖥️ 界面消息列表已更新，共${_messages.length}条消息');
              if (_messages.isNotEmpty) {
                _logger.info('🖥️ 界面消息列表预览:');
                for (int i = 0; i < _messages.length && i < 3; i++) {
                  final msg = _messages[i];
                  _logger.info(
                      '  [$i] ${msg.userName}: ${msg.content} (时间: ${msg.createTime})');
                }
                if (_messages.length > 3) {
                  _logger.info('  ... 还有${_messages.length - 3}条消息');
                }
              }
            });

            // 滚动到底部
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (_scrollController.hasClients) {
                _scrollController.animateTo(
                  _scrollController.position.maxScrollExtent,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeOut,
                );
              }
            });
          }
        } catch (e) {
          _logger.warning('从服务器获取增量消息失败: $e');
        }
      } else {
        _logger.warning('直播聊天服务未连接，只显示本地消息');
      }
    } catch (e) {
      _logger.severe('加载历史消息失败: $e');
    }
  }

  void _setupMessageListeners() {
    // 监听新消息
    _messageSubscription = _liveChatService.messageStream.listen(
      (message) {
        _logger.info(
            '🔔 LiveChatRoom收到消息: ${message.content}, 频道: ${message.channelName}, 当前频道: ${widget.channelName}');

        if (mounted && message.channelName == widget.channelName) {
          _logger.info('✅ 消息频道匹配，检查是否需要添加到列表: ${message.content}');

          // 检查消息是否已存在，避免重复
          bool messageExists = _messages.any((m) {
            // 优先使用msgId进行比较
            if (message.msgId != null && m.msgId != null) {
              return m.msgId == message.msgId;
            }
            // 如果没有msgId，使用id进行比较
            if (message.id.isNotEmpty && m.id.isNotEmpty) {
              return m.id == message.id;
            }
            // 最后使用内容和时间戳进行比较
            return m.content == message.content &&
                m.userId == message.userId &&
                (m.createTime ?? 0) == (message.createTime ?? 0);
          });

          if (!messageExists) {
            setState(() {
              _messages.add(message);
            });
            _logger.info('📝 消息已添加到列表，当前消息列表长度: ${_messages.length}');
          } else {
            _logger.info('⚠️ 消息已存在，跳过添加: ${message.content}');
          }

          _logger.info('📝 当前消息列表长度: ${_messages.length}');

          // 播放声音通知（仅在直播聊天室声音开启时）
          if (_liveChatSoundEnabled) {
            _logger.info('🔊 直播聊天室声音已开启，播放消息提示音');
            _playNotificationSound();
          } else {
            _logger.info('🔇 直播聊天室声音已关闭，不播放提示音');
          }

          // 自动滚动到底部
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (_scrollController.hasClients) {
              _scrollController.animateTo(
                _scrollController.position.maxScrollExtent,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeOut,
              );
            }
          });
        } else {
          _logger.warning(
              '❌ 消息频道不匹配或组件未挂载: 消息频道=${message.channelName}, 当前频道=${widget.channelName}, mounted=$mounted');
        }
      },
      onError: (error) {
        _logger.severe('消息流监听错误: $error');
      },
    );

    // 监听连接状态
    _connectionSubscription = _liveChatService.connectionStream.listen(
      (isConnected) {
        if (mounted) {
          setState(() {
            _isConnected = isConnected;
          });
        }
      },
      onError: (error) {
        _logger.severe('连接状态监听错误: $error');
      },
    );
  }

  Future<void> _sendMessage(String content, {List<String>? images}) async {
    if (content.isEmpty &&
        (images == null || images.isEmpty) &&
        _selectedImages.isEmpty) {
      return;
    }

    try {
      final user = _authProvider.user;
      if (user == null) {
        _logger.warning('用户未登录，无法发送消息');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('请先登录')),
          );
        }
        return;
      }

      // 检查连接状态
      if (!_liveChatService.isConnected) {
        _logger.warning('聊天服务未连接，无法发送消息');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('聊天服务未连接，请稍后重试')),
          );
        }
        return;
      }

      _logger.info('📤 LiveChatRoom发送消息: "$content"');
      _logger.info('📡 当前连接频道: ${_liveChatService.channelName}');
      _logger.info('📡 LiveChatRoom频道: ${widget.channelName}');

      // 构建符合web端格式的消息
      final message = {
        'content': content,
        'type': 'msg',
        'user_id': user.id.toString(),
        'user_name': user.name.isEmpty ? 'User${user.id}' : user.name,
        'create_time': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        'msg_id':
            '${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecond % 1000}',
        'token': user.chatroom['token']?.toString() ?? '',
        'channel_name': widget.channelName,
      };

      // 使用传入的图片或已选择的图片，避免重复
      final allImages = images != null && images.isNotEmpty ? images : _selectedImages;

      // 如果有图片，添加到消息中
      if (allImages.isNotEmpty) {
        message['pics'] = allImages;
        _logger.info('直播聊天室发送包含${allImages.length}张图片的消息');
      }

      // 如果是管理员，添加admin标识
      if (user.live['is_admin'] == true) {
        message['admin'] = 1;
      }

      // 添加回复消息处理
      if (_replyTo != null) {
        message['reply'] = {
          'msg_id': _replyTo!.id,
          'user_id': _replyTo!.userId.toString(),
          'user_name': _replyTo!.userName,
          'content': _replyTo!.content,
          'create_time': _replyTo!.timestamp.millisecondsSinceEpoch ~/ 1000,
        };
        _logger.info('添加回复消息数据: ${_replyTo!.content}');
      }

      // 发送消息
      await _liveChatService.sendCustomMessage(message);

      _logger.info('✅ 消息发送完成');

      _messageController.clear();
      setState(() {
        _replyTo = null;
        _selectedImages.clear(); // 清空选择的图片
      });
    } catch (e) {
      _logger.severe('发送消息失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('发送消息失败: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _messageSubscription?.cancel();
    _connectionSubscription?.cancel();
    _scrollController.dispose();
    _messageController.dispose();

    // 清理音频播放器
    if (_audioPlayer != null) {
      _audioPlayer!.dispose();
      _audioPlayer = null;
      _isAudioPlayerReady = false;
    }

    // 断开直播聊天服务连接
    if (_liveChatService.isConnected) {
      _liveChatService.disconnect();
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.max, // 修改：使用最大尺寸，占满所有可用空间
        children: [
          // 聊天室标题栏 - 使用固定高度
          Container(
            height: 48, // 固定高度，避免动态计算
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.chat_bubble_outline,
                  size: 18, // 稍微减小图标尺寸
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 6),
                Text(
                  '直播聊天室',
                  style: TextStyle(
                    fontSize: 13, // 稍微减小字体
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[800],
                  ),
                ),
                const Spacer(),
                // 声音设置按钮 - 减小尺寸
                SizedBox(
                  width: 32,
                  height: 32,
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    icon: Icon(
                      _liveChatSoundEnabled
                          ? Icons.volume_up
                          : Icons.volume_off,
                      size: 18,
                      color: _liveChatSoundEnabled ? Colors.blue : Colors.grey,
                    ),
                    onPressed: () {
                      setState(() {
                        _liveChatSoundEnabled = !_liveChatSoundEnabled;
                      });
                      _saveLiveChatSoundSettings();

                      // 显示提示
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(_liveChatSoundEnabled
                              ? '直播聊天声音已开启'
                              : '直播聊天声音已关闭'),
                          duration: const Duration(seconds: 1),
                        ),
                      );
                    },
                    tooltip: _liveChatSoundEnabled ? '关闭声音' : '开启声音',
                  ),
                ),
                const SizedBox(width: 6),
                Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: _isConnected ? Colors.green : Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  _isConnected ? '已连接' : '未连接',
                  style: TextStyle(
                    fontSize: 11, // 减小字体
                    color: _isConnected ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
          ),

          // 消息列表 - 使用Expanded占满剩余空间
          Expanded(
            child: _isLoading
                ? const Center(
                    child: SpinKitPulsingGrid(
                      color: AppColors.primaryBlue,
                      size: 30, // 减小加载动画尺寸
                    ),
                  )
                : ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.symmetric(vertical: 4), // 减小内边距
                    itemCount: _messages.length,
                    itemBuilder: (context, index) {
                      final message = _messages[index];
                      return chat_widget.ChatMessage(
                        message: message,
                      );
                    },
                  ),
          ),

          // 输入框 - 使用固定高度，添加底部安全区域
          if (_authProvider.user != null)
            Container(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).padding.bottom, // 添加底部安全区域
              ),
              child: ChatInput(
                controller: _messageController,
                onSendMessage: _sendMessage,
                onPickImage: _pickImage,
                selectedImages: _selectedImages,
                onRemoveImage: (index) {
                  setState(() {
                    _selectedImages.removeAt(index);
                  });
                },
                replyTo: _replyTo,
                onCancelReply: () {
                  setState(() {
                    _replyTo = null;
                  });
                },
                isDisabled: !_isConnected,
              ),
            ),
        ],
      ),
    );
  }
}
