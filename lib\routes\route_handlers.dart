import 'package:flutter/material.dart';

class SearchPage extends StatelessWidget {
  const SearchPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('搜索')),
      body: const Center(child: Text('搜索页面')),
    );
  }
}

class CategoryPage extends StatelessWidget {
  final String id;

  const CategoryPage({super.key, required this.id});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('分类 $id')),
      body: Center(child: Text('分类内容 $id')),
    );
  }
}

class PostDetailPage extends StatelessWidget {
  final String id;

  const PostDetailPage({super.key, required this.id});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('文章 $id')),
      body: Center(child: Text('文章内容 $id')),
    );
  }
}

class PageDetailPage extends StatelessWidget {
  final String id;

  const PageDetailPage({super.key, required this.id});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('页面 $id')),
      body: Center(child: Text('页面内容 $id')),
    );
  }
}

class UserProfilePage extends StatelessWidget {
  final String id;

  const UserProfilePage({super.key, required this.id});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('用户 $id')),
      body: Center(child: Text('用户资料 $id')),
    );
  }
}

class MenuItemPage extends StatelessWidget {
  final String route;

  const MenuItemPage({super.key, required this.route});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('菜单项: $route')),
      body: Center(child: Text('菜单项内容: $route')),
    );
  }
}

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('登录')),
      body: const Center(child: Text('登录页面')),
    );
  }
}

class RegisterPage extends StatelessWidget {
  const RegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('注册')),
      body: const Center(child: Text('注册页面')),
    );
  }
}

class UserCenterPage extends StatelessWidget {
  const UserCenterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('用户中心')),
      body: const Center(child: Text('用户中心页面')),
    );
  }
}

class UserLevelsPage extends StatelessWidget {
  const UserLevelsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('用户等级')),
      body: const Center(child: Text('用户等级介绍页面')),
    );
  }
}
