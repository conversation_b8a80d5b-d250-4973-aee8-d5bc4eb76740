import 'package:flutter/material.dart';
import '../../services/records_service.dart';
import '../../widgets/record_list.dart';
import '../../theme.dart';

class PurchaseRecordsPage extends StatefulWidget {
  const PurchaseRecordsPage({Key? key}) : super(key: key);

  @override
  _PurchaseRecordsPageState createState() => _PurchaseRecordsPageState();
}

class _PurchaseRecordsPageState extends State<PurchaseRecordsPage> {
  final RecordsService _recordsService = RecordsService();
  final List<RecordItem> _records = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 1;
  final int _pageSize = 10;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadRecords();
  }

  Future<void> _loadRecords({bool refresh = false}) async {
    if (_isLoading) return;

    if (refresh) {
      setState(() {
        _records.clear();
        _currentPage = 1;
        _hasMore = true;
        _errorMessage = '';
      });
    }

    if (!_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final data = await _recordsService.getPurchaseRecords(
        page: _currentPage,
        limit: _pageSize,
        context: context,
      );

      final List<dynamic> newRecords = data['records'] ?? [];
      final int total = data['total'] ?? 0;

      setState(() {
        for (var record in newRecords) {
          _records.add(RecordItem(
            title: record['title'] ?? '未知商品',
            subtitle: '购买商品',
            time: record['time'] ?? '',
            status: record['status'] ?? '未知状态',
            amount: (record['price'] is int) ? (record['price'] as int).toDouble() : (record['price'] ?? 0.0),
          ));
        }

        _hasMore = _records.length < total;
        _currentPage++;
        _errorMessage = '';
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('购买记录'),
        backgroundColor: Colors.white,
        foregroundColor: AppColors.textEmphasis,
        elevation: 0.5,
      ),
      body: Column(
        children: [
          if (_errorMessage.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(8.0),
              color: Colors.red[50],
              width: double.infinity,
              child: Text(
                _errorMessage,
                style: const TextStyle(color: Colors.red),
                textAlign: TextAlign.center,
              ),
            ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () => _loadRecords(refresh: true),
              child: RecordList(
                records: _records,
                isLoading: _isLoading && _records.isEmpty,
                hasMore: _hasMore && !_isLoading,
                onLoadMore: _loadRecords,
                emptyText: '暂无购买记录',
              ),
            ),
          ),
        ],
      ),
    );
  }
}
