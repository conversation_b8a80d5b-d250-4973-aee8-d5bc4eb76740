class CarouselItem {
  final String pic;
  final String route;

  CarouselItem({required this.pic, required this.route});

  factory CarouselItem.fromJson(Map<String, dynamic> json) {
    return CarouselItem(
      pic: json['pic'] ?? '',
      route: json['route'] ?? '',
    );
  }
  
  // 添加toJson方法，用于序列化
  Map<String, dynamic> toJson() {
    return {
      'pic': pic,
      'route': route,
    };
  }
}

class MenuItem {
  final String title;
  final String icon;
  final String route;

  MenuItem({required this.title, required this.icon, required this.route});

  factory MenuItem.fromJson(Map<String, dynamic> json) {
    return MenuItem(
      title: json['title'] ?? '',
      icon: json['icon'] ?? '',
      route: json['route'] ?? '',
    );
  }
  
  // 添加toJson方法，用于序列化
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'icon': icon,
      'route': route,
    };
  }
}

class DailyRecommendation {
  final String title;
  final String thumb;
  final String date;
  final int ping;
  final int view;
  final String route;

  DailyRecommendation({
    required this.title,
    required this.thumb,
    required this.date,
    required this.ping,
    required this.view,
    required this.route,
  });

  factory DailyRecommendation.fromJson(Map<String, dynamic> json) {
    return DailyRecommendation(
      title: json['title'] ?? '',
      thumb: json['thumb'] is String ? json['thumb'] : '',
      date: json['date'] ?? '',
      ping: _parseIntOrZero(json['ping']),
      view: _parseIntOrZero(json['view']),
      route: json['route'] ?? '',
    );
  }
  
  // 添加toJson方法，用于序列化
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'thumb': thumb,
      'date': date,
      'ping': ping,
      'view': view,
      'route': route,
    };
  }

  static int _parseIntOrZero(dynamic value) {
    if (value is int) {
      return value;
    } else if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    return 0;
  }
}
