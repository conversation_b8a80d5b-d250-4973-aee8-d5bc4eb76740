import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'dart:convert';
import '../../models/chat_message.dart';
import '../../theme.dart';

class ChatMessage extends StatelessWidget {
  final ChatMessageData message;
  final bool isMe;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const ChatMessage({
    super.key,
    required this.message,
    this.isMe = false,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    if (message.isSystem) {
      return _buildSystemMessage(context);
    }

    // 检查消息是否被撤回 - 同时检查revoked属性和meta.revoke字段
    bool isRevoked = message.revoked ||
        (message.metadata != null &&
            message.metadata!['revoke'] != null &&
            (message.metadata!['revoke'] == 1 ||
                message.metadata!['revoke'] == true ||
                message.metadata!['revoke'] == '1'));

    if (isRevoked) {
      return _buildRevokedMessage(context);
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment:
            isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!isMe) _buildAvatar(),
          const SizedBox(width: 8),
          Flexible(
            child: GestureDetector(
              onTap: onTap,
              onLongPress: onLongPress,
              child: Column(
                crossAxisAlignment:
                    isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                children: [
                  _buildUserName(),
                  const SizedBox(height: 4),
                  _buildMessageContent(context),
                  const SizedBox(height: 2),
                  _buildTimestamp(),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          if (isMe) _buildAvatar(),
        ],
      ),
    );
  }

  Widget _buildSystemMessage(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(13),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Text(
                message.content,
                style: TextStyle(
                  color: Colors.grey[700],
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRevokedMessage(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(13),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.replay, size: 14, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '此消息已被撤回',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    final String avatarUrl = message.userAvatar ?? message.avatar ?? '';

    return CircleAvatar(
      radius: 20,
      backgroundColor: _getAvatarColor(message.userId),
      backgroundImage:
          avatarUrl.isNotEmpty ? CachedNetworkImageProvider(avatarUrl) : null,
      child: avatarUrl.isEmpty
          ? Text(
              message.userName.isNotEmpty
                  ? message.userName[0].toUpperCase()
                  : '?',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            )
          : null,
    );
  }

  // 根据用户ID生成固定的头像颜色
  Color _getAvatarColor(String userId) {
    final List<Color> colors = [
      AppColors.primaryGreen,
      Colors.red[300]!,
      Colors.green[300]!,
      Colors.purple[300]!,
      Colors.orange[300]!,
      Colors.teal[300]!,
      Colors.pink[300]!,
      Colors.indigo[300]!,
    ];

    // 使用用户ID的哈希值来选择颜色
    final int colorIndex = userId.hashCode.abs() % colors.length;
    return colors[colorIndex];
  }

  Widget _buildUserName() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          message.userName,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 12,
            // 根据Web端样式，参考checkMsgUserClass和checkMsgUserClass2函数
            color: message.isAdmin
                ? AppColors.primaryGreen // 管理员使用绿色 text-green-800
                : (isMe
                    ? AppColors.primaryGreen // 自己的消息使用绿色
                    : const Color(0xFF6B7280)), // 其他人使用灰色 text-gray-500
          ),
        ),
        if (message.isAdmin)
          Padding(
            padding: const EdgeInsets.only(left: 4),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
              decoration: BoxDecoration(
                color: const Color(0xFFFFF7ED), // 使用 bg-orange-50 对应的颜色
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                    color:
                        const Color(0xFFFED7AA)), // 使用 border-orange-100 对应的颜色
              ),
              child: const Text(
                '管理员',
                style: TextStyle(
                  fontSize: 9,
                  color: Color(0xFF166534), // 使用 text-green-800 对应的颜色
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildMessageContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: message.isAdmin
            ? const Color(0xFFFFF7ED) // 管理员消息使用 bg-orange-50
            : message.getMessageColor(context),
        borderRadius: BorderRadius.circular(8),
        border: message.isAdmin
            ? Border.all(
                color: const Color(0xFFFED7AA)) // 使用 border-orange-100 对应的颜色
            : null,
      ),
      child: Column(
        crossAxisAlignment:
            isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          if (message.reply != null) _buildReplyContent(context),
          if (message.isImage)
            _buildImageContent()
          else if (message.isLink)
            _buildLinkContent()
          else
            Text(
              message.content,
              style: TextStyle(
                fontSize: 14,
                color: message.isAdmin
                    ? AppColors.primaryGreen // 管理员消息使用 text-green-800
                    : message.getTextColor(),
                fontWeight:
                    message.isAdmin ? FontWeight.bold : FontWeight.normal,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildReplyContent(BuildContext context) {
    // 使用硬编码的回复消息进行测试
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 2,
            height: 16,
            margin: const EdgeInsets.only(top: 2),
            decoration: BoxDecoration(
              color: Colors.grey[400],
            ),
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              message.reply != null
                  ? '${message.reply!.userName}: ${message.reply!.content}'
                  : 'Max.King-2024: 14',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageContent() {
    // 使用 Set 存储图片 URL，确保不会有重复
    final Set<String> imageUrlSet = <String>{};

    // 添加 imgs 字段中的图片
    if (message.imgs != null && message.imgs!.isNotEmpty) {
      imageUrlSet.addAll(message.imgs!);
    }

    // 如果没有图片列表但有内容，可能内容就是图片URL
    if (imageUrlSet.isEmpty &&
        message.content.isNotEmpty &&
        (message.content.startsWith('http') ||
            message.content.startsWith('data:image'))) {
      imageUrlSet.add(message.content);
    }

    // 确保从消息的 metadata.pics 字段获取图片，如果有的话
    if (message.metadata != null &&
        message.metadata!['pics'] != null &&
        message.metadata!['pics'] is List) {
      List<dynamic> pics = message.metadata!['pics'];
      for (var pic in pics) {
        if (pic is String) {
          imageUrlSet.add(pic);
        }
      }
    }

    // 转换为列表用于显示
    final List<String> imageUrls = imageUrlSet.toList();

    return Column(
      crossAxisAlignment:
          isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        if (message.content.isNotEmpty &&
            (imageUrls.isEmpty || (!imageUrls.contains(message.content))))
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              message.content,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        Wrap(
          alignment: isMe ? WrapAlignment.end : WrapAlignment.start,
          spacing: 4,
          runSpacing: 4,
          children: imageUrls.map((url) {
            return Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: onTap,
                borderRadius: BorderRadius.circular(8),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: url.startsWith('data:image')
                      ? Image.memory(
                          base64Decode(url.split(',')[1]),
                          width: 150,
                          height: 150,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              Container(
                            width: 150,
                            height: 150,
                            color: Colors.grey[300],
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.error, color: Colors.red),
                                const SizedBox(height: 4),
                                Text(
                                  'Base64图片解析失败',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                      : CachedNetworkImage(
                          imageUrl: url,
                          width: 150,
                          height: 150,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            width: 150,
                            height: 150,
                            color: Colors.grey[300],
                            child: const Center(
                              child: SizedBox(
                                width: 24,
                                height: 24,
                                child: SpinKitPulsingGrid(
                                  size: 20,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            width: 150,
                            height: 150,
                            color: Colors.grey[300],
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.error, color: Colors.red),
                                const SizedBox(height: 4),
                                Text(
                                  '加载失败',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildLinkContent() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.primaryGreen.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: AppColors.secondaryLightGreen,
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.link, size: 14, color: AppColors.primaryGreen),
              SizedBox(width: 4),
              Text(
                '链接',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryGreen,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            message.content,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.primaryGreen,
              decoration: TextDecoration.underline,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimestamp() {
    final DateTime messageTime = DateTime.fromMillisecondsSinceEpoch(
        (message.createTime ??
                (message.timestamp.millisecondsSinceEpoch ~/ 1000)) *
            1000);

    final String formattedTime = DateFormat('HH:mm').format(messageTime);
    final bool isSameDay = messageTime.day == DateTime.now().day &&
        messageTime.month == DateTime.now().month &&
        messageTime.year == DateTime.now().year;

    final String displayTime = isSameDay
        ? formattedTime
        : DateFormat('MM-dd HH:mm').format(messageTime);

    return Padding(
      padding: const EdgeInsets.only(top: 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.access_time,
            size: 9,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 2),
          Text(
            displayTime,
            style: TextStyle(
              fontSize: 9,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}
