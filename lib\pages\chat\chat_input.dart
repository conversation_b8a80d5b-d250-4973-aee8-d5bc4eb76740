import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../models/chat_message.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'dart:convert';

class ChatInput extends StatefulWidget {
  final TextEditingController? controller;
  final ChatMessageData? replyTo;
  final Function()? onCancelReply;
  final Function(String, {List<String>? images}) onSendMessage;
  final Future<List<String>> Function() onPickImage;
  final List<String>? selectedImages;
  final Function(int)? onRemoveImage;
  final bool isDisabled;
  final String buttonText;
  final String? placeholder;

  const ChatInput({
    Key? key,
    this.controller,
    this.replyTo,
    this.onCancelReply,
    required this.onSendMessage,
    required this.onPickImage,
    this.selectedImages,
    this.onRemoveImage,
    this.isDisabled = false,
    this.buttonText = '发送',
    this.placeholder,
  }) : super(key: key);

  @override
  State<ChatInput> createState() => _ChatInputState();
}

class _ChatInputState extends State<ChatInput>
    with SingleTickerProviderStateMixin {
  bool _isPickingImage = false;
  late TextEditingController _controller;
  final List<String> _localSelectedImages = [];
  late AnimationController _sendButtonAnimController;
  late Animation<double> _sendButtonScaleAnimation;
  bool _hasContent = false;
  FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();

    // 初始化发送按钮动画
    _sendButtonAnimController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _sendButtonScaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(
        parent: _sendButtonAnimController,
        curve: Curves.easeInOut,
      ),
    );

    // 监听输入框内容变化
    _controller.addListener(_onTextChanged);

    // 监听焦点变化
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        // 当输入框获得焦点时，可以添加额外的动画或效果
      }
    });
  }

  void _onTextChanged() {
    final hasContent = _controller.text.trim().isNotEmpty ||
        (widget.selectedImages?.isNotEmpty ?? false) ||
        _localSelectedImages.isNotEmpty;

    if (hasContent != _hasContent) {
      setState(() {
        _hasContent = hasContent;
      });

      if (hasContent) {
        _sendButtonAnimController.forward();
      } else {
        _sendButtonAnimController.reverse();
      }
    }
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_onTextChanged);
    }
    _sendButtonAnimController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  List<String> get _selectedImages =>
      widget.selectedImages ?? _localSelectedImages;

  void _onRemoveImage(int index) {
    if (widget.onRemoveImage != null) {
      widget.onRemoveImage!(index);
    } else {
      setState(() {
        _localSelectedImages.removeAt(index);
      });
    }
    _onTextChanged();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 回复消息提示 - 优化高度
          if (widget.replyTo != null)
            Container(
              padding: const EdgeInsets.symmetric(
                  horizontal: 8, vertical: 6), // 减小垂直内边距
              color: Colors.grey[200],
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min, // 关键：使用最小尺寸
                      children: [
                        Text(
                          '回复 ${widget.replyTo!.userName}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 11, // 稍微减小字体
                          ),
                        ),
                        const SizedBox(height: 2), // 减小间距
                        Text(
                          widget.replyTo!.content,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(fontSize: 11), // 稍微减小字体
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 32,
                    height: 32,
                    child: IconButton(
                      padding: EdgeInsets.zero,
                      icon: const Icon(Icons.close, size: 14), // 减小图标尺寸
                      onPressed: widget.onCancelReply,
                    ),
                  ),
                ],
              ),
            ),

          // 已选图片预览 - 优化高度
          if (_selectedImages.isNotEmpty)
            Container(
              height: 80, // 从100减少到80
              padding: const EdgeInsets.symmetric(vertical: 4), // 减小内边距
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.only(left: 8),
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(7),
                          child: _selectedImages[index].startsWith('data:image')
                              ? Image.memory(
                                  base64Decode(
                                      _selectedImages[index].split(',')[1]),
                                  fit: BoxFit.cover,
                                  width: 80,
                                  height: 80,
                                  errorBuilder: (context, error, stackTrace) =>
                                      Container(
                                    color: Colors.grey[200],
                                    child: const Icon(Icons.error),
                                  ),
                                )
                              : CachedNetworkImage(
                                  imageUrl: _selectedImages[index],
                                  fit: BoxFit.cover,
                                  width: 80,
                                  height: 80,
                                  placeholder: (context, url) => Container(
                                    color: Colors.grey[200],
                                    child: const Center(
                                      child: SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: SpinKitPulsingGrid(
                                          size: 15,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ),
                                  ),
                                  errorWidget: (context, url, error) =>
                                      Container(
                                    color: Colors.grey[200],
                                    child: const Icon(Icons.error),
                                  ),
                                ),
                        ),
                        Positioned(
                          right: 0,
                          top: 0,
                          child: GestureDetector(
                            onTap: () => _onRemoveImage(index),
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.5),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: const Icon(
                                Icons.close,
                                size: 16,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),

          // 输入框和按钮 - 优化内边距
          Container(
            padding: const EdgeInsets.symmetric(vertical: 4), // 从8减少到4
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // 图片选择按钮 - 优化尺寸
                SizedBox(
                  width: 40,
                  height: 40,
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    icon: Icon(
                      Icons.image,
                      size: 20, // 减小图标尺寸
                      color:
                          widget.isDisabled ? Colors.grey : theme.primaryColor,
                    ),
                    onPressed: widget.isDisabled
                        ? null
                        : () async {
                            if (_isPickingImage) return;

                            setState(() {
                              _isPickingImage = true;
                            });

                            try {
                              final imageUrls = await widget.onPickImage();
                              if (imageUrls.isNotEmpty) {
                                setState(() {
                                  _localSelectedImages.addAll(imageUrls);
                                });
                                _onTextChanged();
                              }
                            } finally {
                              setState(() {
                                _isPickingImage = false;
                              });
                            }
                          },
                  ),
                ),

                // 输入框
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: TextField(
                      controller: _controller,
                      focusNode: _focusNode,
                      decoration: InputDecoration(
                        hintText: widget.placeholder ?? '输入消息...',
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 6), // 从8减少到6
                      ),
                      maxLines: 2, // 从3减少到2，进一步减少最大高度
                      minLines: 1,
                      enabled: !widget.isDisabled,
                      textInputAction: TextInputAction.send,
                      onSubmitted: (text) {
                        if (!widget.isDisabled &&
                            (_hasContent || _selectedImages.isNotEmpty)) {
                          _sendMessage();
                        }
                      },
                    ),
                  ),
                ),

                // 发送按钮 - 优化尺寸
                Padding(
                  padding: const EdgeInsets.only(right: 4, left: 2), // 减小外边距
                  child: ScaleTransition(
                    scale: _sendButtonScaleAnimation,
                    child: ElevatedButton(
                      onPressed: widget.isDisabled ||
                              (!_hasContent && _selectedImages.isEmpty)
                          ? null
                          : _sendMessage,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.primaryColor,
                        foregroundColor: Colors.white,
                        shape: const CircleBorder(),
                        padding: const EdgeInsets.all(10), // 从12减少到10
                        elevation: 2,
                        minimumSize: const Size(40, 40), // 设置最小尺寸
                      ),
                      child: Icon(
                        Icons.send,
                        size: 18, // 从20减少到18
                        color: widget.isDisabled ||
                                (!_hasContent && _selectedImages.isEmpty)
                            ? Colors.grey[400]
                            : Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _sendMessage() {
    final content = _controller.text.trim();
    if (content.isNotEmpty || _selectedImages.isNotEmpty) {
      widget.onSendMessage(content,
          images:
              _selectedImages.isNotEmpty ? List.from(_selectedImages) : null);
      _controller.clear();
      setState(() {
        _localSelectedImages.clear();
        _hasContent = false;
      });
      _sendButtonAnimController.reverse();
    }
  }
}
