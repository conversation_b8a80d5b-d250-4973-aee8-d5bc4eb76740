import 'package:flutter/material.dart';
import 'dart:convert';
import 'message_type.dart';
import 'package:flutter/foundation.dart';

class ChatMessageData {
  final String id;
  final String userId;
  final String userName;
  final String content;
  final DateTime timestamp;
  final String type;
  final bool isAdmin;
  final String? toUserId;
  final String? toUserName;
  final String? avatar;
  final Map<String, dynamic>? metadata;
  final String? msgId;
  final int? createTime;
  final ChatMessageData? reply;
  final List<String>? imgs;
  final String? userAvatar;
  final String? channelName;
  final MessageSource source;
  bool read;
  bool revoked;

  ChatMessageData({
    required this.id,
    required this.userId,
    required this.userName,
    required this.content,
    required this.timestamp,
    required this.type,
    this.isAdmin = false,
    this.toUserId,
    this.toUserName,
    this.avatar,
    this.metadata,
    this.msgId,
    this.createTime,
    this.reply,
    this.imgs,
    this.userAvatar,
    this.channelName,
    this.source = MessageSource.WEBSOCKET,
    this.read = false,
    this.revoked = false,
  });

  factory ChatMessageData.fromJson(Map<String, dynamic> json) {
    try {
      // 处理嵌套的sender结构
      Map<String, dynamic> processedJson = Map<String, dynamic>.from(json);

      // 如果有sender字段，提取其中的信息
      if (json['sender'] != null && json['sender'] is Map<String, dynamic>) {
        final sender = json['sender'] as Map<String, dynamic>;
        processedJson['userId'] = sender['id']?.toString() ?? '';

        // 改进用户名处理，确保不显示'未知用户'
        String senderName = sender['name']?.toString() ?? '';
        if (senderName.isEmpty) {
          // 如果sender.name为空，使用"用户+ID"格式
          senderName = '用户${sender['id']?.toString() ?? ''}';
        }
        processedJson['userName'] = senderName;

        processedJson['avatar'] = sender['avatar']?.toString();
        processedJson['userAvatar'] = sender['avatar']?.toString();

        // 从sender中提取admin信息
        if (sender['admin'] != null) {
          processedJson['admin'] = sender['admin'];
        }
      }

      // 处理timestamp和createTime
      DateTime timestamp = DateTime.now(); // 默认为当前时间
      int? createTimeInt;

      // 优先使用create_time
      if (processedJson['create_time'] != null) {
        try {
          if (processedJson['create_time'] is int) {
            createTimeInt = processedJson['create_time'] as int;
            // 同时设置timestamp
            timestamp =
                DateTime.fromMillisecondsSinceEpoch(createTimeInt * 1000);
          } else if (processedJson['create_time'] is String) {
            createTimeInt =
                int.tryParse(processedJson['create_time'] as String);
            if (createTimeInt != null) {
              timestamp =
                  DateTime.fromMillisecondsSinceEpoch(createTimeInt * 1000);
            }
          } else {
            // 尝试转换为字符串后解析
            createTimeInt =
                int.tryParse(processedJson['create_time'].toString());
            if (createTimeInt != null) {
              timestamp =
                  DateTime.fromMillisecondsSinceEpoch(createTimeInt * 1000);
            }
          }
        } catch (e) {
          // 保持默认值
        }
      }
      // 如果没有create_time，尝试使用timestamp
      else if (processedJson['timestamp'] != null) {
        try {
          if (processedJson['timestamp'] is int) {
            // 检查是否是毫秒时间戳（13位）还是秒时间戳（10位）
            final timestampValue = processedJson['timestamp'] as int;
            if (timestampValue > 9999999999) {
              // 大于10位，可能是毫秒
              timestamp = DateTime.fromMillisecondsSinceEpoch(timestampValue);
              createTimeInt = timestampValue ~/ 1000;
            } else {
              // 秒时间戳
              timestamp =
                  DateTime.fromMillisecondsSinceEpoch(timestampValue * 1000);
              createTimeInt = timestampValue;
            }
          } else if (processedJson['timestamp'] is String) {
            final timestampStr = processedJson['timestamp'] as String;
            // 尝试解析为整数
            final timestampInt = int.tryParse(timestampStr);
            if (timestampInt != null) {
              // 检查是否是毫秒时间戳（13位）还是秒时间戳（10位）
              if (timestampInt > 9999999999) {
                // 大于10位，可能是毫秒
                timestamp = DateTime.fromMillisecondsSinceEpoch(timestampInt);
                createTimeInt = timestampInt ~/ 1000;
              } else {
                // 秒时间戳
                timestamp =
                    DateTime.fromMillisecondsSinceEpoch(timestampInt * 1000);
                createTimeInt = timestampInt;
              }
            } else if (timestampStr.contains('-') ||
                timestampStr.contains('T')) {
              // 尝试解析为ISO日期
              timestamp = DateTime.parse(timestampStr);
              createTimeInt = timestamp.millisecondsSinceEpoch ~/ 1000;
            }
          }
        } catch (e) {
          // 保持默认值
          createTimeInt = DateTime.now().millisecondsSinceEpoch ~/ 1000;
        }
      } else {
        // 如果都没有，使用当前时间
        createTimeInt = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      }

      // 处理回复消息
      ChatMessageData? replyMessage;
      if (processedJson['reply'] != null) {
        try {
          if (processedJson['reply'] is String) {
            final replyJson = jsonDecode(processedJson['reply'] as String);
            if (replyJson is Map<String, dynamic>) {
              replyMessage = ChatMessageData.fromJson(replyJson);
            }
          } else if (processedJson['reply'] is Map) {
            // 确保reply是一个Map<String, dynamic>
            Map<String, dynamic> replyMap;
            if (processedJson['reply'] is Map<String, dynamic>) {
              replyMap = processedJson['reply'] as Map<String, dynamic>;
            } else {
              replyMap =
                  Map<String, dynamic>.from(processedJson['reply'] as Map);
            }

            // 检查并确保必要字段存在
            if (!replyMap.containsKey('msg_id') && replyMap.containsKey('id')) {
              replyMap['msg_id'] = replyMap['id'];
            }

            if (!replyMap.containsKey('user_id') &&
                replyMap.containsKey('userId')) {
              replyMap['user_id'] = replyMap['userId'];
            }

            if (!replyMap.containsKey('user_name') &&
                replyMap.containsKey('userName')) {
              replyMap['user_name'] = replyMap['userName'];
            }

            // 确保有create_time字段
            if (!replyMap.containsKey('create_time') &&
                replyMap.containsKey('timestamp')) {
              var timestamp = replyMap['timestamp'];
              if (timestamp is String) {
                int? timestampInt = int.tryParse(timestamp);
                if (timestampInt != null) {
                  replyMap['create_time'] = timestampInt;
                }
              } else if (timestamp is int) {
                replyMap['create_time'] = timestamp;
              }
            }

            replyMessage = ChatMessageData.fromJson(replyMap);
          }
        } catch (e) {
          // 处理失败，记录错误但继续处理
          debugPrint('解析回复消息失败: $e');
        }
      } else if (processedJson['reply_to'] != null) {
        try {
          final replyId = processedJson['reply_to'].toString();
          // 创建一个简单的回复消息对象
          replyMessage = ChatMessageData(
            id: replyId,
            userId: '',
            userName: '原消息',
            content: '查看原消息',
            timestamp: timestamp,
            type: 'text',
          );
        } catch (e) {
          // 处理失败，忽略回复消息
          debugPrint('创建简单回复消息失败: $e');
        }
      }

      // 处理图片列表
      List<String>? imageList;
      if (processedJson['imgs'] != null) {
        try {
          if (processedJson['imgs'] is List) {
            imageList = List<String>.from(
                (processedJson['imgs'] as List).map((e) => e.toString()));
          } else if (processedJson['imgs'] is String) {
            final imgJson = jsonDecode(processedJson['imgs'] as String);
            if (imgJson is List) {
              imageList = List<String>.from(imgJson.map((e) => e.toString()));
            }
          }
        } catch (e) {
          // 处理失败，忽略图片列表
        }
      }

      // 处理room字段
      String? roomId;
      if (processedJson['room'] != null) {
        roomId = processedJson['room'].toString();
      }

      // 处理meta字段
      Map<String, dynamic>? metaData;
      if (processedJson['meta'] != null) {
        if (processedJson['meta'] is Map) {
          metaData = Map<String, dynamic>.from(processedJson['meta'] as Map);
        } else if (processedJson['meta'] is String) {
          try {
            final metaJson = jsonDecode(processedJson['meta'] as String);
            if (metaJson is Map) {
              metaData = Map<String, dynamic>.from(metaJson);
            }
          } catch (e) {
            // 处理失败，忽略meta
          }
        }
      } else if (processedJson['metadata'] != null) {
        if (processedJson['metadata'] is Map) {
          metaData =
              Map<String, dynamic>.from(processedJson['metadata'] as Map);
        }
      }

      if (metaData == null) {
        metaData = {'room': roomId};
      } else if (roomId != null && !metaData.containsKey('room')) {
        metaData['room'] = roomId;
      }

      // 检查是否是撤回消息
      bool isRevoked = processedJson['revoked'] == true;
      // 检查meta.revoke字段
      if (!isRevoked && metaData != null && metaData['revoke'] != null) {
        isRevoked = metaData['revoke'] == 1 ||
            metaData['revoke'] == true ||
            metaData['revoke'] == '1';
      }

      // 确保id字段是字符串类型
      String id = '';
      if (processedJson['msg_id'] != null) {
        id = processedJson['msg_id'].toString();
      } else if (processedJson['msgId'] != null) {
        id = processedJson['msgId'].toString();
      } else if (processedJson['id'] != null) {
        id = processedJson['id'].toString();
      } else {
        id = DateTime.now().millisecondsSinceEpoch.toString();
      }

      // 确保userId字段是字符串类型
      String userId = '';
      if (processedJson['user_id'] != null) {
        userId = processedJson['user_id'].toString();
      } else if (processedJson['userId'] != null) {
        userId = processedJson['userId'].toString();
      }

      // 处理admin字段 - 可能是布尔值或数值
      bool isAdmin = false;
      if (processedJson['admin'] != null) {
        if (processedJson['admin'] is int) {
          isAdmin = (processedJson['admin'] as int) > 0;
        } else {
          // 尝试转换为整数
          try {
            int adminValue = int.parse(processedJson['admin'].toString());
            isAdmin = adminValue > 0;
          } catch (e) {
            // 如果无法转换为整数，则检查是否为 true 或 "true" 或 "1"
            var adminStr = processedJson['admin'].toString().toLowerCase();
            isAdmin = adminStr == 'true' || adminStr == '1';
          }
        }
      }

      // 处理channelName字段 - 检查多个可能的字段名
      String? channelName;
      if (processedJson['channel_name'] != null) {
        channelName = processedJson['channel_name'].toString();
      } else if (processedJson['channelName'] != null) {
        channelName = processedJson['channelName'].toString();
      }

      return ChatMessageData(
        id: id,
        userId: userId,
        userName: processedJson['user_name']?.toString() ??
            processedJson['userName']?.toString() ??
            (userId.isNotEmpty ? '用户$userId' : '系统'),
        content: processedJson['content']?.toString() ?? '',
        timestamp: timestamp,
        type: processedJson['type']?.toString() ?? 'text',
        isAdmin: isAdmin,
        toUserId: processedJson['to_user_id']?.toString() ??
            processedJson['toUserId']?.toString(),
        toUserName: processedJson['to_user_name']?.toString() ??
            processedJson['toUserName']?.toString(),
        avatar: processedJson['avatar']?.toString() ??
            processedJson['user_avatar']?.toString(),
        metadata: metaData,
        read: processedJson['read'] == true,
        msgId: processedJson['msg_id']?.toString() ??
            processedJson['msgId']?.toString() ??
            id,
        createTime: createTimeInt,
        reply: replyMessage,
        imgs: imageList,
        userAvatar: processedJson['user_avatar']?.toString() ??
            processedJson['userAvatar']?.toString() ??
            processedJson['avatar']?.toString(),
        channelName: channelName,
        revoked: isRevoked,
        source: MessageSource.WEBSOCKET,
      );
    } catch (e) {
      // 如果解析过程中出现任何错误，记录错误但不创建系统消息
      debugPrint('消息解析错误: $e');
      throw FormatException('消息解析失败: $e');
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'msg_id': msgId ?? id,
      'user_id': userId,
      'user_name': userName,
      'content': content,
      'type': type,
      'create_time': createTime ?? (timestamp.millisecondsSinceEpoch ~/ 1000),
      'admin': isAdmin ? 1 : 0,
      'user_avatar': userAvatar ?? avatar,
    };

    // 添加频道名称
    if (channelName != null) {
      json['channel_name'] = channelName;
    }

    // 处理meta字段
    Map<String, dynamic> meta =
        metadata != null ? Map<String, dynamic>.from(metadata!) : {};

    // 如果消息被撤回，设置meta.revoke = 1
    if (revoked) {
      meta['revoke'] = 1;
    }

    json['meta'] = meta;

    // 处理reply字段
    if (reply != null) {
      try {
        // 确保reply是完整的对象，包含所有必要字段
        Map<String, dynamic> replyJson = reply!.toJson();

        // 检查必要字段
        if (!replyJson.containsKey('msg_id') && replyJson.containsKey('id')) {
          replyJson['msg_id'] = replyJson['id'];
        }

        if (!replyJson.containsKey('user_id') &&
            replyJson.containsKey('userId')) {
          replyJson['user_id'] = replyJson['userId'];
        }

        if (!replyJson.containsKey('user_name') &&
            replyJson.containsKey('userName')) {
          replyJson['user_name'] = replyJson['userName'];
        }

        // 确保有create_time字段
        if (!replyJson.containsKey('create_time')) {
          replyJson['create_time'] = reply!.createTime ??
              (reply!.timestamp.millisecondsSinceEpoch ~/ 1000);
        }

        json['reply'] = replyJson;
      } catch (e) {
        // 处理失败时记录错误
        debugPrint('序列化reply字段失败: $e');
      }
    }

    // 处理imgs字段
    if (imgs != null && imgs!.isNotEmpty) {
      // 确保imgs是一个有效的字符串列表
      List<String> validImgs = imgs!
          .where((img) =>
              img != null &&
              img.isNotEmpty &&
              (img.startsWith('http://') || img.startsWith('https://')))
          .toList();

      if (validImgs.isNotEmpty) {
        json['imgs'] = validImgs;
      }
    }

    return json;
  }

  // 是否是系统消息
  bool get isSystem {
    // 内容为空的消息不是系统消息
    if (content.trim().isEmpty) {
      return false;
    }

    // app_state类型消息不是系统消息
    if (type == 'app_state' ||
        type == 'ping' ||
        type == 'pong' ||
        type == 'delivery_strategy' ||
        type == 'user_state') {
      return false;
    }

    // 检查消息类型
    bool isSystemType = type == 'system' ||
        type == MessageType.system.toString() ||
        type == MessageType.notification.toString() ||
        type == 'connect'; // 连接消息也视为系统消息

    // 检查消息内容是否是系统消息
    bool isSystemContent = content.contains('进入房间') ||
        content.contains('离开房间') ||
        content.contains('连接成功') ||
        content.contains('断开连接') ||
        content.contains('重新连接') ||
        content.contains('请求连接');

    return isSystemType || isSystemContent;
  }

  // 是否是私聊消息
  bool get isPrivate => toUserId != null && toUserName != null;

  // 是否是图片消息
  bool get isImage =>
      type == 'image' ||
      type == MessageType.image.toString() ||
      (imgs != null && imgs!.isNotEmpty);

  // 是否是链接消息
  bool get isLink =>
      type == 'link' ||
      type == MessageType.link.toString() ||
      content.startsWith('http://') ||
      content.startsWith('https://');

  // 获取消息时间
  String get time => DateTime.fromMillisecondsSinceEpoch(
          (createTime ?? (timestamp.millisecondsSinceEpoch ~/ 1000)) * 1000)
      .toString();

  // 获取消息颜色
  Color getMessageColor(BuildContext context) {
    if (isSystem) {
      return Colors.grey[300]!;
    } else if (isAdmin) {
      // 管理员消息的背景色在 ChatMessage 组件中单独处理
      return Colors.amber[50]!;
    } else {
      return Theme.of(context).primaryColor.withOpacity(0.05);
    }
  }

  // 获取消息文本颜色
  Color getTextColor() {
    if (isSystem) {
      return Colors.grey[700]!;
    } else if (isAdmin) {
      // 管理员消息的文本颜色在 ChatMessage 组件中单独处理
      return Color(0xFF166534); // text-green-800
    } else {
      return Colors.black87;
    }
  }

  // 复制并修改消息
  ChatMessageData copyWith({
    String? id,
    String? userId,
    String? userName,
    String? content,
    DateTime? timestamp,
    String? type,
    bool? isAdmin,
    String? toUserId,
    String? toUserName,
    String? avatar,
    Map<String, dynamic>? metadata,
    String? msgId,
    int? createTime,
    ChatMessageData? reply,
    List<String>? imgs,
    String? userAvatar,
    String? channelName,
    MessageSource? source,
    bool? read,
    bool? revoked,
  }) {
    return ChatMessageData(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      type: type ?? this.type,
      isAdmin: isAdmin ?? this.isAdmin,
      toUserId: toUserId ?? this.toUserId,
      toUserName: toUserName ?? this.toUserName,
      avatar: avatar ?? this.avatar,
      metadata: metadata ?? this.metadata,
      msgId: msgId ?? this.msgId,
      createTime: createTime ?? this.createTime,
      reply: reply ?? this.reply,
      imgs: imgs ?? this.imgs,
      userAvatar: userAvatar ?? this.userAvatar,
      channelName: channelName ?? this.channelName,
      source: source ?? this.source,
      read: read ?? this.read,
      revoked: revoked ?? this.revoked,
    );
  }

  @override
  String toString() {
    return 'ChatMessageData{id: $id, userId: $userId, userName: $userName, content: $content, timestamp: $timestamp, type: $type, isAdmin: $isAdmin, channelName: $channelName}';
  }

  // 从数据库映射创建ChatMessageData对象
  factory ChatMessageData.fromDbMap(Map<String, dynamic> map) {
    // 解析metadata
    Map<String, dynamic>? metadata;
    if (map['metadata'] != null) {
      try {
        metadata = json.decode(map['metadata']);
      } catch (e) {
        debugPrint('解析metadata失败: $e');
      }
    }

    // 解析reply
    ChatMessageData? replyData;
    if (map['reply'] != null) {
      try {
        final replyJson = json.decode(map['reply']);
        replyData = ChatMessageData.fromJson(replyJson);
      } catch (e) {
        debugPrint('解析reply失败: $e');
      }
    }

    // 解析图片列表
    List<String>? imgsList;
    if (map['imgs'] != null) {
      try {
        final String imgsStr = map['imgs'];
        if (imgsStr.startsWith('[') && imgsStr.endsWith(']')) {
          // 去掉首尾的方括号
          final String cleanedStr = imgsStr.substring(1, imgsStr.length - 1);
          // 分割字符串
          imgsList = cleanedStr.split(', ').map((s) => s.trim()).toList();
        }
      } catch (e) {
        debugPrint('解析imgs失败: $e');
      }
    }

    // 解析timestamp
    DateTime timestamp;
    try {
      if (map['timestamp'] is int) {
        // 如果是毫秒时间戳
        timestamp = DateTime.fromMillisecondsSinceEpoch(map['timestamp']);
      } else if (map['timestamp'] is String) {
        final String timestampStr = map['timestamp'].toString();
        // 尝试作为ISO日期解析
        if (timestampStr.contains('-') || timestampStr.contains('T')) {
          try {
            timestamp = DateTime.parse(timestampStr);
          } catch (e) {
            // 如果解析ISO日期失败，尝试解析为整数时间戳
            debugPrint('解析ISO日期失败，尝试解析为时间戳: $e');
            int? timeInt = int.tryParse(timestampStr);
            if (timeInt != null) {
              timestamp = DateTime.fromMillisecondsSinceEpoch(timeInt);
            } else {
              // 如果都失败，使用当前时间
              timestamp = DateTime.now();
            }
          }
        } else {
          // 尝试解析为整数时间戳
          int? timeInt = int.tryParse(timestampStr);
          if (timeInt != null) {
            timestamp = DateTime.fromMillisecondsSinceEpoch(timeInt);
          } else {
            // 如果解析失败，使用当前时间
            timestamp = DateTime.now();
          }
        }
      } else {
        // 其他类型或null，使用当前时间
        timestamp = DateTime.now();
      }
    } catch (e) {
      debugPrint('解析timestamp失败，使用当前时间: $e');
      timestamp = DateTime.now();
    }

    // 创建ChatMessageData对象
    return ChatMessageData(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? '',
      content: map['content'] ?? '',
      timestamp: timestamp,
      type: map['type'] ?? 'text',
      isAdmin: map['isAdmin'] == 1,
      toUserId: map['toUserId'],
      toUserName: map['toUserName'],
      avatar: map['avatar'],
      metadata: metadata,
      msgId: map['msgId'],
      createTime: map['createTime'],
      reply: replyData,
      imgs: imgsList,
      userAvatar: map['userAvatar'],
      channelName: map['channelName'],
      read: map['read'] == 1,
      revoked: map['revoked'] == 1,
      source: MessageSource.WEBSOCKET,
    );
  }
}
