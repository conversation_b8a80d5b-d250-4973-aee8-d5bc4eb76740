import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:logging/logging.dart';
import 'package:super_player/super_player.dart';
import '../../utils/logging.dart';
import '../../utils/wakelock_helper.dart';
import '../../config/config.dart';

/// 基于TXLivePlayerController的直播播放器
/// 专门为WebRTC直播优化，解决视图绑定问题
class SuperLivePlayer extends StatefulWidget {
  final String? liveUrl;
  final VoidCallback? onError;
  final VoidCallback? onLoaded;
  final VoidCallback? onFullscreenChanged;

  const SuperLivePlayer({
    super.key,
    this.liveUrl,
    this.onError,
    this.onLoaded,
    this.onFullscreenChanged,
  });

  @override
  State<SuperLivePlayer> createState() => _SuperLivePlayerState();
}

class _SuperLivePlayerState extends State<SuperLivePlayer> {
  static final Logger _logger = Logging.getLogger('SuperLivePlayer');

  TXLivePlayerController? _liveController; // 官方推荐的直播播放器控制器
  bool _isFullScreen = false;
  bool _isInitialized = false;
  bool _hasError = false;
  String _errorMessage = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    // 关键修复：延迟更长时间，确保SuperPlayerView完全创建并绑定
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 再次延迟，确保视图渲染完成
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _initializePlayer();
        }
      });
    });
  }

  @override
  void dispose() {
    _logger.info('🔄 SuperLivePlayer组件销毁，清理资源');
    // 禁用屏幕常亮
    _disableWakelock();
    if (_liveController != null) {
      try {
        _liveController!.dispose();
        _liveController = null;
      } catch (e) {
        _logger.severe('dispose中清理播放器失败: $e');
      }
    }
    super.dispose();
  }

  /// 启用屏幕常亮
  Future<void> _enableWakelock() async {
    await WakelockHelper.enable('直播播放');
  }

  /// 禁用屏幕常亮
  Future<void> _disableWakelock() async {
    await WakelockHelper.disable('直播播放结束');
  }

  /// 初始化播放器
  Future<void> _initializePlayer() async {
    try {
      _logger.info('初始化TXLivePlayer直播播放器...');

      if (widget.liveUrl == null || widget.liveUrl!.isEmpty) {
        if (mounted) {
          setState(() {
            _hasError = true;
            _errorMessage = '直播URL为空，无法播放';
            _isLoading = false;
          });
        }
        return;
      }

      // 设置全局License
      await SuperPlayerPlugin.setGlobalLicense(
        AppConfig.vLicUrl,
        AppConfig.vLicKey,
      );
      _logger.info('腾讯云License设置成功');

      // 创建TXLivePlayerController
      _liveController = TXLivePlayerController();
      _logger.info('🎮 TXLivePlayerController创建完成');

      // 设置事件监听 - 参考官方案例
      _liveController!.onPlayerEventBroadcast.listen((event) {
        int evtCode = event["event"];
        _logger.info('🎭 *** [TXLivePlayer] 收到事件: $evtCode ***');
        _logger.info('🎭 事件详情: $event');

        // 关键修复：检查组件是否已销毁
        if (!mounted) {
          _logger.info('组件已销毁，忽略TXLivePlayer事件: $evtCode');
          return;
        }

        _handlePlayerEvent(evtCode, event);
      });

      // 设置播放器状态监听
      _liveController!.onPlayerState.listen((event) {
        _logger.info('🔄 播放器状态: ${event?.name}');
      });

      // 设置渲染模式 - 使用FULL_FILL_CONTAINER确保视频填满整个容器
      _liveController!.setRenderMode(FTXPlayerRenderMode.FULL_FILL_CONTAINER);

      _logger.info('🧪 TXLivePlayerController初始化完成，准备播放');
      _logger.info('🧪 播放URL: ${widget.liveUrl}');

      // 关键修复：初始化完成后立即停止加载状态，让TXPlayerVideo能够被渲染
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }

      _logger.info('TXLivePlayer直播播放器初始化成功，等待视图创建');
    } catch (e) {
      _logger.severe('直播播放器初始化失败: $e');
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = '直播播放器初始化失败: $e';
          _isLoading = false;
        });
      }
      widget.onError?.call();
    }
  }

  /// 处理播放器事件
  void _handlePlayerEvent(int evtCode, Map<dynamic, dynamic> event) {
    // 关键修复：检查组件是否已销毁
    if (!mounted) {
      _logger.info('组件已销毁，忽略播放器事件: $evtCode');
      return;
    }

    if (evtCode == TXVodPlayEvent.PLAY_EVT_RCV_FIRST_I_FRAME) {
      // 首帧出现
      _logger.info('✅ *** 首帧出现 - 播放成功！***');
      setState(() {
        _isInitialized = true;
        _hasError = false;
        _isLoading = false;
      });
      // 播放成功后启用屏幕常亮
      _enableWakelock();
      widget.onLoaded?.call();
    } else if (evtCode == TXVodPlayEvent.PLAY_EVT_PLAY_BEGIN) {
      _logger.info('✅ *** 播放开始 ***');
    } else if (evtCode < 0 && evtCode != -100) {
      _logger.severe('❌ *** 播放失败: code=$evtCode ***');
      setState(() {
        _hasError = true;
        _errorMessage = '直播播放失败: 错误码 $evtCode';
        _isLoading = false;
      });
      widget.onError?.call();
    }
  }

  /// 停止播放并清理播放器资源
  void _stopAndCleanupPlayer() {
    try {
      _logger.info('🛑 停止播放并清理播放器资源');

      // 停止播放时禁用屏幕常亮
      _disableWakelock();

      if (_liveController != null) {
        // 停止播放
        _liveController!.pause();

        // 清理播放器资源
        _liveController!.dispose();
        _liveController = null;
      }

      // 更新状态
      if (mounted) {
        setState(() {
          _isInitialized = false;
          _isLoading = false;
          _hasError = false;
          _errorMessage = '';
        });
      }

      _logger.info('✅ 播放器资源清理完成');
    } catch (e) {
      _logger.severe('❌ 清理播放器资源失败: $e');
    }
  }

  /// 刷新直播流
  Future<void> refreshLiveStream() async {
    if (mounted) {
      setState(() {
        _hasError = false;
        _errorMessage = '';
        _isLoading = true;
        _isInitialized = false;
      });
    }
    await _initializePlayer();
  }

  @override
  Widget build(BuildContext context) {
    // 步骤6: 添加返回事件监听 - 按照官方文档标准实现
    return PopScope(
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          final shouldPop = await onWillPop();
          if (shouldPop && context.mounted) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Container(
        decoration: const BoxDecoration(color: Colors.black),
        child: _isFullScreen
            ? getBody() // 全屏时直接显示内容，不使用Scaffold
            : Scaffold(
                backgroundColor: Colors.transparent,
                appBar: AppBar(
                  backgroundColor: Colors.transparent,
                  title: const Text('直播播放'),
                ),
                body: SafeArea(
                  child: Builder(
                    builder: (context) => getBody(),
                  ),
                ),
              ),
      ),
    );
  }

  /// 获取主体内容
  Widget getBody() {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Stack(
          children: [
            // 播放器视图 - 使用TXLivePlayerController，直接填充整个容器
            if (!_hasError && _liveController != null)
              Positioned.fill(
                child: TXPlayerVideo(
                  onRenderViewCreatedListener: (viewId) async {
                    _logger.info(
                        '👁️ *** TXPlayerVideo视图创建成功，viewId: $viewId ***');
                    _logger.info('👁️ 设置播放器视图并开始播放');

                    try {
                      // 关键修复：等待视图完全准备好，避免FrameEvents错误
                      await Future.delayed(const Duration(milliseconds: 300));

                      // 检查组件是否仍然挂载
                      if (!mounted) {
                        _logger.warning('组件已销毁，取消播放器初始化');
                        return;
                      }

                      // 设置播放器视图 - 关键步骤
                      _liveController!.setPlayerView(viewId);
                      _logger.info('✅ 播放器视图绑定成功');

                      // 再次设置渲染模式，确保视频填满容器
                      _liveController!.setRenderMode(
                          FTXPlayerRenderMode.FULL_FILL_CONTAINER);
                      _logger.info('✅ 渲染模式设置为FULL_FILL_CONTAINER');

                      // 再次等待确保视图绑定完成
                      await Future.delayed(const Duration(milliseconds: 200));

                      // 开始播放 - 根据官方文档，这是正确的流程
                      _logger.info('🎬 开始TXLivePlayer播放: ${widget.liveUrl}');
                      await _liveController!.startLivePlay(widget.liveUrl!);
                      _logger.info('✅ TXLivePlayer播放命令已发送');
                    } catch (e) {
                      _logger.severe('❌ 播放器视图绑定或播放启动失败: $e');
                      if (mounted) {
                        setState(() {
                          _hasError = true;
                          _errorMessage = '播放器视图绑定失败: $e';
                          _isLoading = false;
                        });
                      }
                      widget.onError?.call();
                    }
                  },
                ),
              ),

            // 错误显示
            if (_hasError)
              Center(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 48,
                      ),
                      const SizedBox(height: 12),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          _errorMessage,
                          style: const TextStyle(color: Colors.white),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Flexible(
                            child: ElevatedButton(
                              onPressed: refreshLiveStream,
                              child: const Text('重试'),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Flexible(
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.of(context)
                                    .pushReplacementNamed('/live/check');
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey[600],
                              ),
                              child: const Text('刷新直播'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

            // 加载指示器
            if (_isLoading && !_hasError)
              const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(color: Colors.white),
                    SizedBox(height: 16),
                    Text(
                      '正在加载直播...',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),

            // 播放器控制按钮
            if (!_hasError && !_isLoading && _liveController != null)
              _buildPlayerControls(),
          ],
        );
      },
    );
  }

  /// 构建播放器控制按钮
  Widget _buildPlayerControls() {
    return Stack(
      children: [
        // 中央播放/暂停按钮
        if (!_isInitialized)
          Center(
            child: GestureDetector(
              onTap: () {
                // 对于直播，点击播放按钮重新初始化播放器
                refreshLiveStream();
              },
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.play_arrow,
                  color: Colors.white,
                  size: 30,
                ),
              ),
            ),
          ),

        // 底部控制栏 - 使用Align确保正确定位
        Align(
          alignment: Alignment.bottomCenter,
          child: Container(
            width: double.infinity,
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 20),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.6),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                // 播放/暂停按钮
                GestureDetector(
                  onTap: () {
                    if (_isInitialized) {
                      // 对于直播，暂停实际上是停止播放并清理资源
                      if (_liveController != null) {
                        _logger.info('🔄 用户点击暂停按钮，停止直播播放');
                        _stopAndCleanupPlayer();
                      }
                    } else {
                      // 重新开始播放
                      _logger.info('🔄 用户点击播放按钮，重新开始直播');
                      refreshLiveStream();
                    }
                  },
                  child: Icon(
                    _isInitialized ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                    size: 24,
                  ),
                ),

                const SizedBox(width: 16),

                // 直播状态指示器
                Expanded(
                  child: Text(
                    _isInitialized ? '直播中' : '直播已暂停',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                  ),
                ),

                // 全屏按钮
                GestureDetector(
                  onTap: () {
                    _toggleFullScreen();
                  },
                  child: Icon(
                    _isFullScreen ? Icons.fullscreen_exit : Icons.fullscreen,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 切换全屏状态
  Future<void> _toggleFullScreen() async {
    try {
      _logger.info('切换全屏状态: ${_isFullScreen ? "退出全屏" : "进入全屏"}');

      if (_isFullScreen) {
        // 退出全屏：恢复竖屏
        await SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
        ]);
        await SystemChrome.setEnabledSystemUIMode(
          SystemUiMode.edgeToEdge,
          overlays: SystemUiOverlay.values,
        );
        _logger.info('已恢复竖屏模式');
      } else {
        // 进入全屏：切换到横屏
        await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
        await SystemChrome.setPreferredOrientations([
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight,
        ]);
        _logger.info('已切换到横屏模式');
        // 确保全屏状态下屏幕常亮
        _enableWakelock();
      }

      // 等待屏幕方向切换完成
      await Future.delayed(const Duration(milliseconds: 300));

      // 更新状态
      if (mounted) {
        setState(() {
          _isFullScreen = !_isFullScreen;
        });
        widget.onFullscreenChanged?.call();
        _logger.info('全屏状态已更新: $_isFullScreen');
      }
    } catch (e) {
      _logger.severe('切换全屏状态失败: $e');
    }
  }

  /// 返回事件处理 - 按照官方文档标准实现
  Future<bool> onWillPop() async {
    // 如果当前是全屏状态，先退出全屏
    if (_isFullScreen) {
      _toggleFullScreen();
      return false; // 阻止页面退出，先处理全屏退出
    }
    // TXLivePlayerController 不需要特殊的返回处理
    return true;
  }
}
