import 'package:flutter/material.dart';
import 'package:estockcafe/services/home_service.dart';
import 'package:estockcafe/services/splash_ad_service.dart';
import 'package:estockcafe/models/splash_ad.dart';
import 'package:estockcafe/config/config.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:async';
import '../services/navigation_service.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
// 导入main.dart中的等待服务初始化函数
import '../main.dart';

class SplashScreen extends StatefulWidget {
  final Widget child;
  final VoidCallback? onSplashComplete;

  // 静态标记，确保广告只被加载一次
  static bool hasShownAd = false;

  const SplashScreen({
    super.key,
    required this.child,
    this.onSplashComplete,
  });

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final HomeService _homeService = HomeService();
  final SplashAdService _adService = SplashAdService();
  final NavigationService _navigationService = NavigationService();
  bool _showAd = true;
  SplashAd? _splashAd;
  int _countdown = 3;
  bool _isImageLoaded = false;
  static const String _defaultAdImage = 'assets/images/default_ad.jpg';
  bool _isNavigating = false;
  bool _isInitializing = false;
  // 跟踪服务初始化状态
  bool _servicesReady = false;

  @override
  void initState() {
    super.initState();
    // 如果已经显示过广告，直接跳过
    if (SplashScreen.hasShownAd) {
      debugPrint('已经显示过广告，直接跳转到首页');
      _navigateToHome();
    } else {
      debugPrint('首次启动，尝试显示广告');
      _initializeApp();
    }

    // 在后台预加载首页数据和用户认证状态
    _preloadData();
  }

  // 预加载所有必要数据
  Future<void> _preloadData() async {
    // 延迟执行，避免在build过程中触发状态更新
    Future.microtask(() async {
      try {
        // 预加载首页数据
        await _preloadHomeData();

        // 预加载认证状态
        await _preloadAuthStatus();
      } catch (e) {
        debugPrint('预加载数据失败: $e');
      }
    });
  }

  // 预加载首页数据
  Future<void> _preloadHomeData() async {
    try {
      // 并行加载多个首页数据
      await Future.wait([
        _homeService.fetchHomeSlider(),
        _homeService.fetchHomeMenu(),
        _homeService.fetchDailyRecommendations(),
        // 可以根据需要添加更多首页数据的预加载
      ]);
      debugPrint('首页数据预加载完成');
    } catch (e) {
      debugPrint('首页数据预加载失败: $e');
      // 预加载失败不影响主流程
    }
  }

  // 预加载用户认证状态
  Future<void> _preloadAuthStatus() async {
    try {
      debugPrint('开始预加载用户认证状态');
      if (_navigationService.navigatorKey.currentContext != null) {
        final authProvider = Provider.of<AuthProvider>(
            _navigationService.navigatorKey.currentContext!,
            listen: false);

        await authProvider.checkAuthStatus();
        debugPrint('用户认证状态预加载完成');
      } else {
        debugPrint('无法获取context，跳过认证状态预加载');
      }
    } catch (e) {
      debugPrint('用户认证状态预加载失败: $e');
    }
  }

  Future<void> _initializeApp() async {
    // 防止重复初始化
    if (_isInitializing) {
      return;
    }
    _isInitializing = true;

    // 添加初始加载超时计时器
    Timer? timeoutTimer;
    bool adStartedDisplaying = false;

    // 启动服务初始化，与广告同步进行
    _startServicesInitialization();

    timeoutTimer = Timer(const Duration(seconds: 5), () {
      // 调整为5秒超时
      // 只有在广告尚未开始显示时才跳过
      if (mounted && _showAd && !adStartedDisplaying) {
        debugPrint('广告加载超时，跳过广告显示');
        if (mounted) {
          setState(() {
            _showAd = false;
          });
        }
        _navigateToHome();
      }
    });

    try {
      // 获取当前要显示的广告（从缓存或服务器）
      _splashAd = await _adService.fetchSplashAd();
      debugPrint('获取到广告: ${_splashAd != null ? '成功' : '无广告'}');

      if (_splashAd != null && mounted && _showAd) {
        // 标记广告已开始显示，防止被超时打断
        adStartedDisplaying = true;

        // 标记已经显示过广告
        SplashScreen.hasShownAd = true;
        debugPrint('设置广告已显示标记: ${SplashScreen.hasShownAd}');

        // 设置广告显示倒计时
        _countdown = _splashAd!.displayDuration;

        // 预加载网络图片
        if (!_splashAd!.isLocalImage) {
          debugPrint('开始预加载网络图片: ${_splashAd!.imageUrl}');
          final imageProvider = NetworkImage(_splashAd!.imageUrl);
          final imageStream = imageProvider.resolve(ImageConfiguration.empty);
          final completer = Completer<void>();

          final listener = ImageStreamListener(
            (ImageInfo info, bool synchronousCall) {
              debugPrint('网络图片加载成功');
              if (!completer.isCompleted) {
                completer.complete();
                if (mounted) {
                  setState(() {
                    _isImageLoaded = true;
                  });
                }
              }
            },
            onError: (exception, stackTrace) {
              debugPrint('网络图片加载失败: $exception');
              if (!completer.isCompleted) {
                completer.complete();
              }
            },
          );

          imageStream.addListener(listener);

          // 设置超时
          Timer(const Duration(seconds: 3), () {
            if (!completer.isCompleted) {
              debugPrint('图片加载超时，使用默认图片');
              completer.complete();
              if (mounted) {
                setState(() {
                  _isImageLoaded = true;
                });
              }
            }
          });
        } else {
          // 本地图片直接标记为已加载
          _isImageLoaded = true;
        }

        if (mounted) {
          setState(() {});
        }

        // 启动倒计时
        Timer.periodic(const Duration(seconds: 1), (timer) {
          if (_countdown <= 0 || !mounted || !_showAd) {
            timer.cancel();
            if (mounted && _showAd) {
              setState(() {
                _showAd = false;
              });
              _navigateToHome();
            }
          } else {
            if (mounted) {
              setState(() {
                _countdown--;
              });
            }
          }
        });

        // 在后台预加载下一次的广告
        _adService.fetchAndCacheNextAd();
      } else if (mounted) {
        // 如果没有广告或广告加载失败，直接跳转到主页
        setState(() {
          _showAd = false;
        });
        _navigateToHome();
      }
    } catch (e) {
      debugPrint('初始化失败: $e');
      if (mounted) {
        setState(() {
          _showAd = false;
        });
        _navigateToHome();
      }
    }
  }

  // 启动服务初始化并监听完成状态
  Future<void> _startServicesInitialization() async {
    debugPrint('开始监听服务初始化进度');
    try {
      // 等待服务初始化完成
      await waitForServicesInitialized();
      debugPrint('所有服务已初始化完成');
      if (mounted) {
        setState(() {
          _servicesReady = true;
        });
      }
    } catch (e) {
      debugPrint('服务初始化监听失败: $e');
    }
  }

  void _navigateToHome() {
    if (_isNavigating) {
      return;
    }
    _isNavigating = true;

    // 如果服务尚未初始化完成，等待它们完成
    if (!_servicesReady) {
      debugPrint('等待服务初始化完成后再跳转到首页...');
      waitForServicesInitialized().then((_) {
        debugPrint('服务初始化完成，准备跳转到首页');
        _completeNavigation();
      }).catchError((error) {
        debugPrint('等待服务初始化失败: $error，强制跳转到首页');
        _completeNavigation();
      });
    } else {
      debugPrint('服务已初始化，直接跳转到首页');
      _completeNavigation();
    }
  }

  void _completeNavigation() {
    if (mounted) {
      if (widget.onSplashComplete != null) {
        widget.onSplashComplete!();
      }
      debugPrint('跳转到首页完成');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (_showAd)
          Positioned.fill(
            child: Material(
              color: Colors.white,
              child: Stack(
                fit: StackFit.expand,
                children: [
                  if (_splashAd != null)
                    GestureDetector(
                      onTap: _onAdTap,
                      child: _splashAd!.isLocalImage
                          ? Container(
                              width: MediaQuery.of(context).size.width,
                              height: MediaQuery.of(context).size.height,
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: AssetImage(_splashAd!.imageUrl),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            )
                          : Stack(
                              children: [
                                if (!_isImageLoaded)
                                  const Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                Container(
                                  width: MediaQuery.of(context).size.width,
                                  height: MediaQuery.of(context).size.height,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: NetworkImage(_splashAd!.imageUrl),
                                      fit: BoxFit.cover,
                                      onError: (exception, stackTrace) {
                                        debugPrint('加载广告图片失败: $exception');
                                        if (mounted) {
                                          setState(() {
                                            _isImageLoaded = true;
                                          });
                                        }
                                      },
                                    ),
                                  ),
                                ),
                              ],
                            ),
                    )
                  else
                    Container(
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.height,
                      decoration: const BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(_defaultAdImage),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  if (_splashAd?.skipable == true)
                    Positioned(
                      top: MediaQuery.of(context).padding.top + 20,
                      right: 20,
                      child: Material(
                        color: Colors.black38,
                        borderRadius: BorderRadius.circular(20),
                        child: InkWell(
                          onTap: () {
                            setState(() {
                              _showAd = false;
                            });
                            _navigateToHome();
                          },
                          borderRadius: BorderRadius.circular(20),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            child: Text(
                              '跳过 $_countdown',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Future<void> _onAdTap() async {
    if (_splashAd == null) return;

    try {
      // 处理相对路径
      String url = _splashAd!.linkUrl;
      if (url.startsWith('/')) {
        url = '${AppConfig.baseUrl}$url';
      }

      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
    } catch (e) {
      debugPrint('打开广告链接失败: $e');
    }
  }
}
