import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../widgets/bottom_navbar.dart';
import '../widgets/user/user_info.dart';
import '../widgets/payment_widget.dart';
import '../theme.dart';
import '../services/user_service.dart';
import '../utils/logging.dart';
import 'package:logging/logging.dart';

class VipPage extends StatefulWidget {
  const VipPage({super.key});

  @override
  _VipPageState createState() => _VipPageState();
}

class _VipPageState extends State<VipPage> {
  int _selectedUserType = 8; // 默认选中普通会员
  int? _selectedPaymentMethod; // 添加选中的支付方式
  bool _isLoading = false;
  String _paymentInfo = '';
  bool _showPaymentInfo = false;
  double _currentPrice = 0;
  List<Map<String, dynamic>> _membershipTypes = [];
  List<String> _benefits = [];
  List<String> _premiumBenefits = [];
  List<Map<String, dynamic>> _paymentMethods = [];

  final UserService _userService = UserService();
  final Logger _logger = Logging.getLogger('VipPage');

  @override
  void initState() {
    super.initState();
    _loadMembershipData();
  }

  Future<void> _loadMembershipData() async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
    });
    try {
      _logger.info('开始加载会员数据');
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final isLoggedIn = authProvider.isAuthenticated;
      _logger.info('用户登录状态: $isLoggedIn');

      final data = await _userService.loadMembershipData();
      _logger.info('成功获取会员数据');

      if (!mounted) return;
      setState(() {
        _membershipTypes = data['membershipTypes'];
        _benefits = data['benefits'];
        _premiumBenefits = data['premiumBenefits'];
        _paymentMethods = data['paymentMethods'] ?? [];
        _isLoading = false;
      });
      _logger.info('会员数据已更新到状态');
    } catch (e) {
      _logger.severe('加载会员数据失败: $e');
      if (!mounted) return;

      // 先显示错误信息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载会员数据失败：$e')),
      );

      // 然后更新加载状态
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshPage(BuildContext context) async {
    _logger.info('刷新页面');
    await _loadMembershipData();
    if (!mounted) return;
    setState(() {
      _showPaymentInfo = false;
    });
  }

  void _handleMembershipSelection(int userType, int price) {
    if (!mounted) return;
    setState(() {
      _selectedUserType = userType;
      _showPaymentInfo = false; // 重置支付信息显示状态
      _currentPrice = price.toDouble();
    });
    _logger.info('选择会员类型: $userType, 价格: $price');
  }

  void _handlePaymentComplete(bool success,
      {String? paymentType, Map<String, dynamic>? paymentResult}) {
    if (!mounted) return;

    if (success) {
      _logger.info('支付成功');
      // 支付成功，显示科技感弹窗
      final successMessage = paymentResult?['success_message'] ?? '会员购买成功！';

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Dialog(
            backgroundColor: Colors.transparent,
            child: Container(
              width: MediaQuery.of(context).size.width * 0.85,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF1A1A2E),
                    Color(0xFF16213E),
                    Color(0xFF0F3460),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.cyan.withValues(alpha: 0.3),
                    blurRadius: 20,
                    spreadRadius: 2,
                  ),
                  BoxShadow(
                    color: Colors.blue.withValues(alpha: 0.2),
                    blurRadius: 40,
                    spreadRadius: 5,
                  ),
                ],
                border: Border.all(
                  color: Colors.cyan.withValues(alpha: 0.5),
                  width: 1,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 成功图标
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            Colors.green.withValues(alpha: 0.8),
                            Colors.green.withValues(alpha: 0.3),
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.green.withValues(alpha: 0.5),
                            blurRadius: 20,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.check_circle_outline,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                    const SizedBox(height: 20),

                    // 标题
                    ShaderMask(
                      shaderCallback: (bounds) => const LinearGradient(
                        colors: [Colors.cyan, Colors.blue, Colors.purple],
                      ).createShader(bounds),
                      child: const Text(
                        '支付成功',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // 成功消息
                    Text(
                      successMessage,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 32),

                    // 按钮区域
                    Row(
                      children: [
                        // 取消按钮
                        Expanded(
                          child: Container(
                            height: 48,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(24),
                              border: Border.all(
                                color: Colors.white30,
                                width: 1,
                              ),
                            ),
                            child: TextButton(
                              onPressed: () {
                                Navigator.of(context).pop(); // 只关闭弹窗
                              },
                              style: TextButton.styleFrom(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(24),
                                ),
                              ),
                              child: const Text(
                                '继续购买',
                                style: TextStyle(
                                  color: Colors.white70,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),

                        // 确定按钮
                        Expanded(
                          child: Container(
                            height: 48,
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Colors.cyan, Colors.blue],
                              ),
                              borderRadius: BorderRadius.circular(24),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.cyan.withValues(alpha: 0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: TextButton(
                              onPressed: () {
                                Navigator.of(context).pop(); // 关闭弹窗
                                _refreshAndNavigateToVip();
                              },
                              style: TextButton.styleFrom(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(24),
                                ),
                              ),
                              child: const Text(
                                '查看记录',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );
    } else {
      setState(() {
        _paymentInfo = '支付失败，请重试。';
        _logger.warning('支付失败');
        _showPaymentInfo = true;
      });
    }
  }

  void _handlePaymentMethodChange(int methodId) {
    if (!mounted) return;
    setState(() {
      _selectedPaymentMethod = methodId;
    });
  }

  Future<void> _refreshAndNavigateToVip() async {
    if (!mounted) return;
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.refreshUserInfo();
    if (mounted) {
      Navigator.pushReplacementNamed(context, '/user/vip_records');
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);

    // Check if user is not logged in
    if (!authProvider.isAuthenticated) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('会员中心'),
          centerTitle: true,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                '请先登录以访问会员功能',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/user/login');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryGreen,
                  foregroundColor: AppColors.primaryWhite,
                ),
                child: const Text('去登录'),
              ),
            ],
          ),
        ),
        bottomNavigationBar: const BottomNavBar(currentIndex: 2),
      );
    }

    final isLoggedIn = authProvider.isAuthenticated;

    // 获取当前选中会员类型的价格
    for (var type in _membershipTypes) {
      if (type['id'] == _selectedUserType) {
        _currentPrice = type['price'].toDouble();
        break;
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('会员中心'),
      ),
      body: RefreshIndicator(
        onRefresh: () => _refreshPage(context),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 50),
                const UserInfoBox(),
                const SizedBox(height: 10),
                _isLoading
                    ? const Center(
                        child: SpinKitPulsingGrid(
                          size: 20,
                          color: AppColors.primaryGreen,
                        ),
                      )
                    : Row(
                        children: _membershipTypes
                            .map((type) => Expanded(
                                  child: _buildMembershipCard(
                                    type['id'] as int,
                                    type['title'] as String,
                                    type['price'],
                                    _selectedUserType == type['id'],
                                  ),
                                ))
                            .toList(),
                      ),
                const SizedBox(height: 20),
                Container(
                  decoration: BoxDecoration(
                    color: AppColors.primaryWhite,
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: AppColors.secondaryLightGreen),
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const Text(
                        '成为会员后，您将可以：',
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 10),
                      _buildBenefitsList(),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                if (_showPaymentInfo)
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.secondaryLightGreen,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(_paymentInfo),
                  ),
                PaymentWidget(
                  paymentMethods: _paymentMethods,
                  paymentParams: _membershipTypes.isNotEmpty
                      ? {
                          'userType': _selectedUserType,
                          'price': _currentPrice,
                          'type': 'vip',
                          'membershipType': _membershipTypes.firstWhere(
                              (type) => type['userType'] == _selectedUserType,
                              orElse: () => {
                                    'userType': _selectedUserType,
                                    'name': 'default'
                                  })['name'],
                          'payment_method': _selectedPaymentMethod,
                        }
                      : {},
                  onPaymentComplete: _handlePaymentComplete,
                  onPaymentMethodChange: _handlePaymentMethodChange,
                ),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: const BottomNavBar(currentIndex: 2),
    );
  }

  Widget _buildMembershipCard(
      int userType, String title, int price, bool isSelected) {
    return Card(
      color: isSelected ? AppColors.secondaryLightGreen : Colors.white,
      child: InkWell(
        onTap: () => _handleMembershipSelection(userType, price),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                title,
                style:
                    const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                '¥ $price',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: isSelected ? AppColors.primaryGreen : Colors.black,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '有效期12个月',
                style: TextStyle(
                  color: isSelected ? AppColors.primaryGreen : Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBenefitsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ..._benefits.map((benefit) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  const Icon(Icons.check,
                      color: AppColors.primaryGreen, size: 20),
                  const SizedBox(width: 8),
                  Expanded(child: Text(benefit)),
                ],
              ),
            )),
        const SizedBox(height: 5),
        if (_selectedUserType == 9)
          ..._premiumBenefits.map((benefit) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    const Icon(Icons.check,
                        color: AppColors.primaryGreen, size: 20),
                    const SizedBox(width: 8),
                    Expanded(child: Text(benefit)),
                  ],
                ),
              )),
      ],
    );
  }
}
