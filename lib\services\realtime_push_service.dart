import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/chat_message.dart';
import '../utils/web_socket.dart';
import 'chat_notification_manager.dart';
import 'global_chat_service.dart';
import '../config/config.dart';

/// 实时推送服务 - 实现基于WebSocket的实时推送机制
class RealtimePushService with ChangeNotifier {
  static final RealtimePushService _instance = RealtimePushService._internal();
  factory RealtimePushService() => _instance;
  
  RealtimePushService._internal() {
    _logger.level = Level.INFO;
    _initialize();
  }

  final Logger _logger = Logger('RealtimePushService');
  final ChatNotificationManager _notificationManager = ChatNotificationManager();
  final GlobalChatService _chatService = GlobalChatService();
  
  // WebSocket连接
  WebSocketUtility? _webSocket;
  
  // 轮询定时器
  Timer? _pollTimer;
  
  // 连接状态
  bool _isConnected = false;
  bool _isBackground = false;
  DateTime? _lastMessageTime;
  
  // 重连尝试次数
  int _reconnectAttempts = 0;
  
  // 用户信息
  String? _userId;
  
  // 推送设置
  bool _pushEnabled = true;
  int _backgroundPollIntervalSeconds = 60;
  
  // 控制器
  final StreamController<PushEvent> _eventController = 
      StreamController<PushEvent>.broadcast();
  
  // 公开属性
  Stream<PushEvent> get eventStream => _eventController.stream;
  bool get isConnected => _isConnected;
  bool get isBackground => _isBackground;
  DateTime? get lastMessageTime => _lastMessageTime;
  bool get pushEnabled => _pushEnabled;
  int get backgroundPollIntervalSeconds => _backgroundPollIntervalSeconds;
  set backgroundPollIntervalSeconds(int value) {
    if (value >= 30 && value <= 300) {
      _backgroundPollIntervalSeconds = value;
      _savePushSettings();
      
      // 如果正在后台轮询，更新轮询间隔
      if (_isBackground && _pollTimer != null) {
        _startBackgroundPolling();
      }
    }
  }
  
  // 初始化
  Future<void> _initialize() async {
    try {
      // 加载推送设置
      await _loadPushSettings();
      
      // 注册应用生命周期监听
      WidgetsBinding.instance.addObserver(_AppLifecycleObserver(this));
      
      _logger.info('实时推送服务初始化完成');
    } catch (e) {
      _logger.severe('实时推送服务初始化失败: $e');
    }
  }
  
  // 加载推送设置
  Future<void> _loadPushSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _pushEnabled = prefs.getBool('push_enabled') ?? true;
      _backgroundPollIntervalSeconds = prefs.getInt('background_poll_interval_seconds') ?? 60;
      
      _logger.info('已加载推送设置: 推送已${_pushEnabled ? "启用" : "禁用"}, 后台轮询间隔: $_backgroundPollIntervalSeconds秒');
    } catch (e) {
      _logger.severe('加载推送设置失败: $e');
    }
  }
  
  // 保存推送设置
  Future<void> _savePushSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('push_enabled', _pushEnabled);
      await prefs.setInt('background_poll_interval_seconds', _backgroundPollIntervalSeconds);
      
      _logger.info('已保存推送设置');
    } catch (e) {
      _logger.severe('保存推送设置失败: $e');
    }
  }
  
  // 设置用户信息
  void setUserInfo(String? userId, String? userName) {
    bool userChanged = _userId != userId;
    
    _userId = userId;
    
    // 如果用户变更，需要重新连接
    if (userChanged && _userId != null) {
      _connectWebSocket();
    } else if (userChanged && _userId == null) {
      // 用户登出，断开连接
      _disconnectWebSocket();
    }
  }
  
  // 设置应用状态
  void setAppState(AppLifecycleState state) {
    final bool wasBackground = _isBackground;
    
    // 更新应用状态
    _isBackground = state != AppLifecycleState.resumed;
    
    _logger.info('应用状态变更: ${state.toString()}, 后台状态: $_isBackground');
    
    // 状态变化时的处理
    if (!wasBackground && _isBackground) {
      // 进入后台
      _onEnterBackground();
    } else if (wasBackground && !_isBackground) {
      // 回到前台
      _onEnterForeground();
    }
  }
  
  // 进入后台
  void _onEnterBackground() {
    _logger.info('应用进入后台');
    
    // 断开WebSocket连接
    _disconnectWebSocket();
    
    // 启动后台轮询
    if (_pushEnabled) {
      _startBackgroundPolling();
    }
  }
  
  // 回到前台
  void _onEnterForeground() {
    _logger.info('应用回到前台');
    
    // 停止后台轮询
    _stopBackgroundPolling();
    
    // 重新连接WebSocket
    if (_pushEnabled && _userId != null) {
      _connectWebSocket();
    }
    
    // 检查是否有新消息
    _checkForNewMessages();
  }
  
  // 连接WebSocket
  Future<void> _connectWebSocket() async {
    // 如果已连接或用户ID为空，不进行连接
    if (_isConnected || _userId == null) {
      return;
    }
    
    try {
      // 构建WebSocket URL
      String wsUrl = '${AppConfig.chatWsUrl}/v1/push/$_userId';
      
      // 确保URL格式正确
      if (wsUrl.startsWith('https://')) {
        wsUrl = 'wss://${wsUrl.substring(8)}';
      } else if (!wsUrl.startsWith('wss://') && !wsUrl.startsWith('ws://')) {
        wsUrl = 'wss://$wsUrl';
      }
      
      _logger.info('连接推送WebSocket: $wsUrl');
      
      // 创建WebSocket连接
      _webSocket = WebSocketUtility(
        url: wsUrl,
        onOpen: _onConnectionOpen,
        onMessage: _onMessageReceived,
        onClose: _onConnectionClosed,
        onError: _onConnectionError,
      );
      
      // 连接
      await _webSocket?.connect(
        channelName: 'push',
        userId: _userId!,
      );
    } catch (e) {
      _logger.severe('连接推送WebSocket失败: $e');
      _scheduleReconnect();
    }
  }
  
  // 断开WebSocket连接
  void _disconnectWebSocket() {
    if (_webSocket != null) {
      _logger.info('断开推送WebSocket连接');
      _webSocket?.close();
      _webSocket = null;
      _isConnected = false;
      notifyListeners();
    }
  }
  
  // WebSocket连接打开
  void _onConnectionOpen() {
    _logger.info('推送WebSocket连接已打开');
    _isConnected = true;
    _reconnectAttempts = 0;
    
    // 发送订阅消息
    _sendSubscribeMessage();
    
    // 通知状态变化
    _eventController.add(PushEvent(
      type: PushEventType.connectionStateChanged,
      data: {'connected': true},
    ));
    
    notifyListeners();
  }
  
  // WebSocket连接关闭
  void _onConnectionClosed() {
    _logger.info('推送WebSocket连接已关闭');
    _isConnected = false;
    
    // 通知状态变化
    _eventController.add(PushEvent(
      type: PushEventType.connectionStateChanged,
      data: {'connected': false},
    ));
    
    // 如果不是后台状态，尝试重连
    if (!_isBackground && _pushEnabled && _userId != null) {
      _scheduleReconnect();
    }
    
    notifyListeners();
  }
  
  // WebSocket连接错误
  void _onConnectionError(dynamic error) {
    _logger.severe('推送WebSocket连接错误: $error');
    _isConnected = false;
    
    // 通知状态变化
    _eventController.add(PushEvent(
      type: PushEventType.connectionStateChanged,
      data: {'connected': false, 'error': error.toString()},
    ));
    
    // 如果不是后台状态，尝试重连
    if (!_isBackground && _pushEnabled && _userId != null) {
      _scheduleReconnect();
    }
    
    notifyListeners();
  }
  
  // 收到WebSocket消息
  void _onMessageReceived(dynamic data) {
    try {
      _logger.fine('收到推送消息: $data');
      
      // 更新最后消息时间
      _lastMessageTime = DateTime.now();
      
      // 解析消息
      Map<String, dynamic> message;
      if (data is String) {
        message = jsonDecode(data);
      } else if (data is Map<String, dynamic>) {
        message = data;
      } else {
        _logger.warning('收到无效的消息格式: ${data.runtimeType}');
        return;
      }
      
      // 处理不同类型的消息
      if (message['type'] == 'ping' || message['type'] == 'pong') {
        // 心跳消息，不需要特殊处理
        _logger.fine('收到心跳消息: ${message['type']}');
      } else if (message['type'] == 'push') {
        // 推送消息
        _processPushMessage(message);
      } else if (message['type'] == 'notification') {
        // 通知消息
        _processNotificationMessage(message);
      } else {
        // 其他类型的消息
        _logger.fine('收到其他类型消息: ${message['type']}');
        
        // 尝试作为聊天消息处理
        _processChatMessage(message);
      }
    } catch (e) {
      _logger.severe('处理推送消息失败: $e');
    }
  }
  
  // 处理推送消息
  void _processPushMessage(Map<String, dynamic> message) {
    try {
      if (message['data'] != null) {
        final data = message['data'];
        
        // 通知事件
        _eventController.add(PushEvent(
          type: PushEventType.newMessage,
          data: data is Map<String, dynamic> ? data : {'message': data},
        ));
        
        // 如果包含聊天消息，处理它
        if (data is Map<String, dynamic> && data['type'] == 'chat') {
          _processChatMessage(data);
        }
      }
    } catch (e) {
      _logger.severe('处理推送消息失败: $e');
    }
  }
  
  // 处理通知消息
  void _processNotificationMessage(Map<String, dynamic> message) {
    try {
      if (message['data'] != null) {
        final data = message['data'];
        
        // 通知事件
        _eventController.add(PushEvent(
          type: PushEventType.notification,
          data: data is Map<String, dynamic> ? data : {'message': data},
        ));
      }
    } catch (e) {
      _logger.severe('处理通知消息失败: $e');
    }
  }
  
  // 处理聊天消息
  void _processChatMessage(Map<String, dynamic> message) {
    try {
      // 尝试解析为聊天消息
      final chatMessage = ChatMessageData.fromJson(message);
      
      // 处理通知
      _notificationManager.processNewMessage(chatMessage);
      
      // 通知事件
      _eventController.add(PushEvent(
        type: PushEventType.chatMessage,
        data: {'message': message},
      ));
    } catch (e) {
      _logger.warning('处理聊天消息失败: $e');
    }
  }
  
  // 发送订阅消息
  void _sendSubscribeMessage() {
    if (!_isConnected || _webSocket == null) return;
    
    try {
      final message = {
        'type': 'subscribe',
        'userId': _userId,
        'channels': ['chat', 'notification'],
      };
      
      _webSocket?.send(jsonEncode(message));
      _logger.info('已发送订阅消息');
    } catch (e) {
      _logger.severe('发送订阅消息失败: $e');
    }
  }
  
  // 安排重连
  void _scheduleReconnect() {
    // 计算重连延迟（指数退避策略）
    final delay = _calculateReconnectDelay();
    _reconnectAttempts++;
    
    _logger.info('安排重连，延迟: ${delay}ms，尝试次数: $_reconnectAttempts');
    
    // 延迟重连
    Future.delayed(Duration(milliseconds: delay), () {
      if (!_isConnected && _pushEnabled && _userId != null && !_isBackground) {
        _connectWebSocket();
      }
    });
  }
  
  // 计算重连延迟
  int _calculateReconnectDelay() {
    // 指数退避策略，最小1秒，最大30秒
    final baseDelay = 1000; // 1秒
    final maxDelay = 30000; // 30秒
    
    // 计算延迟时间
    int delay = baseDelay * (1 << _reconnectAttempts);
    
    // 添加随机抖动，避免多个客户端同时重连
    delay = (delay * (0.8 + 0.4 * DateTime.now().millisecondsSinceEpoch % 100 / 100)).toInt();
    
    // 限制最大延迟
    return delay > maxDelay ? maxDelay : delay;
  }
  
  // 启动后台轮询
  void _startBackgroundPolling() {
    // 取消现有定时器
    _pollTimer?.cancel();
    
    // 创建新定时器
    _pollTimer = Timer.periodic(
      Duration(seconds: _backgroundPollIntervalSeconds),
      (_) => _pollForNewMessages(),
    );
    
    _logger.info('已启动后台轮询，间隔: $_backgroundPollIntervalSeconds秒');
  }
  
  // 停止后台轮询
  void _stopBackgroundPolling() {
    if (_pollTimer != null) {
      _pollTimer!.cancel();
      _pollTimer = null;
      _logger.info('已停止后台轮询');
    }
  }
  
  // 轮询新消息
  Future<void> _pollForNewMessages() async {
    if (!_pushEnabled || _userId == null) return;
    
    try {
      _logger.fine('轮询新消息');
      
      // 获取最后消息时间
      final lastTime = _lastMessageTime ?? DateTime.now().subtract(const Duration(days: 1));
      final timestamp = lastTime.millisecondsSinceEpoch ~/ 1000; // 转换为秒级时间戳
      
      // 请求新消息
      await _chatService.requestHistoryMessages(
        beforeTime: timestamp, // 使用秒级时间戳
        limit: 10, // 限制消息数量
      );
      
      _logger.fine('轮询新消息完成');
    } catch (e) {
      _logger.warning('轮询新消息失败: $e');
    }
  }
  
  // 检查新消息
  Future<void> _checkForNewMessages() async {
    if (!_pushEnabled || _userId == null) return;
    
    try {
      _logger.info('检查新消息');
      
      // 获取未读消息
      final unreadCount = await _chatService.getHistoryMessages();
      
      _logger.info('检查新消息完成，未读消息数: ${unreadCount.length}');
    } catch (e) {
      _logger.warning('检查新消息失败: $e');
    }
  }
  
  // 设置推送是否启用
  Future<void> setPushEnabled(bool enabled) async {
    if (_pushEnabled == enabled) return;
    
    _pushEnabled = enabled;
    await _savePushSettings();
    
    if (enabled) {
      // 如果启用推送，连接WebSocket或启动后台轮询
      if (_isBackground) {
        _startBackgroundPolling();
      } else if (_userId != null) {
        _connectWebSocket();
      }
    } else {
      // 如果禁用推送，断开WebSocket连接并停止后台轮询
      _disconnectWebSocket();
      _stopBackgroundPolling();
    }
    
    notifyListeners();
  }
  
  // 手动重连
  Future<void> reconnect() async {
    if (!_pushEnabled || _userId == null) return;
    
    // 断开现有连接
    _disconnectWebSocket();
    
    // 重置重连尝试次数
    _reconnectAttempts = 0;
    
    // 重新连接
    await _connectWebSocket();
  }
  
  // 释放资源
  void dispose() {
    _disconnectWebSocket();
    _stopBackgroundPolling();
    _eventController.close();
    super.dispose();
  }
}

/// 应用生命周期观察者
class _AppLifecycleObserver with WidgetsBindingObserver {
  final RealtimePushService _service;
  
  _AppLifecycleObserver(this._service) {
    WidgetsBinding.instance.addObserver(this);
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    _service.setAppState(state);
  }
  
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
  }
}

/// 推送事件
class PushEvent {
  final PushEventType type;
  final Map<String, dynamic> data;
  
  PushEvent({
    required this.type,
    required this.data,
  });
}

/// 推送事件类型
enum PushEventType {
  connectionStateChanged,
  newMessage,
  notification,
  chatMessage,
}
