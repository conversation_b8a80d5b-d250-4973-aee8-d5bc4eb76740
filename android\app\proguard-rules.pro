# 保留 com.google.errorprone.annotations 包中的所有类
-keep class com.google.errorprone.annotations.** { *; }

# 保留 javax.annotation 包中的所有类
-keep class javax.annotation.** { *; }

# 保留 javax.lang.model.element 包中的所有类
-keep class javax.lang.model.element.** { *; }

# 保留特定的错误类
-keep class com.google.errorprone.annotations.CanIgnoreReturnValue { *; }
-keep class com.google.errorprone.annotations.CheckReturnValue { *; }
-keep class com.google.errorprone.annotations.Immutable { *; }
-keep class com.google.errorprone.annotations.RestrictedApi { *; }
-keep class com.google.errorprone.annotations.InlineMe { *; }
-keep class javax.annotation.Nullable { *; }
-keep class javax.annotation.concurrent.GuardedBy { *; }
-keep class javax.annotation.concurrent.ThreadSafe { *; }

# 保留 com.google.crypto.tink 包中的所有类
-keep class com.google.crypto.tink.** { *; }

# 保留 com.google.api.client.http 包中的所有类
-keep class com.google.api.client.http.** { *; }

# 保留 com.google.api.client.http.javanet 包中的所有类
-keep class com.google.api.client.http.javanet.** { *; }

# 保留 org.joda.time 包中的所有类
-keep class org.joda.time.** { *; }

# 保留特定的 API 类
-keep class com.google.api.client.http.GenericUrl { *; }
-keep class com.google.api.client.http.HttpHeaders { *; }
-keep class com.google.api.client.http.HttpRequest { *; }
-keep class com.google.api.client.http.HttpRequestFactory { *; }
-keep class com.google.api.client.http.HttpResponse { *; }
-keep class com.google.api.client.http.HttpTransport { *; }
-keep class com.google.api.client.http.javanet.NetHttpTransport { *; }
-keep class com.google.api.client.http.javanet.NetHttpTransport$Builder { *; }
