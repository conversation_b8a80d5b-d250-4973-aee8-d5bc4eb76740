class Comment {
  final int id;
  final String author;
  final String content;
  final String date;

  Comment({
    required this.id,
    required this.author,
    required this.content,
    required this.date,
  });

  factory Comment.fromJson(Map<String, dynamic> json) {
    return Comment(
      id: int.tryParse(json['id'].toString()) ?? 0,
      author: json['author'] as String? ?? '',
      content: json['content'] as String? ?? '',
      date: json['date'] as String? ?? '',
    );
  }
}
