import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/chat_notification_service.dart';
import '../../providers/auth_provider.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final chatNotificationService = Provider.of<ChatNotificationService>(context);
    final authProvider = Provider.of<AuthProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
      ),
      body: ListView(
        children: [
          const SizedBox(height: 16),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              '聊天设置',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SwitchListTile(
            title: const Text('启用消息通知'),
            subtitle: const Text('开启后，收到新消息时会显示通知'),
            value: chatNotificationService.notificationsEnabled,
            onChanged: (value) {
              chatNotificationService.notificationsEnabled = value;
            },
          ),
          SwitchListTile(
            title: const Text('启用通知声音'),
            subtitle: const Text('开启后，收到新消息时会播放提示音'),
            value: chatNotificationService.notificationSoundEnabled,
            onChanged: (value) {
              chatNotificationService.notificationSoundEnabled = value;
            },
          ),
          SwitchListTile(
            title: const Text('只接收与我相关的消息通知'),
            subtitle: const Text('开启后，只有@你或私聊你的消息才会显示通知'),
            value: chatNotificationService.onlyShowRelevantMessages,
            onChanged: (value) {
              chatNotificationService.setOnlyShowRelevantMessages(value);
              // 如果用户已登录，设置当前用户ID
              if (authProvider.isAuthenticated && authProvider.user != null) {
                chatNotificationService.currentUserId = authProvider.user!.id.toString();
              }
            },
          ),
          SwitchListTile(
            title: const Text('仅接收@我的消息'),
            subtitle: const Text('开启后，只有@你的消息才会显示通知'),
            value: chatNotificationService.onlyMentionNotifications,
            onChanged: (value) {
              chatNotificationService.onlyMentionNotifications = value;
            },
          ),
          const Divider(),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              '免打扰设置',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ListTile(
            title: const Text('设置免打扰时段'),
            subtitle: const Text('在指定时间段内不接收通知'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              _showDoNotDisturbDialog(context, chatNotificationService);
            },
          ),
          const Divider(),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              '关于',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ListTile(
            title: const Text('版本信息'),
            subtitle: const Text('v1.0.0'),
          ),
          if (authProvider.isAuthenticated)
            ListTile(
              title: const Text('退出登录'),
              onTap: () async {
                final confirmed = await showDialog<bool>(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('确认退出'),
                    content: const Text('确定要退出登录吗？'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text('取消'),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        child: const Text('确定'),
                      ),
                    ],
                  ),
                );
                
                if (confirmed == true) {
                  await authProvider.logout();
                  Navigator.of(context).pushReplacementNamed('/login');
                }
              },
            ),
        ],
      ),
    );
  }
  
  void _showDoNotDisturbDialog(BuildContext context, ChatNotificationService service) {
    // 这里可以实现免打扰时段设置对话框
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('免打扰时段设置'),
        content: const Text('此功能正在开发中，敬请期待！'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
