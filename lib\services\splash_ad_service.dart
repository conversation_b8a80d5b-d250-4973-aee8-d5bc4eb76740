import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/splash_ad.dart';
import '../utils/logging.dart';
import '../services/cache_service.dart';
import '../config/config.dart';
import 'package:logging/logging.dart';

class SplashAdService {
  final Logger _logger = Logging.getLogger('SplashAdService');
  final String _baseUrl = AppConfig.baseUrl;
  final CacheService _cacheService = CacheService();
  static const String _splashAdCacheKey = 'splash_ad_data';
  static const String _lastShowTimeKey = 'last_splash_ad_show_time';
  static const String _defaultLocalImage = 'assets/images/default_ad.jpg';
  static const int _adShowIntervalHours = 4;

  Future<SplashAd?> fetchSplashAd() async {
    try {
      // 检查是否需要显示广告
      if (!await _shouldShowAd()) {
        _logger.info('根据显示间隔规则，此次启动不显示广告');
        return null;
      }

      // 先从缓存获取广告数据
      String? cachedAd;
      try {
        cachedAd = await _cacheService.getFromCache<String>(_splashAdCacheKey);
        if (cachedAd != null) {
          _logger.info('从缓存加载广告数据成功: $cachedAd');
        }
      } catch (e) {
        _logger.warning('获取缓存广告数据失败: $e');
      }

      if (cachedAd != null && cachedAd.isNotEmpty) {
        // 更新显示时间
        await _updateLastShowTime();
        try {
          final adData = SplashAd.fromJson(json.decode(cachedAd));
          _logger.info('成功解析缓存广告数据');
          
          // 后台预加载下一次广告
          _logger.info('后台预加载下一次广告');
          fetchAndCacheNextAd();
          
          return adData;
        } catch (e) {
          _logger.warning('解析缓存广告数据失败: $e');
          // 如果解析失败，继续尝试获取新广告
        }
      } else {
        _logger.info('缓存中没有广告数据，尝试从服务器获取');
      }
      
      // 使用更短的超时时间
      final response = await http.get(
        Uri.parse('$_baseUrl/splash-ad'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 5)); // 调整为5秒超时

      if (response.statusCode == 200) {
        _logger.info('获取广告成功: ${response.body}');
        final Map<String, dynamic> jsonResponse = json.decode(response.body);

        if (jsonResponse['code'] == 0 && jsonResponse['data'] != null) {
          final data = jsonResponse['data'] as Map<String, dynamic>;

          final displayDuration =
              int.tryParse(data['displayDuration']?.toString() ?? '3') ?? 3;

          final newAd = SplashAd(
            imageUrl: data['imageUrl'] as String,
            linkUrl: data['linkUrl'] as String,
            displayDuration: displayDuration,
            skipable: true,
            isLocalImage: false,
          );

          // 缓存新的广告数据
          final adJson = json.encode(newAd.toJson());
          _logger.info('缓存新广告数据: $adJson');
          
          // 使用getCachedData方法缓存数据
          await _cacheService.getCachedData<String>(
            _splashAdCacheKey,
            () => Future.value(adJson),
            cacheDuration: const Duration(hours: 24),
            forceRefresh: true,  // 强制刷新缓存
          );

          // 更新显示时间
          await _updateLastShowTime();
          return newAd;
        }
      }

      // 如果获取失败，使用默认广告
      _logger.info('从服务器获取广告失败，使用默认广告');
      return _getDefaultAd();
    } catch (e) {
      _logger.warning('获取广告失败: $e');
      // 发生错误时使用默认广告
      return _getDefaultAd();
    }
  }

  // 返回默认广告
  SplashAd _getDefaultAd() {
    _logger.info('使用默认本地广告');
    return SplashAd(
      imageUrl: _defaultLocalImage,
      linkUrl: '',
      displayDuration: 2, // 减少默认广告显示时间
      skipable: true,
      isLocalImage: true,
    );
  }

  // 检查是否应该显示广告
  Future<bool> _shouldShowAd() async {
    String? lastShowTimeStr;
    try {
      lastShowTimeStr = await _cacheService.getFromCache<String>(_lastShowTimeKey);
    } catch (e) {
      _logger.warning('获取上次显示时间失败: $e');
    }
    
    if (lastShowTimeStr == null || lastShowTimeStr.isEmpty) {
      return true; // 首次启动，显示广告
    }

    final lastShowTime = DateTime.parse(lastShowTimeStr);
    final now = DateTime.now();
    return now.difference(lastShowTime).inHours >= _adShowIntervalHours;
  }

  // 更新最后显示时间
  Future<void> _updateLastShowTime() async {
    // 使用getCachedData方法更新显示时间
    await _cacheService.getCachedData<String>(
      _lastShowTimeKey,
      () => Future.value(DateTime.now().toIso8601String()),
      cacheDuration: const Duration(days: 30),
    );
  }

  // 静默获取远程广告并缓存
  Future<void> fetchAndCacheNextAd() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/splash-ad'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 5)); // 调整为5秒超时

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = json.decode(response.body);

        if (jsonResponse['code'] == 0 && jsonResponse['data'] != null) {
          final data = jsonResponse['data'] as Map<String, dynamic>;

          final displayDuration =
              int.tryParse(data['displayDuration']?.toString() ?? '3') ?? 3;

          final newAd = SplashAd(
            imageUrl: data['imageUrl'] as String,
            linkUrl: data['linkUrl'] as String,
            displayDuration: displayDuration,
            skipable: true,
            isLocalImage: false,
          );

          // 缓存下一个广告
          final adJson = json.encode(newAd.toJson());
          _logger.info('缓存下一次广告数据: $adJson');
          
          // 使用getCachedData方法缓存数据
          await _cacheService.getCachedData<String>(
            _splashAdCacheKey,
            () => Future.value(adJson),
            cacheDuration: const Duration(hours: 24),
            forceRefresh: true,  // 强制刷新缓存
          );
          
          _logger.info('成功缓存下一次广告');
          
          // 预加载图片
          _preloadImage(data['imageUrl'] as String);
        }
      }
    } catch (e) {
      _logger.warning('缓存下一个广告失败: $e');
      // 错误处理，但不中断应用流程
    }
  }
  
  // 预加载图片
  Future<void> _preloadImage(String imageUrl) async {
    try {
      _logger.info('预加载广告图片: $imageUrl');
      
      // 使用HTTP请求预加载图片
      final response = await http.get(
        Uri.parse(imageUrl),
        headers: {'User-Agent': 'eStockCafe/1.0'},
      ).timeout(const Duration(seconds: 5));
      
      if (response.statusCode == 200) {
        _logger.info('广告图片预加载成功，大小: ${response.bodyBytes.length} 字节');
      } else {
        _logger.warning('广告图片预加载失败，状态码: ${response.statusCode}');
      }
    } catch (e) {
      _logger.warning('预加载图片时出错: $e');
    }
  }
}
