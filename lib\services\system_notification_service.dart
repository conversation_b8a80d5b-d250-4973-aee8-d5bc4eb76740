import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:logging/logging.dart';
import '../models/chat_message.dart';
import 'navigation_service.dart';
import 'chat_notification_service.dart';
import 'app_state_manager.dart';
import '../utils/app_state.dart';

// 全局共享的FlutterLocalNotificationsPlugin实例
final FlutterLocalNotificationsPlugin _globalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

/// 系统通知服务 - 专门处理系统级通知，不使用应用内通知
class SystemNotificationService {
  static final SystemNotificationService _instance =
      SystemNotificationService._internal();
  factory SystemNotificationService() => _instance;

  final Logger _logger = Logger('SystemNotificationService');
  final NavigationService _navigationService = NavigationService();

  // 添加已处理的系统消息ID集合
  static final Set<String> _processedSystemMessageIds = <String>{};

  // 使用全局共享的FlutterLocalNotificationsPlugin实例
  FlutterLocalNotificationsPlugin get _flutterLocalNotificationsPlugin =>
      _globalNotificationsPlugin;

  // 是否已初始化
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  // 获取全局通知插件实例的静态方法
  static FlutterLocalNotificationsPlugin getNotificationsPlugin() {
    return _globalNotificationsPlugin;
  }

  SystemNotificationService._internal();

  // 初始化通知服务
  Future<void> initialize() async {
    if (_isInitialized) {
      _logger.info('系统通知服务已初始化，跳过');
      return;
    }

    try {
      _logger.info('初始化系统通知服务');

      if (Platform.isAndroid) {
        // 重新初始化插件，确保不会出现MissingPluginException
        const AndroidInitializationSettings initializationSettingsAndroid =
            AndroidInitializationSettings('@mipmap/ic_launcher');
        const InitializationSettings initializationSettings =
            InitializationSettings(android: initializationSettingsAndroid);

        await _flutterLocalNotificationsPlugin.initialize(
          initializationSettings,
          onDidReceiveNotificationResponse: (NotificationResponse details) {
            _logger.info('收到通知响应: ${details.payload}');
            if (details.payload != null) {
              try {
                final payloadData =
                    json.decode(details.payload!) as Map<String, dynamic>;
                _handleNotificationTap(payloadData);
              } catch (e) {
                _logger.warning('解析通知负载失败: $e');
              }
            }
          },
        );

        // 创建通知渠道
        await _createNotificationChannels();
      }

      _isInitialized = true;
      _logger.info('系统通知服务初始化完成');
    } catch (e, stackTrace) {
      _logger.severe('初始化系统通知服务失败: $e');
      _logger.severe('错误堆栈: $stackTrace');
    }
  }

  // 创建通知渠道
  Future<void> _createNotificationChannels() async {
    if (Platform.isAndroid) {
      _logger.info('创建Android通知渠道');

      // 创建聊天消息通知渠道
      const AndroidNotificationChannel chatChannel = AndroidNotificationChannel(
        'chat_channel', // id
        '聊天消息', // name
        importance: Importance.high,
        description: '聊天室和私聊消息通知',
      );

      // 创建重要消息通知渠道
      const AndroidNotificationChannel importantChannel =
          AndroidNotificationChannel(
        'important_messages', // id - 与服务器发送的android_channel_id匹配
        '重要通知', // name
        importance: Importance.max,
        description: '重要系统消息和提醒',
        sound: RawResourceAndroidNotificationSound('notification_sound'),
        enableVibration: true,
      );

      // 注册通知渠道
      final androidPlugin = _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

      if (androidPlugin != null) {
        await androidPlugin.createNotificationChannel(chatChannel);
        await androidPlugin.createNotificationChannel(importantChannel);
        _logger.info('Android通知渠道创建完成');
      } else {
        _logger.warning('无法获取Android通知插件实现');
      }
    }
  }

  // 显示聊天消息通知
  Future<void> showChatNotification(ChatMessageData message) async {
    if (!_isInitialized) {
      _logger.warning('系统通知服务未初始化，尝试初始化');
      await initialize();
    }

    try {
      // 检查当前应用状态，如果在聊天室中，则不显示通知
      AppStateManager appStateManager = AppStateManager();

      // 获取应用当前状态和频道
      final appState = appStateManager.currentState;
      final currentChannelName = appStateManager.currentChannelName;

      // 只有当应用状态为CHATROOM_ACTIVE且频道名匹配时，才认为用户在当前聊天室中
      if (appState == AppState.CHATROOM_ACTIVE &&
          currentChannelName != null &&
          currentChannelName == message.channelName) {
        _logger.info('用户当前在聊天室中(${message.channelName})，跳过显示通知');
        return;
      }

      // 验证消息内容
      if (message.content.isEmpty) {
        _logger.warning('跳过显示空内容的通知');
        return;
      }

      // 如果是系统消息，需要特殊处理
      if (message.isSystem) {
        _logger.info('系统消息: ${message.content}');
        // 只有在内容非空且不是普通状态更新时才显示通知
        if (!_isStatusUpdateMessage(message.content)) {
          // 生成通知ID
          String notificationId = '${message.id}_${message.content}';

          // 检查是否已经处理过该系统消息
          if (_processedSystemMessageIds.contains(notificationId)) {
            _logger.info('跳过重复的系统消息通知: ${message.content}');
            return;
          }

          // 添加到已处理集合中
          _processedSystemMessageIds.add(notificationId);

          // 限制集合大小，避免内存泄漏
          if (_processedSystemMessageIds.length > 100) {
            _processedSystemMessageIds.remove(_processedSystemMessageIds.first);
          }

          _logger.info('显示系统通知: ${message.content}');

          // 显示系统通知
          await _showLocalNotification(
            title: '系统消息',
            body: message.content,
            payload: {
              'type': 'system',
              'messageId': message.id,
              'channel_name': message.channelName,
            },
          );
        }
        return;
      }

      _logger.info('显示聊天通知: ${message.userName} - ${message.content}');

      // 显示聊天消息通知
      await _showLocalNotification(
        title: message.userName,
        body: message.content,
        payload: {
          'type': 'chat',
          'messageId': message.id,
          'channel_name': message.channelName,
          'roomId': message.metadata?['room'] ?? message.channelName,
        },
      );
    } catch (e, stackTrace) {
      _logger.severe('显示聊天通知失败: $e');
      _logger.severe('错误堆栈: $stackTrace');
    }
  }

  // 显示本地通知
  Future<void> _showLocalNotification({
    required String title,
    required String body,
    required Map<String, dynamic> payload,
  }) async {
    try {
      _logger.info('显示本地通知: $title - $body');

      // 确保服务已初始化
      if (!_isInitialized) {
        await initialize();
      }

      // 获取通知渠道ID
      final String channelId =
          payload['type'] == 'system' ? 'important_messages' : 'chat_channel';

      _logger.info('使用通知渠道: $channelId');

      // 创建Android通知详情
      final AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        channelId,
        channelId == 'important_messages' ? '重要消息' : '聊天消息',
        importance: Importance.high,
        priority: Priority.high,
        channelDescription: '应用通知',
        showWhen: true,
        // 确保通知声音正确播放
        sound: channelId == 'important_messages'
            ? const RawResourceAndroidNotificationSound('notification_sound')
            : null,
        icon: '@mipmap/ic_launcher',
      );

      // 创建通知详情
      final NotificationDetails notificationDetails =
          NotificationDetails(android: androidDetails);

      // 创建通知负载
      final String notificationPayload = json.encode(payload);

      // 使用消息ID或内容的哈希值作为通知ID，确保相同消息不会创建多个通知
      final String messageId = payload['messageId'] ?? '';
      final int notificationId =
          messageId.isNotEmpty ? messageId.hashCode : (title + body).hashCode;

      _logger.info('使用通知ID: $notificationId, 基于消息ID: $messageId');

      // 在显示新通知前，取消可能存在的相同ID通知
      await _flutterLocalNotificationsPlugin.cancel(notificationId);

      // 显示通知
      _logger.info('准备调用flutter_local_notifications显示通知，ID: $notificationId');
      await _flutterLocalNotificationsPlugin.show(
        notificationId,
        title,
        body,
        notificationDetails,
        payload: notificationPayload,
      );

      _logger.info('本地通知已显示成功，ID: $notificationId');
    } catch (e, stackTrace) {
      _logger.severe('显示本地通知失败: $e');
      _logger.severe('错误堆栈: $stackTrace');

      // 尝试重新初始化插件并再次显示通知
      try {
        _logger.info('尝试重新初始化通知插件');
        _isInitialized = false;
        await initialize();

        // 重新创建通知详情
        final AndroidNotificationDetails androidDetails =
            AndroidNotificationDetails(
          payload['type'] == 'system' ? 'important_messages' : 'chat_channel',
          payload['type'] == 'system' ? '重要消息' : '聊天消息',
          importance: Importance.high,
          priority: Priority.high,
          channelDescription: '应用通知',
        );

        final NotificationDetails notificationDetails =
            NotificationDetails(android: androidDetails);

        final String notificationPayload = json.encode(payload);

        // 使用相同的通知ID策略
        final String messageId = payload['messageId'] ?? '';
        final int notificationId =
            messageId.isNotEmpty ? messageId.hashCode : (title + body).hashCode;

        await _flutterLocalNotificationsPlugin.cancel(notificationId);
        await _flutterLocalNotificationsPlugin.show(
          notificationId,
          title,
          body,
          notificationDetails,
          payload: notificationPayload,
        );

        _logger.info('重试显示通知成功');
      } catch (retryError) {
        _logger.severe('重试显示通知失败: $retryError');
      }
    }
  }

  // 处理通知点击事件
  void _handleNotificationTap(Map<String, dynamic> payload) {
    _logger.info('用户点击了通知: $payload');

    // 清除所有通知 - 点击任何通知都会清除所有通知
    try {
      _flutterLocalNotificationsPlugin.cancelAll();
      _logger.info('已取消所有通知');
    } catch (e) {
      _logger.warning('取消通知失败: $e');
    }

    // 获取消息ID，适用于所有类型通知
    final messageId = payload['messageId'];

    // 更新应用状态
    AppStateManager appStateManager = AppStateManager();
    appStateManager.updateState(AppState.APP_ACTIVE);

    // 直接导航到聊天页面
    _navigationService.navigatorKey.currentState?.pushNamedAndRemoveUntil(
      '/chat/check',
      (route) => false,
      arguments: {'messageId': messageId},
    );

    // 清除通知服务中的未读消息
    try {
      final chatNotificationService = ChatNotificationService();
      chatNotificationService.clearMessages();
      _logger.info('已清除所有未读消息');
    } catch (e) {
      _logger.warning('清除未读消息失败: $e');
    }

    _logger.info('已直接导航到聊天页面，消息ID: $messageId');
  }

  // 判断是否是状态更新消息
  bool _isStatusUpdateMessage(String content) {
    // 添加需要过滤的状态更新消息关键词
    final statusKeywords = [
      '进入房间',
      '离开房间',
      '连接成功',
      '断开连接',
      '重新连接',
    ];

    return statusKeywords.any((keyword) => content.contains(keyword));
  }

  // 发送测试消息通知
  Future<bool> sendTestNotification() async {
    if (!_isInitialized) {
      _logger.warning('系统通知服务未初始化，尝试初始化');
      await initialize();
    }

    try {
      _logger.info('发送测试通知');

      await _showLocalNotification(
        title: '测试通知',
        body: '这是一条测试通知 - ${DateTime.now().toString().substring(11, 19)}',
        payload: {
          'type': 'system', // 使用system类型而非test
          'messageId': 'test_${DateTime.now().millisecondsSinceEpoch}',
          'channel_name': 'test',
          'android_channel_id': 'important_messages', // 使用重要通知渠道
        },
      );

      return true;
    } catch (e) {
      _logger.severe('发送测试通知失败: $e');
      return false;
    }
  }

  // 处理FCM消息并显示通知
  Future<void> processFcmMessage(Map<String, dynamic> data) async {
    _logger.info('处理FCM消息: $data');

    try {
      // 提取关键信息
      final String? type = data['type'];
      final String? channelName = data['channel_name'] ?? data['channelName'];

      // 处理messageId可能是不同字段名或不同类型的情况
      String? messageId;
      if (data['messageId'] != null) {
        messageId = data['messageId'].toString();
      } else if (data['msg_id'] != null) {
        messageId = data['msg_id'].toString();
      } else if (data['message_id'] != null) {
        messageId = data['message_id'].toString();
      }

      final String? content = data['content'] ?? data['message'];

      // 处理senderId可能是int类型的情况
      String? senderId;
      if (data['senderId'] != null) {
        senderId = data['senderId'].toString();
      } else if (data['user_id'] != null) {
        senderId = data['user_id'].toString();
      }

      // 处理senderName可能是不同字段名的情况
      String senderName;
      if (data['senderName'] != null) {
        senderName = data['senderName'].toString();
      } else if (data['user_name'] != null) {
        senderName = data['user_name'].toString();
      } else {
        senderName = '新消息';
      }

      final String androidChannelId =
          data['android_channel_id'] ?? 'chat_channel';

      // 验证是否有足够信息显示通知
      if (content == null || content.trim().isEmpty) {
        _logger.warning('FCM消息内容为空，跳过通知');
        return;
      }

      // 创建一个唯一标识符，用于检测重复消息
      final String messageIdentifier = messageId ?? '${senderName}_${content}';

      // 检查是否是重复消息
      if (_processedSystemMessageIds.contains(messageIdentifier)) {
        _logger.info('跳过重复的FCM消息通知: $messageIdentifier');
        return;
      }

      // 添加到已处理集合中
      _processedSystemMessageIds.add(messageIdentifier);

      // 限制集合大小，避免内存泄漏
      if (_processedSystemMessageIds.length > 100) {
        _processedSystemMessageIds.remove(_processedSystemMessageIds.first);
      }

      // 检查当前应用状态，如果在聊天室中且频道名称匹配，则不显示通知
      AppStateManager appStateManager = AppStateManager();
      final appState = appStateManager.currentState;
      final currentChannelName = appStateManager.currentChannelName;

      _logger.info(
          '当前应用状态: ${appState.toString()}, 当前频道: $currentChannelName, 消息频道: $channelName');

      // 增强检查逻辑：如果应用状态为CHATROOM_ACTIVE且频道匹配，不显示任何通知
      bool isInChatroom = appState == AppState.CHATROOM_ACTIVE &&
          currentChannelName != null &&
          currentChannelName == channelName;

      if (isInChatroom) {
        _logger.info('用户当前在聊天室界面($channelName)，跳过FCM通知');
        return;
      }

      // 检查是否是系统类型消息
      bool isSystemMessage = type == 'system' || senderName == '系统消息';

      // 如果是系统消息，检查是否是状态更新消息
      if (isSystemMessage && _isStatusUpdateMessage(content)) {
        _logger.info('跳过FCM状态更新系统消息: $content');
        return;
      }

      // 确保消息内容不为空
      String displayContent = content.trim();
      if (displayContent.isEmpty) {
        displayContent = "新消息";
      }

      // 根据消息类型处理
      if (type == 'silent') {
        _logger.info('处理静默推送消息');

        // 对于静默推送，我们需要手动创建通知
        await _showLocalNotification(
          title: senderName,
          body: displayContent,
          payload: {
            'type': 'chat',
            'channel_name': channelName ?? 'general',
            'messageId': messageId ?? messageIdentifier,
            'android_channel_id': androidChannelId,
          },
        );
      } else {
        // 处理普通消息
        await _showLocalNotification(
          title: senderName,
          body: displayContent,
          payload: {
            'type': type ?? 'chat',
            'channel_name': channelName ?? 'general',
            'messageId': messageId ?? messageIdentifier,
            'android_channel_id': androidChannelId,
            // 添加更多数据，确保点击通知时能正确打开聊天页面
            'userId': senderId,
            'userName': senderName,
            'content': displayContent,
          },
        );
      }

      _logger.info('FCM消息通知已显示: $senderName - $displayContent');
    } catch (e, stackTrace) {
      _logger.severe('处理FCM消息失败: $e');
      _logger.severe('错误堆栈: $stackTrace');
    }
  }

  // 公共显示通知方法
  Future<void> showNotification({
    required String title,
    required String body,
    required Map<String, dynamic> payload,
    String? channelId,
  }) async {
    try {
      // 确定通知渠道
      final String effectiveChannelId = channelId ??
          (payload['type'] == 'system' ? 'important_messages' : 'chat_channel');

      // 添加渠道信息到负载中
      final Map<String, dynamic> updatedPayload = {
        ...payload,
        'channelId': effectiveChannelId,
      };

      // 增强错误处理，确保通知能正确显示
      try {
        // 尝试初始化（如果尚未初始化）
        if (!_isInitialized) {
          await initialize();
        }

        // 调用内部显示方法
        await _showLocalNotification(
          title: title,
          body: body,
          payload: updatedPayload,
        );

        _logger.info('通知显示成功: $title - $body');
      } catch (e, stackTrace) {
        _logger.severe('通知显示失败，尝试备用方式: $e');
        _logger.severe('错误堆栈: $stackTrace');

        // 尝试重新初始化并使用备用方法显示通知
        _isInitialized = false;
        try {
          await initialize();

          // 创建最简单的通知
          final AndroidNotificationDetails androidDetails =
              AndroidNotificationDetails(
            effectiveChannelId,
            effectiveChannelId == 'important_messages' ? '重要消息' : '聊天消息',
            importance: Importance.high,
            priority: Priority.high,
          );

          final NotificationDetails notificationDetails =
              NotificationDetails(android: androidDetails);

          final String notificationPayload = json.encode(updatedPayload);

          // 使用基本配置显示通知
          int notificationId = (title + body).hashCode;
          await _flutterLocalNotificationsPlugin.show(
              notificationId, title, body, notificationDetails,
              payload: notificationPayload);

          _logger.info('使用备用方式成功显示通知');
        } catch (secondError) {
          _logger.severe('备用通知方式也失败: $secondError');
        }
      }
    } catch (e, stackTrace) {
      _logger.severe('准备通知参数失败: $e');
      _logger.severe('错误堆栈: $stackTrace');
    }
  }
}
