import 'package:estockcafe/services/common.dart';
import 'package:estockcafe/services/post_service.dart';
import 'package:estockcafe/utils/functions.dart';
import 'package:estockcafe/utils/logging.dart';
import 'package:estockcafe/widgets/post/list_view.dart';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:estockcafe/widgets/bottom_navbar.dart';

class CategoryPage extends StatefulWidget {
  final String id;

  const CategoryPage({super.key, required this.id});

  @override
  _CategoryPageState createState() => _CategoryPageState();
}

class _CategoryPageState extends State<CategoryPage> {
  List<dynamic> articles = [];
  int currentPage = 1;
  bool isLoading = false;
  bool hasNext = true;
  String category = '';
  int categoryId = 0;

  final ScrollController _scrollController = ScrollController();
  final Logger _logger = Logging.getLogger('CategoryPage');

  @override
  void initState() {
    super.initState();

    _fetchArticles();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        if (!isLoading && hasNext) {
          _fetchArticles();
        }
      }
    });
  }

  Future<void> _fetchArticles() async {
    if (isLoading) {
      _logger.info('已经在加载中，跳过此次请求');
      return;
    }
    setState(() {
      isLoading = true;
    });
    try {
      final data = await PostService()
          .fetchArticlesByCategory(widget.id, page: currentPage);
      if (mounted) {
        setState(() {
          articles.addAll(data.posts);
          category = data.category;
          categoryId = data.categoryId;
          currentPage++;
          hasNext = data.hasNextPage;
        });
      } else {
        _logger.warning('Widget 已经被销毁，无法更新状态');
      }
    } catch (e, stackTrace) {
      _logger.severe('获取文章时出错: $e\n$stackTrace');
      if (mounted) {
        String errorMessage = '加载文章失败，请稍后重试';
        if (e is HttpException) {
          errorMessage = '网络错误 (${e.statusCode}): ${e.message}';
        } else if (e is FormatException) {
          errorMessage = '数据格式错误: ${e.message}';
        }
        showErrorDialog(context, errorMessage, _fetchArticles);
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
      _logger.info('文章获取完成');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          category,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        child: articles.isEmpty && isLoading
            ? const Center(
                child: SpinKitPulsingGrid(
                size: 20,
                color: Colors.grey,
              ))
            : articles.isEmpty
                ? const Center(child: Text('没有找到文章'))
                : PostListView(
                    articles: articles,
                    hasNext: hasNext,
                    scrollController: _scrollController),
      ),
      bottomNavigationBar: const BottomNavBar(currentIndex: -1),
    );
  }

  @override
  void dispose() {
    _logger.info('CategoryPage disposed');
    _scrollController.dispose();
    super.dispose();
  }
}
