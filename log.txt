D/EGL_emulation(25771): app_time_stats: avg=28.23ms min=2.20ms max=86.52ms count=36
W/liteav  (25771): [W][08-05/16:27:21.763+0.0][25771,26254][player_statistician.cc:97][1:67a0] SVR:171.105.193.130|SPD:1124|ARA:171,112|VRA:778  CPU:43|0|RES:1920*1080|GOP:2s  FPS:60,32(60|19),34,33  CACHE:222,20|4033ms,3683ms|1113ms,700ms  DIFF:6ms,2910ms  DROP:0|0  AUDIO:(48000,2)(48000,2)|61,94|1890  PT:259s|BT:0s|BR:0%|BC:0
I/flutter (25771): [16:27:22.615] SEVERE: LogService: 未捕获的Flutter错误: RenderAndroidView object was given an infinite size during layout.
I/flutter (25771): This probably means that it is a render object that tries to be as big as possible, but it was put inside another render object that allows its children to pick their own size.
I/flutter (25771): The nearest ancestor providing an unbounded width constraint is: RenderFittedBox#bee10 NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE:
I/flutter (25771):   creator: FittedBox ← ConstrainedBox ← Container ← Positioned ← Stack ← LayoutBuilder ← Builder ← MediaQuery ← Padding ← SafeArea ← KeyedSubtree-[GlobalKey#851ce] ← _BodyBuilder ← ⋯
I/flutter (25771):   parentData: <none> (can use size)
I/flutter (25771):   constraints: BoxConstraints(w=360.0, h=146.5)
I/flutter (25771):   size: MISSING
I/flutter (25771):   fit: cover
I/flutter (25771):   alignment: Alignment.center
I/flutter (25771):   textDirection: ltr
I/flutter (25771): The nearest ancestor providing an unbounded height constraint is: RenderFittedBox#bee10 NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE:
I/flutter (25771):   creator: FittedBox ← ConstrainedBox ← Container ← Positioned ← Stack ← LayoutBuilder ← Builder ← MediaQuery ← Padding ← SafeArea ← KeyedSubtree-[GlobalKey#851ce] ← _BodyBuilder ← ⋯
I/flutter (25771):   parentData: <none> (can use size)
I/flutter (25771):   constraints: BoxConstraints(w=360.0, h=146.5)
I/flutter (25771):   size: MISSING
I/flutter (25771):   fit: cover
I/flutter (25771):   alignment: Alignment.center
I/flutter (25771):   textDirection: ltr
I/flutter (25771): The constraints that applied to the RenderAndroidView were:
I/flutter (25771):   BoxConstraints(unconstrained)
I/flutter (25771): The exact size it was given was:
I/flutter (25771):   Size(Infinity, Infinity)
I/flutter (25771): See https://flutter.dev/to/unbounded-constraints for more information.
W/liteav  (25771): [W][08-05/16:27:22.653+0.0][25771,26255][audio_io_watchdog.cc:238][audio_log][audio-io]Audio total data size is under threshold: 0.08 expect is 2030, real is 1860, type is player , sample rate: 48000, channels: 2
D/TrafficStats(25771): tagSocket(257) with statsTag=0xffffffff, statsUid=-1
D/EGL_emulation(25771): app_time_stats: avg=29.02ms min=1.98ms max=115.21ms count=35
I/flutter (25771): ══╡ EXCEPTION CAUGHT BY RENDERING LIBRARY ╞═════════════════════════════════════════════════════════
I/flutter (25771): The following assertion was thrown during performResize():
I/flutter (25771): RenderAndroidView object was given an infinite size during layout.
I/flutter (25771): This probably means that it is a render object that tries to be as big as possible, but it was put
I/flutter (25771): inside another render object that allows its children to pick their own size.
I/flutter (25771): The nearest ancestor providing an unbounded width constraint is: RenderFittedBox#bee10 NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE:
I/flutter (25771):   creator: FittedBox ← ConstrainedBox ← Container ← Positioned ← Stack ← LayoutBuilder ← Builder ←
I/flutter (25771):     MediaQuery ← Padding ← SafeArea ← KeyedSubtree-[GlobalKey#851ce] ← _BodyBuilder ← ⋯
I/flutter (25771):   parentData: <none> (can use size)
I/flutter (25771):   constraints: BoxConstraints(w=360.0, h=146.5)
I/flutter (25771):   size: MISSING
I/flutter (25771):   fit: cover
I/flutter (25771):   alignment: Alignment.center
I/flutter (25771):   textDirection: ltr
I/flutter (25771): The nearest ancestor providing an unbounded height constraint is: RenderFittedBox#bee10 NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE:
I/flutter (25771):   creator: FittedBox ← ConstrainedBox ← Container ← Positioned ← Stack ← LayoutBuilder ← Builder ←
I/flutter (25771):     MediaQuery ← Padding ← SafeArea ← KeyedSubtree-[GlobalKey#851ce] ← _BodyBuilder ← ⋯
I/flutter (25771):   parentData: <none> (can use size)
I/flutter (25771):   constraints: BoxConstraints(w=360.0, h=146.5)
I/flutter (25771):   size: MISSING
I/flutter (25771):   fit: cover
I/flutter (25771):   alignment: Alignment.center
I/flutter (25771):   textDirection: ltr
I/flutter (25771): The constraints that applied to the RenderAndroidView were:
I/flutter (25771):   BoxConstraints(unconstrained)
I/flutter (25771): The exact size it was given was:
I/flutter (25771):   Size(Infinity, Infinity)
I/flutter (25771): See https://flutter.dev/to/unbounded-constraints for more information.
I/flutter (25771): 
I/flutter (25771): The relevant error-causing widget was:
I/flutter (25771):   TXPlayerVideo
I/flutter (25771):   TXPlayerVideo:file:///E:/KEHU/202304meigu/estockcafe/lib/widgets/live/super_live_player.dart:278:28
I/flutter (25771): 
I/flutter (25771): When the exception was thrown, this was the stack:
I/flutter (25771): #0      RenderBox.debugAssertDoesMeetConstraints.<anonymous closure> (package:flutter/src/rendering/box.dart:2610:9)
I/flutter (25771): #1      RenderBox.debugAssertDoesMeetConstraints (package:flutter/src/rendering/box.dart:2761:6)
I/flutter (25771): #2      RenderBox.size=.<anonymous closure> (package:flutter/src/rendering/box.dart:2353:7)
I/flutter (25771): #3      RenderBox.size= (package:flutter/src/rendering/box.dart:2355:6)
I/flutter (25771): #4      RenderBox.performResize (package:flutter/src/rendering/box.dart:2861:5)
I/flutter (25771): #5      RenderAndroidView.performResize (package:flutter/src/rendering/platform_view.dart:160:11)
I/flutter (25771): #6      RenderObject.layout (package:flutter/src/rendering/object.dart:2694:9)
I/flutter (25771): #7      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #8      RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #9      RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #10     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #11     RenderFittedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:2747:14)
I/flutter (25771): #12     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #13     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:293:14)
I/flutter (25771): #14     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #15     RenderStack.layoutPositionedChild (package:flutter/src/rendering/stack.dart:549:11)
I/flutter (25771): #16     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:684:13)
I/flutter (25771): #17     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #18     _RenderLayoutBuilder.performLayout (package:flutter/src/widgets/layout_builder.dart:392:14)
I/flutter (25771): #19     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #20     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:243:12)
I/flutter (25771): #21     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #22     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:180:12)
I/flutter (25771): #23     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1118:7)
I/flutter (25771): #24     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:249:7)
I/flutter (25771): #25     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:419:14)
I/flutter (25771): #26     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #27     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #28     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #29     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #30     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1483:11)
I/flutter (25771): #31     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #32     RenderPadding.performLayout (package:flutter/src/rendering/shifted_box.dart:243:12)
I/flutter (25771): #33     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #34     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #35     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #36     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #37     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #38     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:293:14)
I/flutter (25771): #39     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #40     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:62:11)
I/flutter (25771): #41     RenderFlex._computeSizes (package:flutter/src/rendering/flex.dart:1161:28)
I/flutter (25771): #42     RenderFlex.performLayout (package:flutter/src/rendering/flex.dart:1255:32)
I/flutter (25771): #43     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #44     RenderConstrainedBox.performLayout (package:flutter/src/rendering/proxy_box.dart:293:14)
I/flutter (25771): #45     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #46     _RenderSingleChildViewport.performLayout (package:flutter/src/widgets/single_child_scroll_view.dart:493:14)
I/flutter (25771): #47     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #48     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #49     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #50     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #51     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #52     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #53     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #54     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #55     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #56     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #57     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #58     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #59     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #60     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #61     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #62     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #63     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1483:11)
I/flutter (25771): #64     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #65     _RenderLayoutBuilder.performLayout (package:flutter/src/widgets/layout_builder.dart:392:14)
I/flutter (25771): #66     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #67     ChildLayoutHelper.layoutChild (package:flutter/src/rendering/layout_helper.dart:62:11)
I/flutter (25771): #68     RenderStack._computeSize (package:flutter/src/rendering/stack.dart:646:43)
I/flutter (25771): #69     RenderStack.performLayout (package:flutter/src/rendering/stack.dart:673:12)
I/flutter (25771): #70     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #71     MultiChildLayoutDelegate.layoutChild (package:flutter/src/rendering/custom_layout.dart:180:12)
I/flutter (25771): #72     _ScaffoldLayout.performLayout (package:flutter/src/material/scaffold.dart:1118:7)
I/flutter (25771): #73     MultiChildLayoutDelegate._callPerformLayout (package:flutter/src/rendering/custom_layout.dart:249:7)
I/flutter (25771): #74     RenderCustomMultiChildLayoutBox.performLayout (package:flutter/src/rendering/custom_layout.dart:419:14)
I/flutter (25771): #75     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #76     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #77     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #78     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #79     _RenderCustomClip.performLayout (package:flutter/src/rendering/proxy_box.dart:1483:11)
I/flutter (25771): #80     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #81     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #82     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #83     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #84     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #85     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #86     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #87     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #88     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #89     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #90     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #91     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #92     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #93     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #94     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #95     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #96     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #97     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #98     RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #99     RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/flutter (25771): #100    RenderOffstage.performLayout (package:flutter/src/rendering/proxy_box.dart:3750:13)
I/flutter (25771): #101    RenderObject.layout (package:flutter/src/rendering/object.dart:2715:7)
I/flutter (25771): #102    RenderProxyBoxMixin.performLayout (package:flutter/src/rendering/proxy_box.dart:115:18)
I/liteav  (25771): [I][08-05/16:27:23.339+0.0][25771,26254][live_player_video_jitter_buffer.cc:444][1:67a0] Prepare to drop frame. Current cache frame count :249, duration:4132
I/liteav  (25771): [I][08-05/16:27:23.339+0.0][25771,26259][rtp_video_stream_receiver2.cc:738][1:67a0] Packet received on SSRC: 18840776 with payload type: 98, timestamp: 1129494030, sequence number: 34395, arrival time: 47609506
W/liteav  (25771): [W][08-05/16:27:23.339+0.0][25771,26254][live_player_video_jitter_buffer.cc:512][1:67a0] Drop the front gop frames, size:121 pts: [2064585715,2064587730)
W/liteav  (25771): [W][08-05/16:27:23.341+0.0][25771,26254][live_player_video_jitter_buffer.cc:512][1:67a0] Drop the front gop frames, size:120 pts: [2064587730,2064589731)
I/liteav  (25771): [I][08-05/16:27:23.341+0.0][25771,26254][live_player_video_jitter_buffer.cc:454][1:67a0] Drop 241frames when video lags audio too much
E/liteav  (25771): [E][08-05/16:27:23.341+0.0][25771,26254][live_player_video_jitter_buffer.cc:777][1:67a0] Invalid consumer cache duration:4416ms.
I/liteav  (25771): [I][08-05/16:27:23.370+0.0][25771,26254][live_player_video_jitter_buffer.cc:444][1:67a0] Prepare to drop frame. Current cache frame count :13, duration:200
I/liteav  (25771): [I][08-05/16:27:23.370+0.0][25771,26254][live_player_video_jitter_buffer.cc:454][1:67a0] Drop 0frames when video lags audio too much
I/liteav  (25771): [I][08-05/16:27:23.394+0.0][25771,26254][live_player_video_jitter_buffer.cc:444][1:67a0] Prepare to drop frame. Current cache frame count :16, duration:250
I/liteav  (25771): [I][08-05/16:27:23.400+0.0][25771,26268][log_jni.cc:30][player_video]|[1:67a0]|[dec_wrap:@d8d0][[1:67a0]]HardwareVideoDecoder2: stop
I/liteav  (25771): [I][08-05/16:27:23.400+0.0][25771,26268][log_jni.cc:30][player_video]|[1:67a0]|[dec_wrap:@d8d0][[1:67a0]]HardwareVideoDecoder2: mediaCodec stop
I/liteav  (25771): [I][08-05/16:27:23.400+0.0][25771,26268][video_decoder_safe_wrapper.cc:707][player_video]|[1:67a0]|[dec_wrap:@d8d0][1:67a0] Sps data changed, decoder need to be recreated.
I/liteav  (25771): [I][08-05/16:27:23.400+0.0][25771,26268][video_decoder_safe_wrapper.cc:613][player_video]|[1:67a0]|[dec_wrap:@d8d0][1:67a0] Destroy decoder: internal-mediacodec-hardware-video-decoder
14
D/CCodecBufferChannel(25771): [c2.goldfish.h264.decoder#768] MediaCodec discarded an unknown buffer
D/SurfaceUtils(25771): disconnecting from surface 0x74b2849d8f00, reason disconnectFromSurface
I/liteav  (25771): [I][08-05/16:27:23.475+0.0][25771,26268][log_jni.cc:30][player_video]|[1:67a0]|[dec_wrap:@d8d0][[1:67a0]]HardwareVideoDecoder2: mediaCodec release
I/hw-BpHwBinder(25771): onLastStrongRef automatically unlinking death recipients
I/liteav  (25771): [I][08-05/16:27:23.496+0.0][25771,26268][log_jni.cc:30][player_video]|[1:67a0]|[dec_wrap:@d8d0][[1:67a0]]HardwareVideoDecoder2: Uninitialize surface
I/liteav  (25771): [I][08-05/16:27:23.505+0.0][25771,26268][log_jni.cc:30][player_video]|[1:67a0]|[dec_wrap:@d8d0][[1:67a0]]HardwareVideoDecoder2: Start: texture_id = 23
I/liteav  (25771): [I][08-05/16:27:23.496+0.0][25771,26268][video_decoder_impl_android.cc:70][player_video]|[1:67a0]|[dec_wrap:@d8d0][1:67a0] Decoder destroyed: input 481, decoded 475, output 461, cur_cache 20, min_cache 18
I/liteav  (25771): [I][08-05/16:27:23.496+0.0][25771,26268][video_decoder_decider.cc:137][player_video]|[1:67a0][1:67a0] Video resolution changed from Size{1920x1080} to Size{960x540}
I/liteav  (25771): [I][08-05/16:27:23.500+0.0][25771,26253][player_video