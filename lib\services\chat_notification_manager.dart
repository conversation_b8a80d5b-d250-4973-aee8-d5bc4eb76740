import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/chat_message.dart';
import 'chat_database_service.dart';
import 'chat_notification_service.dart';

/// 通知优先级
enum NotificationPriority {
  high,
  medium,
  low,
}

/// 未读消息摘要
class UnreadMessageSummary {
  final int totalUnreadCount;
  final int importantUnreadCount;
  final Map<String, int> channelUnreadCounts;
  final Map<String, int> channelImportantCounts;
  final DateTime lastUpdated;
  
  UnreadMessageSummary({
    required this.totalUnreadCount,
    required this.importantUnreadCount,
    required this.channelUnreadCounts,
    required this.channelImportantCounts,
    required this.lastUpdated,
  });
}

/// 聊天通知管理器 - 实现多维度未读消息统计和高级通知管理
class ChatNotificationManager {
  static final ChatNotificationManager _instance = ChatNotificationManager._internal();
  factory ChatNotificationManager() => _instance;
  
  ChatNotificationManager._internal() {
    _loadSettings();
    _logger.level = Level.INFO;
    _initialize();
  }

  final Logger _logger = Logger('ChatNotificationManager');
  final ChatNotificationService _notificationService = ChatNotificationService();
  
  // 数据库服务
  ChatDatabaseService? _databaseService;
  
  // 未读消息计数器
  final Map<String, int> _channelUnreadCount = {};
  final Map<String, int> _importantUnreadCount = {};
  final Map<String, DateTime> _lastReadTime = {};
  
  // 通知设置
  bool _notificationsEnabled = true;
  bool _onlyMentionNotifications = false;
  bool _notificationSoundEnabled = true;
  bool _mergeNotifications = true;
  bool _showAvatarInNotification = true;
  int _notificationExpiryDays = 7;
  
  // 免打扰时段
  TimeOfDay? _doNotDisturbStart;
  TimeOfDay? _doNotDisturbEnd;
  bool _doNotDisturbEnabled = false;
  
  // 用户ID
  String? _currentUserId;
  
  // 通知分级
  final Map<String, NotificationPriority> _notificationPriorities = {
    'reply': NotificationPriority.high,
    'mention': NotificationPriority.high,
    'private': NotificationPriority.high,
    'admin': NotificationPriority.medium,
    'system': NotificationPriority.medium,
    'normal': NotificationPriority.low,
  };
  
  // 控制器
  final StreamController<UnreadMessageSummary> _unreadSummaryController = 
      StreamController<UnreadMessageSummary>.broadcast();
  
  // 公开属性
  Stream<UnreadMessageSummary> get unreadSummaryStream => _unreadSummaryController.stream;
  bool get notificationsEnabled => _notificationsEnabled;
  bool get onlyMentionNotifications => _onlyMentionNotifications;
  bool get notificationSoundEnabled => _notificationSoundEnabled;
  bool get mergeNotifications => _mergeNotifications;
  bool get showAvatarInNotification => _showAvatarInNotification;
  int get notificationExpiryDays => _notificationExpiryDays;
  bool get doNotDisturbEnabled => _doNotDisturbEnabled;
  TimeOfDay? get doNotDisturbStart => _doNotDisturbStart;
  TimeOfDay? get doNotDisturbEnd => _doNotDisturbEnd;
  String? get currentUserId => _currentUserId;
  
  // 初始化
  Future<void> _initialize() async {
    // 注册监听通知服务的未读消息变化
    _notificationService.addListener(_onNotificationServiceChanged);
    
    // 加载通知设置
    await _loadNotificationSettings();
    
    _logger.info('聊天通知管理器初始化完成');
  }
  
  // 当通知服务状态变化时
  void _onNotificationServiceChanged() {
    _updateUnreadSummary();
  }
  
  // 设置用户ID
  void setUserId(String? userId) {
    _currentUserId = userId;
    
    if (userId != null) {
      _databaseService = ChatDatabaseService(userId: userId);
      _loadUnreadMessagesFromDatabase();
      
      // 同时设置通知服务的用户ID
      _notificationService.currentUserId = userId;
    }
  }
  
  // 加载用户设置
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _notificationsEnabled = prefs.getBool('chat_notifications_enabled') ?? true;
      _onlyMentionNotifications = prefs.getBool('only_mention_notifications') ?? false;
      _notificationSoundEnabled = prefs.getBool('notification_sound_enabled') ?? true;
      _mergeNotifications = prefs.getBool('merge_notifications') ?? true;
      _showAvatarInNotification = prefs.getBool('show_avatar_in_notification') ?? true;
      _notificationExpiryDays = prefs.getInt('notification_expiry_days') ?? 7;
      _doNotDisturbEnabled = prefs.getBool('do_not_disturb_enabled') ?? false;
      
      // 加载免打扰时段
      final startHour = prefs.getInt('do_not_disturb_start_hour');
      final startMinute = prefs.getInt('do_not_disturb_start_minute');
      final endHour = prefs.getInt('do_not_disturb_end_hour');
      final endMinute = prefs.getInt('do_not_disturb_end_minute');
      
      if (startHour != null && startMinute != null) {
        _doNotDisturbStart = TimeOfDay(hour: startHour, minute: startMinute);
      }
      
      if (endHour != null && endMinute != null) {
        _doNotDisturbEnd = TimeOfDay(hour: endHour, minute: endMinute);
      }
      
      // 加载通知优先级设置
      final prioritiesJson = prefs.getString('notification_priorities');
      if (prioritiesJson != null) {
        final Map<String, dynamic> priorities = jsonDecode(prioritiesJson);
        priorities.forEach((key, value) {
          _notificationPriorities[key] = _parseNotificationPriority(value);
        });
      }
      
      _logger.info('已加载通知设置');
    } catch (e) {
      _logger.severe('加载通知设置失败: $e');
    }
  }
  
  // 加载通知设置
  Future<void> _loadNotificationSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _notificationsEnabled = prefs.getBool('chat_notifications_enabled') ?? true;
      _onlyMentionNotifications = prefs.getBool('only_mention_notifications') ?? false;
      _notificationSoundEnabled = prefs.getBool('notification_sound_enabled') ?? true;
    } catch (e) {
      _logger.severe('加载通知设置失败: $e');
    }
  }
  
  // 保存通知设置
  Future<void> _saveNotificationSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('chat_notifications_enabled', _notificationsEnabled);
      await prefs.setBool('only_mention_notifications', _onlyMentionNotifications);
      await prefs.setBool('notification_sound_enabled', _notificationSoundEnabled);
      await prefs.setBool('merge_notifications', _mergeNotifications);
      await prefs.setBool('show_avatar_in_notification', _showAvatarInNotification);
      await prefs.setInt('notification_expiry_days', _notificationExpiryDays);
      await prefs.setBool('do_not_disturb_enabled', _doNotDisturbEnabled);
      
      // 保存免打扰时段
      if (_doNotDisturbStart != null) {
        await prefs.setInt('do_not_disturb_start_hour', _doNotDisturbStart!.hour);
        await prefs.setInt('do_not_disturb_start_minute', _doNotDisturbStart!.minute);
      }
      
      if (_doNotDisturbEnd != null) {
        await prefs.setInt('do_not_disturb_end_hour', _doNotDisturbEnd!.hour);
        await prefs.setInt('do_not_disturb_end_minute', _doNotDisturbEnd!.minute);
      }
      
      // 保存通知优先级设置
      final Map<String, String> prioritiesMap = {};
      _notificationPriorities.forEach((key, value) {
        prioritiesMap[key] = value.toString().split('.').last;
      });
      await prefs.setString('notification_priorities', jsonEncode(prioritiesMap));
      
      _logger.info('已保存通知设置');
    } catch (e) {
      _logger.severe('保存通知设置失败: $e');
    }
  }
  
  // 从数据库加载未读消息
  Future<void> _loadUnreadMessagesFromDatabase() async {
    if (_databaseService == null) {
      _logger.warning('数据库服务未初始化，无法加载未读消息');
      return;
    }
    
    try {
      // 先清理过期消息
      await _cleanupOldMessages();
      
      // 加载未读消息
      final messages = await _databaseService!.getUnreadMessages(limit: 200);
      
      // 更新未读消息计数
      _updateUnreadCounters(messages);
      
      _logger.info('从数据库加载了 ${messages.length} 条未读消息');
      _updateUnreadSummary();
    } catch (e) {
      _logger.warning('从数据库加载未读消息失败: $e');
    }
  }
  
  // 清理过期消息
  Future<void> _cleanupOldMessages() async {
    if (_databaseService == null) {
      _logger.warning('数据库服务未初始化，无法清理过期消息');
      return;
    }
    
    try {
      await _databaseService!.cleanupOldMessages(daysToKeep: _notificationExpiryDays);
      _logger.info('清理了过期消息');
    } catch (e) {
      _logger.warning('清理过期消息失败: $e');
    }
  }
  
  // 更新未读消息计数器
  void _updateUnreadCounters(List<ChatMessageData> messages) {
    // 清空计数器
    _channelUnreadCount.clear();
    _importantUnreadCount.clear();
    
    // 按频道统计未读消息
    for (var message in messages) {
      final channelName = message.channelName ?? 'default';
      _channelUnreadCount[channelName] = (_channelUnreadCount[channelName] ?? 0) + 1;
      
      // 统计重要消息（回复、提及、私聊、管理员消息）
      if (_isImportantMessage(message)) {
        _importantUnreadCount[channelName] = (_importantUnreadCount[channelName] ?? 0) + 1;
      }
    }
  }
  
  // 判断是否是重要消息
  bool _isImportantMessage(ChatMessageData message) {
    if (_currentUserId == null) return false;
    
    // 私聊消息
    if (message.isPrivate && message.toUserId == _currentUserId) return true;
    
    // 提及用户的消息
    if (message.content.contains('@$_currentUserId')) return true;
    
    // 管理员消息
    if (message.isAdmin) return true;
    
    // 系统消息
    if (message.isSystem) return true;
    
    return false;
  }
  
  // 更新未读消息摘要
  void _updateUnreadSummary() {
    final totalUnread = _channelUnreadCount.values.fold(0, (sum, count) => sum + count);
    final totalImportant = _importantUnreadCount.values.fold(0, (sum, count) => sum + count);
    
    final summary = UnreadMessageSummary(
      totalUnreadCount: totalUnread,
      importantUnreadCount: totalImportant,
      channelUnreadCounts: Map.from(_channelUnreadCount),
      channelImportantCounts: Map.from(_importantUnreadCount),
      lastUpdated: DateTime.now(),
    );
    
    _unreadSummaryController.add(summary);
  }
  
  // 处理新消息
  Future<void> processNewMessage(ChatMessageData message) async {
    // 如果是当前用户发送的消息，不处理
    if (message.userId == _currentUserId) return;
    
    // 判断消息是否与用户相关
    final isRelevant = _notificationService.isMessageRelevantToUser(message);
    
    // 根据设置决定是否处理
    if (!isRelevant && _onlyMentionNotifications) return;
    
    // 判断是否在免打扰时段
    if (_isInDoNotDisturbPeriod() && !_isImportantMessage(message)) return;
    
    // 添加到未读消息
    final channelName = message.channelName ?? 'default';
    _channelUnreadCount[channelName] = (_channelUnreadCount[channelName] ?? 0) + 1;
    
    if (_isImportantMessage(message)) {
      _importantUnreadCount[channelName] = (_importantUnreadCount[channelName] ?? 0) + 1;
    }
    
    // 保存到数据库
    if (_databaseService != null) {
      await _databaseService!.saveMessage(message, channelName);
      await _databaseService!.updateMessageStatus(message.id, read: false, notificationShown: false);
    }
    
    // 更新未读摘要
    _updateUnreadSummary();
    
    // 添加到通知服务
    _notificationService.addMessage(message);
  }
  
  // 判断当前时间是否在免打扰时段
  bool _isInDoNotDisturbPeriod() {
    if (!_doNotDisturbEnabled || _doNotDisturbStart == null || _doNotDisturbEnd == null) {
      return false;
    }
    
    final now = TimeOfDay.now();
    final nowMinutes = now.hour * 60 + now.minute;
    final startMinutes = _doNotDisturbStart!.hour * 60 + _doNotDisturbStart!.minute;
    final endMinutes = _doNotDisturbEnd!.hour * 60 + _doNotDisturbEnd!.minute;
    
    if (startMinutes <= endMinutes) {
      // 免打扰时段在同一天内
      return nowMinutes >= startMinutes && nowMinutes <= endMinutes;
    } else {
      // 免打扰时段跨天（例如晚上10点到早上7点）
      return nowMinutes >= startMinutes || nowMinutes <= endMinutes;
    }
  }
  
  // 获取消息的通知优先级
  NotificationPriority getMessagePriority(ChatMessageData message) {
    if (_currentUserId == null) return NotificationPriority.low;
    
    // 私聊消息
    if (message.isPrivate && message.toUserId == _currentUserId) {
      return _notificationPriorities['private'] ?? NotificationPriority.high;
    }
    
    // 提及用户的消息
    if (message.content.contains('@$_currentUserId')) {
      return _notificationPriorities['mention'] ?? NotificationPriority.high;
    }
    
    // 管理员消息
    if (message.isAdmin) {
      return _notificationPriorities['admin'] ?? NotificationPriority.medium;
    }
    
    // 系统消息
    if (message.isSystem) {
      return _notificationPriorities['system'] ?? NotificationPriority.medium;
    }
    
    // 普通消息
    return _notificationPriorities['normal'] ?? NotificationPriority.low;
  }
  
  // 标记频道所有消息为已读
  Future<void> markChannelAsRead(String channelName) async {
    if (_databaseService == null) {
      _logger.warning('数据库服务未初始化，无法标记频道消息为已读');
      return;
    }
    
    try {
      await _databaseService!.markAllMessagesAsRead(channelName);
      
      // 更新未读计数
      _channelUnreadCount[channelName] = 0;
      _importantUnreadCount[channelName] = 0;
      
      // 记录最后阅读时间
      _lastReadTime[channelName] = DateTime.now();
      
      // 更新未读摘要
      _updateUnreadSummary();
      
      _logger.info('已将频道 $channelName 的所有消息标记为已读');
    } catch (e) {
      _logger.warning('标记频道消息为已读失败: $e');
    }
  }
  
  // 标记所有消息为已读
  Future<void> markAllAsRead() async {
    if (_databaseService == null) {
      _logger.warning('数据库服务未初始化，无法标记所有消息为已读');
      return;
    }
    
    try {
      // 获取所有频道
      final channels = _channelUnreadCount.keys.toList();
      
      // 添加默认频道
      if (!channels.contains('default')) {
        channels.add('default');
      }
      
      // 标记所有频道的消息为已读
      for (var channel in channels) {
        await _databaseService!.markAllMessagesAsRead(channel);
      }
      
      // 清空未读计数
      _channelUnreadCount.clear();
      _importantUnreadCount.clear();
      
      // 记录最后阅读时间
      final now = DateTime.now();
      for (var channel in channels) {
        _lastReadTime[channel] = now;
      }
      
      // 更新未读摘要
      _updateUnreadSummary();
      
      // 清除通知服务中的未读消息
      _notificationService.clearUnreadMessages();
      
      _logger.info('已将所有消息标记为已读');
    } catch (e) {
      _logger.warning('标记所有消息为已读失败: $e');
    }
  }
  
  // 设置通知是否启用
  Future<void> setNotificationsEnabled(bool enabled) async {
    _notificationsEnabled = enabled;
    _notificationService.notificationsEnabled = enabled;
    await _saveNotificationSettings();
  }
  
  // 设置是否只显示提及消息
  Future<void> setOnlyMentionNotifications(bool enabled) async {
    _onlyMentionNotifications = enabled;
    _notificationService.onlyMentionNotifications = enabled;
    await _saveNotificationSettings();
  }
  
  // 设置通知声音是否启用
  Future<void> setNotificationSoundEnabled(bool enabled) async {
    _notificationSoundEnabled = enabled;
    _notificationService.notificationSoundEnabled = enabled;
    await _saveNotificationSettings();
  }
  
  // 设置是否合并通知
  Future<void> setMergeNotifications(bool enabled) async {
    _mergeNotifications = enabled;
    await _saveNotificationSettings();
  }
  
  // 设置是否在通知中显示头像
  Future<void> setShowAvatarInNotification(bool enabled) async {
    _showAvatarInNotification = enabled;
    await _saveNotificationSettings();
  }
  
  // 设置通知过期天数
  Future<void> setNotificationExpiryDays(int days) async {
    _notificationExpiryDays = days;
    await _saveNotificationSettings();
    await _cleanupOldMessages();
  }
  
  // 设置免打扰时段
  Future<void> setDoNotDisturbPeriod(bool enabled, TimeOfDay? start, TimeOfDay? end) async {
    _doNotDisturbEnabled = enabled;
    _doNotDisturbStart = start;
    _doNotDisturbEnd = end;
    await _saveNotificationSettings();
  }
  
  // 设置消息类型的通知优先级
  Future<void> setNotificationPriority(String messageType, NotificationPriority priority) async {
    _notificationPriorities[messageType] = priority;
    await _saveNotificationSettings();
  }
  
  // 解析通知优先级
  NotificationPriority _parseNotificationPriority(String value) {
    switch (value.toLowerCase()) {
      case 'high':
        return NotificationPriority.high;
      case 'medium':
        return NotificationPriority.medium;
      case 'low':
        return NotificationPriority.low;
      default:
        return NotificationPriority.medium;
    }
  }
  
  // 释放资源
  void dispose() {
    _notificationService.removeListener(_onNotificationServiceChanged);
    _unreadSummaryController.close();
  }
}
