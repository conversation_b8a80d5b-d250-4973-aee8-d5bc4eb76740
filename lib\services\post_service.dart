import 'package:estockcafe/config/config.dart';
import 'package:estockcafe/models/post.dart';
import 'package:estockcafe/models/comment.dart';
import 'package:estockcafe/services/token_service.dart';
import 'package:estockcafe/utils/logging.dart';
import 'package:logging/logging.dart';
import 'package:estockcafe/utils/functions.dart';

class PostService {
  final String baseUrl = AppConfig.baseUrl;
  final Logger _logger = Logging.getLogger('PostService');

  final TokenService _tokenService = TokenService();

  Future<CategoryItem> fetchArticlesByCategory(String categoryId,
      {int page = 1}) async {
    try {
      final url = '$baseUrl/category/$categoryId/$page';

      final jsonRes = await jsonRequest(url);

      if (jsonRes['code'] == 0 && jsonRes['data'] != null) {
        try {
          final categoryItem = CategoryItem.fromJson(jsonRes['data']);
          return categoryItem;
        } catch (e, stackTrace) {
          _logger.severe('创建 CategoryItem 对象时出错: $e\n$stackTrace');
          throw FormatException(
              'Failed to parse category data: $e', jsonRes['data'].toString());
        }
      } else {
        _logger
            .severe('Failed to load articles by category: ${jsonRes['msg']}');
        throw Exception(
            'Failed to load articles by category: ${jsonRes['msg']}');
      }
    } catch (e, stackTrace) {
      _logger.severe('Error fetching articles by category: $e\n$stackTrace');
      rethrow;
    }
  }

  Future<PostItem> fetchPost(String articleId) async {
    String? token = await _tokenService.getAccessToken();
    final headers = {
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
    try {
      final jsonRes = await jsonRequest(
        '$baseUrl/post/$articleId',
        headers: headers,
      );

      if (jsonRes['code'] == 0 && jsonRes['data'] != null) {
        return PostItem.fromJson(jsonRes['data']);
      } else {
        _logger.severe('Failed to load article: ${jsonRes['msg']}');
        throw Exception('Failed to load article: ${jsonRes['msg']}');
      }
    } catch (e) {
      _logger.severe('Error fetching article: $e');
      rethrow;
    }
  }

  Future<List<Comment>> fetchComments(String articleId,
      {int commentPage = 1}) async {
    try {
      final url = '$baseUrl/comments/$articleId/$commentPage';
      final jsonRes = await jsonRequest(url);
      if (jsonRes['code'] == 0 && jsonRes['data'] != null) {
        if (jsonRes['data'] is! List) {
          _logger.warning(
              'Unexpected data format for comments: ${jsonRes['data']}');
          return [];
        }
        return (jsonRes['data'] as List)
            .map((comment) => Comment.fromJson(comment))
            .toList();
      } else {
        _logger.severe('Failed to load comments: ${jsonRes['msg']}');
        throw Exception('Failed to load comments: ${jsonRes['msg']}');
      }
    } catch (e) {
      _logger.severe('Error fetching comments: $e');
      rethrow;
    }
  }

  Future<List<PostItem>> fetchRelatedPosts(String articleId) async {
    try {
      final jsonRes = await jsonRequest('$baseUrl/post/$articleId/related');

      if (jsonRes['code'] == 0 && jsonRes['data'] != null) {
        if (jsonRes['data'] is! List) {
          _logger.warning(
              'Unexpected data format for related posts: ${jsonRes['data']}');
          return [];
        }
        return (jsonRes['data'] as List)
            .map((post) => PostItem.fromJson(post))
            .toList();
      } else {
        _logger.severe('Failed to load related posts: ${jsonRes['msg']}');
        throw Exception('Failed to load related posts: ${jsonRes['msg']}');
      }
    } catch (e) {
      _logger.severe('Error fetching related posts: $e');
      rethrow;
    }
  }

  Future<bool> canUserAccessContent(
      int postId, int userType, int memberDown) async {
    // TODO: 实现检查用户是否可以访问内容的逻辑
    // 1. 检查用户类型是否满足要求
    // 2. 检查用户是否已购买内容
    // 3. 检查内容是否对该用户类型免费
    // 4. 返回用户是否可以访问内容的结果
    return false;
  }

  Future<bool> hasUserPurchased(int postId) async {
    // TODO: 实现检查用户是否已购买内容的逻辑
    // 1. 向服务器发送请求，检查用户的购买记录
    // 2. 解析服务器响应
    // 3. 返回用户是否已购买的结果
    return false;
  }

  Future<void> purchaseContent(int postId,
      {required Map<String, dynamic> purchaseInfo}) async {
    // TODO: 实现购买内容的逻辑
    // 1. 验证购买信息
    // 2. 向服务器发送购买请求
    // 3. 处理服务器响应
    // 4. 更新本地购买状态
    _logger.info(
        'Attempting to purchase content: $postId with info: $purchaseInfo');
  }

  Future<String> getDownloadUrl(int postId) async {
    // TODO: 实现获取下载 URL 的逻辑
    try {
      // 1. 向服务器请求下载 URL
      // 2. 验证响应
      // 3. 返回下载 URL
      return '';
    } catch (e) {
      _logger.severe('Error getting download URL for post $postId: $e');
      rethrow;
    }
  }

  Future<String> getViewUrl(int postId) async {
    // TODO: 实现获取查看 URL 的逻辑
    try {
      // 1. 向服务器请求查看 URL
      // 2. 验证响应
      // 3. 返回查看 URL
      return '';
    } catch (e) {
      _logger.severe('Error getting view URL for post $postId: $e');
      rethrow;
    }
  }

  // 搜索文章
  Future<List<PostItem>> searchPosts(String keyword, {int page = 1}) async {
    try {
      final url = '$baseUrl/search?keyword=${Uri.encodeComponent(keyword)}&page=$page';
      _logger.info('搜索文章: $url');
      
      final jsonRes = await jsonRequest(url);
      
      if (jsonRes['code'] == 0 && jsonRes['data'] != null) {
        if (jsonRes['data'] is! List) {
          _logger.warning('搜索结果格式不符合预期: ${jsonRes['data']}');
          return [];
        }
        
        return (jsonRes['data'] as List)
            .map((post) => PostItem.fromJson(post))
            .toList();
      } else {
        _logger.severe('搜索文章失败: ${jsonRes['msg']}');
        throw Exception('搜索文章失败: ${jsonRes['msg']}');
      }
    } catch (e, stackTrace) {
      _logger.severe('搜索文章时出错: $e\n$stackTrace');
      rethrow;
    }
  }
}
