import 'dart:convert';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../services/system_notification_service.dart';
import '../services/global_chat_service.dart';
import '../services/navigation_service.dart';
import '../services/app_state_manager.dart';
import '../utils/app_state.dart';
import '../utils/web_socket.dart';
import '../main.dart' show globalFcmToken;

class FCMService {
  static final FCMService _instance = FCMService._internal();
  factory FCMService() => _instance;

  final AppStateManager _stateManager = AppStateManager();
  final NavigationService _navigationService = NavigationService();
  final Logger _logger = Logger('FCMService');
  final SystemNotificationService _systemNotificationService =
      SystemNotificationService();

  // 消息ID集合，用于去重
  final Set<String> _processedMessageIds = {};

  // 本地通知插件
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // FCM可用性标志
  bool _isFcmAvailable = false;
  bool get isFcmAvailable => _isFcmAvailable;

  FCMService._internal();

  // 初始化标志
  bool _initialized = false;

  // 检查FCM是否可用
  Future<bool> checkFcmAvailability() async {
    try {
      _logger.info('检查FCM可用性...');

      // 尝试获取FCM令牌，最多尝试2次
      String? token;
      int retryCount = 0;
      const maxRetries = 2;

      while (retryCount < maxRetries && (token == null || token.isEmpty)) {
        try {
          token = await FirebaseMessaging.instance.getToken();
          if (token != null && token.isNotEmpty) {
            break;
          }
        } catch (e) {
          _logger.warning('FCM Token获取尝试${retryCount + 1}/${maxRetries}失败: $e');
        }

        if (retryCount < maxRetries - 1) {
          await Future.delayed(const Duration(seconds: 1));
        }
        retryCount++;
      }

      _isFcmAvailable = token != null && token.isNotEmpty;

      if (_isFcmAvailable) {
        _logger.info('FCM可用，令牌: ${token!.substring(0, 10)}...');
        globalFcmToken = token;
      } else {
        _logger.warning('FCM不可用，无法获取令牌');
      }

      return _isFcmAvailable;
    } catch (e) {
      _logger.warning('FCM可用性检查失败: $e');
      _isFcmAvailable = false;
      return false;
    }
  }

  Future<void> initialize() async {
    if (_initialized) {
      _logger.info('FCM服务已初始化，跳过重复初始化');
      return;
    }

    try {
      _logger.info('开始初始化FCM服务');

      // 首先检查FCM可用性
      await checkFcmAvailability();

      // 如果FCM可用，才执行FCM相关的初始化
      if (_isFcmAvailable) {
        // 请求通知权限
        if (Platform.isIOS || Platform.isAndroid) {
          final settings = await FirebaseMessaging.instance.requestPermission(
            alert: true,
            badge: true,
            sound: true,
          );

          if (settings.authorizationStatus == AuthorizationStatus.authorized) {
            _logger.info('FCM通知权限已授予');
          } else {
            _logger.warning('FCM通知权限被拒绝或受限');
          }
        }

        // 设置消息处理器
        // 1. 前台消息处理
        FirebaseMessaging.onMessage.listen((RemoteMessage message) {
          _logger.info('收到前台FCM消息');
          _handleFCMMessage(message);
        });

        // 2. 当应用在后台但未关闭时，消息被点击的处理
        FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
          _logger.info('用户点击后台FCM通知，应用打开');
          _handleFCMMessage(message);

          // 导航到聊天室
          _handleNotificationTap(message.data);
        });

        // 3. 检查应用打开时可能由FCM通知打开的情况
        final initialMessage =
            await FirebaseMessaging.instance.getInitialMessage();
        if (initialMessage != null) {
          _logger.info('应用由FCM通知打开');
          Future.delayed(const Duration(seconds: 1), () {
            _handleFCMMessage(initialMessage);
            _handleNotificationTap(initialMessage.data);
          });
        }

        // 订阅系统通知主题
        await FirebaseMessaging.instance
            .subscribeToTopic('system_announcements');
      } else {
        _logger.info('FCM不可用，跳过FCM通知设置，但将继续初始化本地通知');
      }

      // 无论FCM是否可用，都初始化本地通知功能
      await _initLocalNotifications();

      _initialized = true;
      _logger.info('FCM服务初始化完成，FCM可用性: $_isFcmAvailable');
    } catch (e) {
      _logger.severe('初始化FCM服务失败: $e');
      // 确保即使初始化失败，也标记为已初始化
      // 这样不会在每次调用时重复尝试初始化
      _initialized = true;
    }
  }

  // 处理通知点击
  void _handleNotificationTap(Map<String, dynamic> data) {
    try {
      final messageId = data['msg_id'] ?? data['message_id'] ?? '';
      final channelName = data['channel_name'] ?? '';

      if (messageId.isNotEmpty) {
        _logger.info('处理通知点击: 消息ID=$messageId, 频道=$channelName');

        // 导航到相应的聊天室
        if (_navigationService.navigatorKey.currentState != null) {
          _navigationService.navigatorKey.currentState!.pushNamedAndRemoveUntil(
            '/chat/check',
            (route) => false,
            arguments: {'messageId': messageId, 'channelName': channelName},
          );
        } else {
          _logger.warning('无法导航到聊天室: Navigator不可用');
        }
      }
    } catch (e) {
      _logger.warning('处理通知点击失败: $e');
    }
  }

  // 初始化本地通知
  Future<void> _initLocalNotifications() async {
    try {
      _logger.info('初始化本地通知插件');

      // 初始化设置
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      const InitializationSettings initializationSettings =
          InitializationSettings(android: initializationSettingsAndroid);

      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: (NotificationResponse details) {
          _logger.info('点击通知: ${details.payload}');
          if (details.payload != null) {
            try {
              final payloadData = json.decode(details.payload!);
              final channelName = payloadData['channel_name'];
              if (channelName != null) {
                _navigateToChannel(channelName);
              }
            } catch (e) {
              _logger.warning('解析通知负载失败: $e');
            }
          }
        },
      );

      // 创建Android通知渠道
      await _createNotificationChannels();

      _logger.info('本地通知插件初始化完成');
    } catch (e) {
      _logger.severe('初始化本地通知插件失败: $e');
    }
  }

  // 创建通知渠道
  Future<void> _createNotificationChannels() async {
    if (defaultTargetPlatform != TargetPlatform.android) return;

    _logger.info('创建Android通知渠道');

    // 创建默认通知渠道
    const AndroidNotificationChannel chatChannel = AndroidNotificationChannel(
      'chat_channel', // id
      '聊天消息', // name
      importance: Importance.high,
      description: '聊天室和私聊消息通知',
    );

    // 创建重要消息通知渠道
    const AndroidNotificationChannel importantChannel =
        AndroidNotificationChannel(
      'important_messages', // id - 与服务器发送的android_channel_id匹配
      '重要消息', // name
      importance: Importance.high,
      description: '重要系统消息和提醒',
      sound: RawResourceAndroidNotificationSound('notification_sound'),
      enableVibration: true,
    );

    // 注册通知渠道
    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(chatChannel);

    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(importantChannel);

    _logger.info('Android通知渠道创建完成');
  }

  // 设置前台消息处理
  Future<void> _setupForegroundMessaging() async {
    // 注册前台消息处理器
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // 设置iOS前台显示通知
    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
  }

  // 处理前台消息
  void _handleForegroundMessage(RemoteMessage message) {
    _logger.info('收到前台消息: ${message.messageId}');

    // 检查消息ID是否已处理过（防止重复）
    final messageId = message.messageId;
    if (messageId != null && _processedMessageIds.contains(messageId)) {
      _logger.info('消息 $messageId 已处理，跳过');
      return;
    }

    if (messageId != null) {
      _processedMessageIds.add(messageId);
      // 保持集合大小适中，避免内存泄漏
      if (_processedMessageIds.length > 100) {
        _processedMessageIds.remove(_processedMessageIds.first);
      }
    }

    final data = message.data;
    final messageType = data['type'] ?? '';
    final channelName = data['channel_name'];

    _logger.info('消息类型: $messageType, 频道: $channelName, 数据: $data');

    // 无论用户是否在查看对应聊天室，都将消息广播到聊天界面
    // 这样确保消息始终能显示在聊天界面上
    _broadcastMessageToChat(message);

    // 应用状态检查
    final currentState = _stateManager.currentState;
    final currentChannel = _stateManager.currentChannelName;

    _logger.info(
        '当前应用状态: ${currentState.toString()}, 当前频道: $currentChannel, 消息频道: $channelName');

    // 增强检查逻辑：如果应用状态为CHATROOM_ACTIVE，不显示任何通知
    if (currentState == AppState.CHATROOM_ACTIVE) {
      _logger.info('用户正在聊天室界面，消息已广播到聊天界面，不显示通知');
      return;
    }

    // 处理静默推送消息的特殊逻辑
    if (messageType == 'silent') {
      _logger.info('收到静默推送消息，处理未读消息更新');

      // 如果是静默推送，我们仍然需要更新未读计数
      _updateUnreadCount(channelName);

      // 提取消息内容
      final senderName = data['sender_name'] ?? '新消息';
      final senderId = data['sender_id'] ?? '';
      final content = data['content'] ?? data['message'] ?? '';

      // 对于静默推送，我们需要手动创建一个通知
      _showLocalNotification(
        RemoteMessage(
          notification: RemoteNotification(title: senderName, body: content),
          data: {
            'type': 'chat',
            'channel_name': channelName,
            'message_id': data['message_id'],
            'sender_id': senderId,
            'sender_name': senderName,
            'content': content,
            'android_channel_id': 'chat_channel',
          },
          messageId: messageId ?? data['message_id'],
        ),
      );

      _logger.info('静默推送消息处理完成');
      return;
    }

    // 更新频道的未读消息计数
    _updateUnreadCount(channelName);

    // 显示通知
    _showLocalNotification(message);

    _logger.info('前台消息处理完成');
  }

  // 将消息广播到聊天界面
  void _broadcastMessageToChat(RemoteMessage message) {
    try {
      _logger.info('开始将消息广播到聊天界面');

      final data = message.data;
      final messageType = data['type'] ?? '';
      final channelName = data['channel_name'];

      // 处理messageId可能是不同字段名或不同类型的情况
      String? messageId;
      if (data['message_id'] != null) {
        messageId = data['message_id'].toString();
      } else if (message.messageId != null) {
        messageId = message.messageId;
      }

      final content = data['content'] ?? message.notification?.body ?? '';

      // 如果消息内容为空，则跳过处理
      if (content.trim().isEmpty) {
        _logger.warning('消息内容为空，跳过广播到聊天界面');
        return;
      }

      // 导入需要的服务
      final globalChatService = GlobalChatService();

      // 检查是否为有效的聊天消息
      if (channelName != null && messageId != null) {
        _logger.info(
            '处理聊天消息: ID=$messageId, 频道=$channelName, 类型=$messageType, 内容=$content');

        // 处理user_id可能是int类型的情况
        String userId;
        if (data['sender_id'] != null) {
          userId = data['sender_id'].toString();
        } else if (data['user_id'] != null) {
          userId = data['user_id'].toString();
        } else {
          userId = 'unknown';
        }

        // 处理user_name可能是不同字段名的情况
        String userName;
        if (data['sender_name'] != null) {
          userName = data['sender_name'].toString();
        } else if (message.notification?.title != null) {
          userName = message.notification!.title!;
        } else {
          userName = '未知用户';
        }

        // 处理timestamp可能是不同类型的情况
        String timestamp;
        if (data['timestamp'] != null) {
          timestamp = data['timestamp'].toString();
        } else {
          timestamp =
              (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString();
        }

        // 处理is_admin可能是int类型的情况
        bool isAdmin = false;
        if (data['is_admin'] != null) {
          isAdmin = data['is_admin'] == 'true' ||
              data['is_admin'] == true ||
              data['is_admin'] == 1;
        } else if (data['admin'] != null) {
          isAdmin = data['admin'] == 'true' ||
              data['admin'] == true ||
              data['admin'] == 1;
        }

        // 将FCM消息数据转换为聊天服务可以处理的格式
        final chatMessageData = {
          'type': messageType,
          'channel_name': channelName,
          'message_id': messageId,
          'msg_id': messageId,
          'content': content,
          'user_id': userId,
          'user_name': userName,
          'timestamp': timestamp,
          'create_time': data['create_time'] ?? timestamp,
          'is_admin': isAdmin,
        };

        // 记录完整的消息数据用于调试
        _logger.info('准备发送到GlobalChatService的消息数据: $chatMessageData');

        // 使用GlobalChatService处理消息
        globalChatService.processFcmMessage(chatMessageData);

        _logger.info('消息已成功广播到聊天界面');
      } else {
        _logger.warning('无法广播消息：缺少频道名称或消息ID');
      }
    } catch (e, stackTrace) {
      _logger.severe('广播消息到聊天界面失败: $e');
      _logger.severe('错误堆栈: $stackTrace');
    }
  }

  // 显示本地通知
  Future<void> _showLocalNotification(RemoteMessage message) async {
    try {
      // 获取通知标题和内容
      final notification = message.notification;
      final title = notification?.title ?? '新消息';
      String body = notification?.body ?? '';
      final data = message.data;

      // 确保消息内容不为空
      if (body.trim().isEmpty) {
        body = data['content'] ?? data['message'] ?? '新消息';
      }

      // 再次确保消息内容不为空
      if (body.trim().isEmpty) {
        _logger.warning('消息内容为空，使用默认内容');
        body = '收到一条新消息';
      }

      _logger.info('准备显示本地通知: 标题=$title, 内容=$body');
      _logger.info('通知数据: $data');

      // 获取通知渠道ID
      final androidChannelId = data['android_channel_id'] ?? 'chat_channel';
      _logger.info('使用通知渠道: $androidChannelId');

      // 创建Android通知详情
      final androidDetails = AndroidNotificationDetails(
        androidChannelId,
        androidChannelId == 'important_messages' ? '重要消息' : '聊天消息',
        importance: Importance.high,
        priority: Priority.high,
        channelDescription: '应用通知',
        showWhen: true,
        // 确保通知声音正确播放
        sound: androidChannelId == 'important_messages'
            ? const RawResourceAndroidNotificationSound(
                'notification_sound',
              )
            : null,
        icon: '@mipmap/ic_launcher',
      );

      // 创建通知详情
      final notificationDetails = NotificationDetails(android: androidDetails);

      // 创建通知负载
      final payload = json.encode({
        'type': data['type'],
        'channel_name': data['channel_name'],
        'message_id': data['message_id'],
      });

      // 使用消息ID或内容的哈希值作为通知ID，确保相同消息不会创建多个通知
      final String messageId = data['message_id'] ?? message.messageId ?? '';
      final String messageContent = body;
      final int notificationId = messageId.isNotEmpty
          ? messageId.hashCode
          : (title + messageContent).hashCode;

      _logger.info('使用通知ID: $notificationId, 基于消息ID: $messageId');

      // 在显示新通知前，清除可能存在的重复通知
      await _flutterLocalNotificationsPlugin.cancel(notificationId);

      // 显示通知
      await _flutterLocalNotificationsPlugin.show(
        notificationId,
        title,
        body,
        notificationDetails,
        payload: payload,
      );

      _logger.info('已显示本地通知: $title - $body (ID: $notificationId)');
    } catch (e, stackTrace) {
      _logger.severe('显示本地通知失败: $e');
      _logger.severe('错误堆栈: $stackTrace');
    }
  }

  // 处理通知点击（应用在后台或关闭状态下）
  void _handleNotificationOpen(RemoteMessage message) {
    _logger.info('通过通知打开应用: ${message.messageId}');

    final data = message.data;
    final channelName = data['channel_name'];

    if (channelName != null) {
      _navigateToChannel(channelName);
    }
  }

  // 处理初始消息（从通知启动应用）
  void _handleInitialMessage(RemoteMessage message) {
    _logger.info('处理初始消息: ${message.messageId}');

    final data = message.data;
    final channelName = data['channel_name'];

    if (channelName != null) {
      // 延迟导航，确保应用已完全初始化
      Future.delayed(const Duration(milliseconds: 1000), () {
        _navigateToChannel(channelName);
      });
    }
  }

  // 导航到指定频道
  void _navigateToChannel(String channelName) {
    _logger.info('导航到频道: $channelName');
    _navigationService.navigateTo('/chatroom/$channelName');
  }

  // 更新未读计数
  void _updateUnreadCount(String? channelName) {
    if (channelName == null) return;

    _logger.info('更新频道未读消息计数: $channelName');

    // TODO: 实现未读消息计数更新逻辑
    // 这里可以调用一个全局的状态管理器来更新特定频道的未读消息计数
    // 例如: ChatManager.instance.incrementUnreadCount(channelName);
  }

  // 设置FCM通知渠道
  Future<void> _setFcmNotificationChannel() async {
    if (defaultTargetPlatform != TargetPlatform.android) return;

    try {
      // 设置FCM默认通知渠道
      await FirebaseMessaging.instance
          .setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );

      _logger.info('FCM前台通知显示选项已设置');
    } catch (e) {
      _logger.severe('设置FCM通知渠道失败: $e');
    }
  }

  // 显示本地通知
  Future<void> _showNotification(
    String message,
    String messageId,
    String channelName,
    String userId,
  ) async {
    try {
      // 构建通知数据
      final notificationData = {
        'messageId': messageId,
        'channel_name': channelName,
        'userId': userId,
        'type': 'chat',
      };

      // 调用SystemNotificationService的公共显示通知方法
      await _systemNotificationService.showNotification(
        title: '聊天消息',
        body: message,
        payload: notificationData,
        channelId: 'chat_channel',
      );

      _logger.info('FCM消息通知已显示: $message');
    } catch (e) {
      _logger.warning('显示FCM消息通知失败: $e');
    }
  }

  // 处理FCM消息
  void _handleFCMMessage(RemoteMessage message) {
    final logger = Logger('FCMService');
    logger.info('收到FCM消息: ${message.messageId}');

    // 提取消息数据
    final data = message.data;
    final notification = message.notification;
    final title = notification?.title;
    final body = notification?.body;
    final messageType = data['type'] ?? '';
    final messageId = data['msg_id'] ?? data['message_id'] ?? '';
    final channelName = data['channel_name'] ?? '';
    final senderName = data['user_name'] ?? data['sender_name'] ?? '新消息';
    final content = data['content'] ?? data['message'] ?? body ?? '';
    final userId = data['user_id']?.toString() ?? '';
    final createTime = data['create_time'] != null
        ? int.tryParse(data['create_time'].toString()) ?? 0
        : 0;

    logger.info('处理FCM消息: $data');

    // 检查是否是聊天消息
    if (messageType == 'msg' || messageType == 'chat') {
      // 获取当前应用状态
      final appStateManager = AppStateManager();
      final appState = appStateManager.currentState;
      final appChannelName = appStateManager.currentChannelName;

      logger
          .info('当前应用状态: $appState, 当前频道: $appChannelName, 消息频道: $channelName');

      // 显示本地通知
      if (messageId.isNotEmpty && content.isNotEmpty) {
        final message = '$senderName - $content';
        logger.info('显示本地通知: $message');
        try {
          _showNotification(message, messageId, channelName, userId);
        } catch (e) {
          logger.warning('显示本地通知失败: $e');
        }
      }

      // 无论应用是否在前台，都将消息保存到数据库
      // 这确保即使用户在后台，也能接收到消息并在回到前台时显示
      try {
        // 确保消息立即传递给GlobalChatService进行处理和保存
        final globalChatService = GlobalChatService();

        // 确保GlobalChatService已初始化
        if (!globalChatService.initialized) {
          logger.info('初始化GlobalChatService');
          globalChatService.initialize();
        }

        // 构建标准化的消息数据格式
        final processedData = _standardizeMessageData(data,
            title: title, body: body, messageId: message.messageId);

        // 使用公共方法处理消息
        globalChatService.processExternalMessage(processedData);
        logger.info('消息已传递给GlobalChatService处理: ${processedData['type']}');
      } catch (e, stackTrace) {
        logger.severe('处理FCM消息失败: $e');
        logger.severe('错误堆栈: $stackTrace');
      }
    } else if (messageType == 'system' || messageType == 'notice') {
      // 处理系统消息...
      logger.info('收到系统消息: $title - $body');
      // 显示系统通知
      try {
        _systemNotificationService.showNotification(
          title: title ?? '系统通知',
          body: body ?? '您有一条新的系统消息',
          payload: data,
          channelId: 'important_messages',
        );
      } catch (e) {
        logger.warning('显示系统通知失败: $e');
      }
    } else {
      // 处理其他类型的消息
      logger.info('收到其他类型的消息: $messageType');

      // 如果有通知内容，显示通知
      if (notification != null) {
        try {
          _systemNotificationService.showNotification(
            title: title ?? '新消息',
            body: body ?? '您有一条新消息',
            payload: data,
            channelId: 'important_messages',
          );
        } catch (e) {
          logger.warning('显示通知失败: $e');
        }
      } else {
        logger.info('静默消息，不显示通知');
      }
    }

    logger.info('FCM消息处理完成: ${message.messageId}');
  }

  // 标准化消息数据格式
  Map<String, dynamic> _standardizeMessageData(
    Map<String, dynamic> data, {
    String? title,
    String? body,
    String? messageId,
  }) {
    final logger = Logger('FCMService');
    logger.info('开始标准化消息数据');

    // 创建标准化的消息数据
    Map<String, dynamic> standardData = Map<String, dynamic>.from(data);

    // 确保消息ID存在
    if (!standardData.containsKey('message_id') &&
        !standardData.containsKey('msg_id')) {
      if (messageId != null) {
        standardData['message_id'] = messageId;
      } else {
        standardData['message_id'] =
            DateTime.now().millisecondsSinceEpoch.toString();
      }
    }

    // 确保msg_id和message_id字段都存在
    if (standardData.containsKey('message_id') &&
        !standardData.containsKey('msg_id')) {
      standardData['msg_id'] = standardData['message_id'];
    } else if (standardData.containsKey('msg_id') &&
        !standardData.containsKey('message_id')) {
      standardData['message_id'] = standardData['msg_id'];
    }

    // 确保content存在
    if (!standardData.containsKey('content')) {
      if (standardData.containsKey('message')) {
        standardData['content'] = standardData['message'];
      } else if (body != null) {
        standardData['content'] = body;
      }
    }

    // 确保user_name存在
    if (!standardData.containsKey('user_name')) {
      if (standardData.containsKey('sender_name')) {
        standardData['user_name'] = standardData['sender_name'];
      } else if (title != null) {
        standardData['user_name'] = title;
      } else {
        standardData['user_name'] = '系统消息';
      }
    }

    // 确保user_id存在
    if (!standardData.containsKey('user_id')) {
      if (standardData.containsKey('sender_id')) {
        standardData['user_id'] = standardData['sender_id'];
      } else {
        standardData['user_id'] = 'system';
      }
    }

    // 确保channelName存在
    if (!standardData.containsKey('channel_name') &&
        !standardData.containsKey('channelName')) {
      standardData['channel_name'] = 'general';
    } else if (standardData.containsKey('channelName') &&
        !standardData.containsKey('channel_name')) {
      standardData['channel_name'] = standardData['channelName'];
    }

    // 确保timestamp存在
    if (!standardData.containsKey('timestamp')) {
      standardData['timestamp'] =
          (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString();
    }

    // 确保create_time存在
    if (!standardData.containsKey('create_time')) {
      standardData['create_time'] = standardData['timestamp'];
    }

    // 确保type存在
    if (!standardData.containsKey('type')) {
      standardData['type'] = 'chat';
    }

    logger.info('消息数据标准化完成');
    return standardData;
  }
}
