import 'dart:async';
import 'dart:collection';
import 'package:logging/logging.dart';
import '../models/chat_message.dart';

/// 通知合并器 - 在短时间内收到多条消息时智能合并显示
class NotificationMerger {
  static final NotificationMerger _instance = NotificationMerger._internal();
  factory NotificationMerger() => _instance;
  
  NotificationMerger._internal() {
    _logger.level = Level.INFO;
    _processPendingMessages();
  }

  final Logger _logger = Logger('NotificationMerger');
  
  // 待处理的消息队列
  final Queue<ChatMessageData> _pendingMessages = Queue<ChatMessageData>();
  
  // 合并后的消息组
  final List<MergedNotification> _mergedNotifications = [];
  
  // 控制器
  final StreamController<List<MergedNotification>> _notificationsController = 
      StreamController<List<MergedNotification>>.broadcast();
  
  // 定时器
  Timer? _processingTimer;
  
  // 合并窗口时间（毫秒）
  int _mergeWindowMs = 3000;
  
  // 公开属性
  Stream<List<MergedNotification>> get notificationsStream => _notificationsController.stream;
  List<MergedNotification> get currentNotifications => List.unmodifiable(_mergedNotifications);
  int get mergeWindowMs => _mergeWindowMs;
  set mergeWindowMs(int value) {
    if (value >= 1000 && value <= 10000) {
      _mergeWindowMs = value;
    }
  }
  
  // 添加消息
  void addMessage(ChatMessageData message) {
    // 将消息添加到待处理队列
    _pendingMessages.add(message);
    
    // 如果定时器未运行，启动定时器
    if (_processingTimer == null || !_processingTimer!.isActive) {
      _startProcessingTimer();
    }
  }
  
  // 启动处理定时器
  void _startProcessingTimer() {
    // 取消现有定时器
    _processingTimer?.cancel();
    
    // 创建新定时器
    _processingTimer = Timer(Duration(milliseconds: _mergeWindowMs), () {
      _processPendingMessages();
    });
  }
  
  // 处理待处理消息
  void _processPendingMessages() {
    if (_pendingMessages.isEmpty) return;
    
    _logger.info('处理 ${_pendingMessages.length} 条待处理消息');
    
    // 按发送者分组
    final Map<String, List<ChatMessageData>> messagesByUser = {};
    final Map<String, List<ChatMessageData>> privateMessages = {};
    final List<ChatMessageData> systemMessages = [];
    
    while (_pendingMessages.isNotEmpty) {
      final message = _pendingMessages.removeFirst();
      
      if (message.isSystem) {
        // 系统消息单独处理
        systemMessages.add(message);
      } else if (message.isPrivate) {
        // 私聊消息按对话分组
        final key = '${message.userId}_${message.toUserId}';
        privateMessages[key] ??= [];
        privateMessages[key]!.add(message);
      } else {
        // 普通消息按发送者分组
        messagesByUser[message.userId] ??= [];
        messagesByUser[message.userId]!.add(message);
      }
    }
    
    // 创建合并通知
    final List<MergedNotification> newNotifications = [];
    
    // 处理系统消息
    if (systemMessages.isNotEmpty) {
      newNotifications.add(MergedNotification(
        id: 'system_${DateTime.now().millisecondsSinceEpoch}',
        messages: systemMessages,
        type: NotificationType.system,
        timestamp: DateTime.now(),
      ));
    }
    
    // 处理私聊消息
    privateMessages.forEach((key, messages) {
      newNotifications.add(MergedNotification(
        id: 'private_${key}_${DateTime.now().millisecondsSinceEpoch}',
        messages: messages,
        type: NotificationType.private,
        timestamp: DateTime.now(),
      ));
    });
    
    // 处理普通消息
    messagesByUser.forEach((userId, messages) {
      newNotifications.add(MergedNotification(
        id: 'user_${userId}_${DateTime.now().millisecondsSinceEpoch}',
        messages: messages,
        type: NotificationType.normal,
        timestamp: DateTime.now(),
      ));
    });
    
    // 添加到合并通知列表
    _mergedNotifications.addAll(newNotifications);
    
    // 限制通知数量，最多保留20条
    if (_mergedNotifications.length > 20) {
      _mergedNotifications.removeRange(0, _mergedNotifications.length - 20);
    }
    
    // 通知监听器
    _notificationsController.add(_mergedNotifications);
    
    _logger.info('创建了 ${newNotifications.length} 条合并通知，当前共有 ${_mergedNotifications.length} 条通知');
  }
  
  // 移除通知
  void removeNotification(String notificationId) {
    final index = _mergedNotifications.indexWhere((n) => n.id == notificationId);
    if (index >= 0) {
      _mergedNotifications.removeAt(index);
      _notificationsController.add(_mergedNotifications);
      _logger.info('移除了通知 $notificationId，当前共有 ${_mergedNotifications.length} 条通知');
    }
  }
  
  // 清空所有通知
  void clearAllNotifications() {
    _mergedNotifications.clear();
    _notificationsController.add(_mergedNotifications);
    _logger.info('清空了所有通知');
  }
  
  // 释放资源
  void dispose() {
    _processingTimer?.cancel();
    _notificationsController.close();
  }
}

/// 合并后的通知
class MergedNotification {
  final String id;
  final List<ChatMessageData> messages;
  final NotificationType type;
  final DateTime timestamp;
  
  MergedNotification({
    required this.id,
    required this.messages,
    required this.type,
    required this.timestamp,
  });
  
  // 获取主要消息（用于显示）
  ChatMessageData get primaryMessage => messages.first;
  
  // 获取发送者名称
  String get senderName {
    if (type == NotificationType.system) {
      return '系统消息';
    } else if (type == NotificationType.private) {
      return '私信: ${primaryMessage.userName}';
    } else {
      return primaryMessage.userName;
    }
  }
  
  // 获取消息数量
  int get messageCount => messages.length;
  
  // 获取消息预览
  String get preview {
    if (messageCount == 1) {
      return primaryMessage.isImage ? '[图片消息]' : primaryMessage.content;
    } else {
      return '${messageCount}条新消息';
    }
  }
  
  // 获取头像URL
  String? get avatarUrl => primaryMessage.userAvatar;
  
  // 是否包含图片
  bool get containsImage => messages.any((msg) => msg.isImage);
  
  // 是否包含@用户的消息
  bool containsMention(String userId) {
    return messages.any((msg) => msg.content.contains('@$userId'));
  }
}

/// 通知类型
enum NotificationType {
  normal,
  private,
  system,
}
