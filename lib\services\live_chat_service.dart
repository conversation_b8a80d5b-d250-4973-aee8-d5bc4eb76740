import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:logging/logging.dart';

import '../models/chat_message.dart';
import '../utils/web_socket.dart';
import '../config/config.dart';
import 'chat_database_service.dart';
import 'chat_notification_service.dart';
import 'app_state_manager.dart';

/// 直播聊天室服务
/// 独立于全局聊天室，专门处理直播间的聊天功能
class LiveChatService extends ChangeNotifier {
  final _logger = Logger('LiveChatService');

  // WebSocket连接
  WebSocketUtility? _webSocket;
  bool _isConnected = false;
  String? _channelName;
  String? _userId;
  String? _userName;
  Map<String, dynamic>? _chatroomInfo;
  bool _hasPermission = false;
  String? _lastError;

  // 消息相关
  final List<ChatMessageData> _messages = [];
  final StreamController<ChatMessageData> _messageController =
      StreamController<ChatMessageData>.broadcast();
  final StreamController<bool> _connectionController =
      StreamController<bool>.broadcast();

  // 数据库服务
  ChatDatabaseService? _chatDatabaseService;
  bool _isDatabaseAvailable = false;

  // 重连机制
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  static const Duration _reconnectDelay = Duration(seconds: 3);

  // 初始化标志
  bool _initialized = false;

  /// 安全地通知监听器，避免在widget树锁定时调用
  void _safeNotifyListeners() {
    // 使用 scheduleMicrotask 确保在下一个事件循环中执行
    // 这样可以避免在 widget 树被锁定时调用 notifyListeners
    scheduleMicrotask(() {
      try {
        notifyListeners();
      } catch (e) {
        _logger.warning('通知监听器时发生错误: $e');
      }
    });
  }

  // Getters
  bool get isConnected => _isConnected;
  String? get channelName => _channelName;
  String? get userId => _userId;
  String? get userName => _userName;
  String? get lastError => _lastError;
  List<ChatMessageData> get messages => List.unmodifiable(_messages);
  Stream<ChatMessageData> get messageStream => _messageController.stream;
  Stream<bool> get connectionStream => _connectionController.stream;
  bool get hasPermission => _hasPermission;

  @override
  void dispose() {
    _messageController.close();
    _connectionController.close();
    _reconnectTimer?.cancel();

    // 关闭数据库连接
    if (_chatDatabaseService != null) {
      _chatDatabaseService!.close();
    }

    // 断开WebSocket连接
    if (_webSocket != null) {
      _webSocket!.disconnect();
    }

    super.dispose();
  }

  /// 初始化直播聊天服务
  Future<void> initialize() async {
    if (_initialized) {
      _logger.info('直播聊天服务已经初始化，跳过重复初始化');
      return;
    }

    try {
      _initialized = true;
      _logger.info('直播聊天服务初始化成功');
    } catch (e) {
      _logger.severe('直播聊天服务初始化失败: $e');
    }
  }

  /// 更新用户信息
  Future<void> updateUserInfo({
    required String userId,
    required String userName,
    required bool hasPermission,
    Map<String, dynamic>? chatroomInfo,
  }) async {
    final userChanged = _userId != userId;
    final permissionChanged = _hasPermission != hasPermission;
    final chatroomChanged = _chatroomInfo != chatroomInfo;

    bool isLoggedIn = userId.isNotEmpty && userId != 'guest';

    _logger.info(
        '🔄 更新直播聊天用户信息: userId=$userId, hasPermission=$hasPermission, isLoggedIn=$isLoggedIn');

    _userId = userId;
    _userName = userName;
    _chatroomInfo = chatroomInfo;
    _hasPermission = hasPermission && isLoggedIn;

    _logger.info('📝 更新后的状态:');
    _logger.info('  - _userId: $_userId');
    _logger.info('  - _userName: $_userName');
    _logger.info('  - _hasPermission: $_hasPermission');
    _logger.info('  - _chatroomInfo: $_chatroomInfo');

    // 如果用户信息或权限发生变化，通知监听器
    if (userChanged || permissionChanged || chatroomChanged) {
      _safeNotifyListeners();
    }

    // 确保服务已初始化
    if (!_initialized) {
      await initialize();
    }
  }

  /// 连接到直播聊天室
  Future<void> connect({
    required String channelName,
    required String userId,
    required String userName,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? chatroomInfo,
  }) async {
    // 检查是否需要切换频道
    if (_isConnected && _channelName != channelName) {
      _logger.info('🔄 需要切换直播频道: 当前频道=${_channelName}, 目标频道=${channelName}');
      await disconnect();
      await Future.delayed(const Duration(milliseconds: 500));
    } else if (_isConnected && _channelName == channelName) {
      _logger.info('直播聊天服务已连接到相同频道，无需重新连接');
      return;
    }

    // 检查用户是否登录
    bool isLoggedIn = userId.isNotEmpty && userId != 'guest';
    _logger.info('🔐 登录状态检查: userId=$userId, isLoggedIn=$isLoggedIn');
    if (!isLoggedIn) {
      _logger.warning('❌ 用户未登录，无法连接直播聊天室');
      return;
    }

    // 检查用户权限
    _logger.info('🔑 权限检查: _hasPermission=$_hasPermission');
    if (!_hasPermission) {
      _logger.warning('❌ 用户无权限访问直播聊天室，拒绝连接');
      return;
    }

    try {
      _logger.info('🚀 开始连接直播聊天室: 频道=$channelName, 用户=$userId');
      _logger.info('🔒 这是独立的直播聊天服务，不会影响全局聊天室');
      _logger.info('🛡️ 防冲突措施:');
      _logger.info('   - 使用独立的WebSocket连接');
      _logger.info('   - 使用不同的URL前缀 (live-room- vs chatroom-)');
      _logger.info('   - 使用独立的消息处理流程');
      _logger.info('   - 使用独立的数据库存储');

      // 更新连接参数 - 直接使用原始频道名称
      _channelName = channelName;
      _userId = userId;
      _userName = userName;
      _chatroomInfo = chatroomInfo;

      // 初始化数据库服务
      await _initChatDatabaseService();

      // 使用与GlobalChatService相同的URL构建逻辑
      String baseUrl = AppConfig.chatWsUrl;
      String domain = baseUrl;
      if (domain.startsWith('https://')) {
        domain = domain.substring(8);
      } else if (domain.startsWith('http://')) {
        domain = domain.substring(7);
      } else if (domain.startsWith('wss://')) {
        domain = domain.substring(6);
      } else if (domain.startsWith('ws://')) {
        domain = domain.substring(5);
      }

      if (domain.endsWith('/')) {
        domain = domain.substring(0, domain.length - 1);
      }

      if (domain.contains('/')) {
        domain = domain.substring(0, domain.indexOf('/'));
      }

      final clientType = Platform.isAndroid ? 'android' : 'ios';
      final String wsUrl =
          'wss://$domain/v1/ws/$channelName/$userId/$clientType';

      _logger.info('🔗 直播聊天WebSocket URL: $wsUrl');
      _logger.info('🔗 使用频道名称: $channelName');
      _logger.info('🔗 注意：直播聊天室和全局聊天室使用不同的频道名称，不会冲突');

      // 创建WebSocket连接
      _webSocket = WebSocketUtility(
        url: wsUrl,
        headers: headers,
        onOpen: _onWebSocketOpen,
        onMessage: _onWebSocketMessage,
        onClose: _onWebSocketClose,
        onError: _onWebSocketError,
      );

      // 连接WebSocket
      _logger.info('🔌 开始WebSocket连接...');
      await _webSocket!.connect(
        channelName: channelName, // 使用原始频道名称
        userId: userId,
        token: chatroomInfo?['token']?.toString(),
      );

      _logger.info('✅ 直播聊天室连接成功');
    } catch (e) {
      _logger.severe('连接直播聊天室失败: $e');
      _lastError = e.toString();
      rethrow;
    }
  }

  /// 断开连接
  Future<void> disconnect() async {
    _logger.info('断开直播聊天室连接');

    _reconnectTimer?.cancel();
    _reconnectTimer = null;

    if (_webSocket != null) {
      _webSocket!.disconnect();
      _webSocket = null;
    }

    _isConnected = false;
    _connectionController.add(false);
    _safeNotifyListeners();
  }

  /// 初始化数据库服务
  Future<void> _initChatDatabaseService() async {
    if (_userId == null || _userId!.isEmpty || _userId == 'guest') {
      _logger.info('用户未登录，跳过数据库初始化');
      return;
    }

    try {
      _chatDatabaseService = ChatDatabaseService(
        userId: _userId!,
        channelName: _channelName ?? 'live-default',
      );
      _isDatabaseAvailable = true;
      _logger.info('直播聊天数据库服务初始化成功，频道: ${_channelName}');
    } catch (e) {
      _logger.warning('直播聊天数据库服务初始化失败: $e');
      _isDatabaseAvailable = false;
    }
  }

  /// WebSocket连接打开回调
  void _onWebSocketOpen() {
    _logger.info('🟢 直播聊天WebSocket连接已打开');
    _logger.info('📡 频道: $_channelName, 用户: $_userId');
    _isConnected = true;
    _reconnectAttempts = 0;
    _connectionController.add(true);
    _safeNotifyListeners();
  }

  /// WebSocket消息接收回调
  void _onWebSocketMessage(dynamic message) {
    try {
      final data = jsonDecode(message);
      _logger.info('📨 收到直播聊天消息: $data');

      // 处理消息
      _processMessage(data);
    } catch (e) {
      _logger.warning('❌ 处理直播聊天消息失败: $e');
      _logger.warning('原始消息: $message');
    }
  }

  /// WebSocket连接关闭回调
  void _onWebSocketClose() {
    _logger.info('🔴 直播聊天WebSocket连接已关闭');
    _logger.info('📡 频道: $_channelName, 用户: $_userId');
    _isConnected = false;
    _connectionController.add(false);
    _safeNotifyListeners();

    // 尝试重连
    _scheduleReconnect();
  }

  /// WebSocket错误回调
  void _onWebSocketError(dynamic error) {
    _logger.severe('💥 直播聊天WebSocket错误: $error');
    _logger.severe('📡 频道: $_channelName, 用户: $_userId');

    // 关键修复：确保错误处理不会阻塞UI
    try {
      _lastError = error.toString();
      _isConnected = false;

      // 异步更新连接状态，避免阻塞
      Future.microtask(() {
        _connectionController.add(false);
        _safeNotifyListeners();
      });

      // 分析错误类型并提供用户友好的反馈
      if (error.toString().contains('Failed host lookup')) {
        _logger.info('🔍 网络连接问题，将在后台自动重试');
      } else if (error.toString().contains('TimeoutException')) {
        _logger.info('⏱️ 连接超时，将尝试重新连接');
      } else {
        _logger.info('🔄 连接异常，正在尝试恢复');
      }

      // 异步安排重连，不阻塞当前线程
      Future.microtask(() {
        _scheduleReconnect();
      });
    } catch (e) {
      _logger.severe('错误处理过程中发生异常: $e');
      // 即使错误处理失败，也要确保app能继续运行
      Future.microtask(() {
        _connectionController.add(false);
      });
    }
  }

  /// 安排重连
  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      _logger.warning('直播聊天重连次数已达上限，停止重连');
      return;
    }

    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(_reconnectDelay, () {
      _reconnectAttempts++;
      _connectWithRetry();
    });
  }

  /// 检查网络连接性
  Future<void> _checkNetworkConnectivity() async {
    try {
      _logger.info('🔍 检查WebSocket服务器连接性...');

      // 检查DNS解析
      final addresses = await InternetAddress.lookup('ws.estockcafe.com');
      if (addresses.isNotEmpty) {
        _logger
            .info('✅ DNS解析成功: ws.estockcafe.com -> ${addresses.first.address}');
      }
    } catch (e) {
      _logger.severe('❌ DNS解析失败: ws.estockcafe.com - $e');

      // 尝试备用检查
      try {
        final googleAddresses = await InternetAddress.lookup('google.com');
        if (googleAddresses.isNotEmpty) {
          _logger.warning('⚠️ 网络连接正常，但WebSocket服务器域名无法解析');
          _logger.warning('💡 建议检查：');
          _logger.warning('   1. 域名配置是否正确');
          _logger.warning('   2. DNS服务器设置');
          _logger.warning('   3. 网络防火墙设置');
        }
      } catch (e2) {
        _logger.severe('❌ 网络连接异常: $e2');
        _logger.severe('💡 请检查网络连接');
      }

      // 不阻止连接尝试，让WebSocket自己处理错误
    }
  }

  /// 带重试的连接方法
  Future<void> _connectWithRetry() async {
    if (_isConnected || !_hasPermission) {
      return;
    }

    if (_userId == null || _channelName == null) {
      _logger.warning('无法重新连接直播聊天：缺少频道名称或用户ID');
      return;
    }

    try {
      _logger.info('尝试重连直播聊天服务: 第$_reconnectAttempts次尝试');

      // 在重连时也检查网络连接
      await _checkNetworkConnectivity();

      await connect(
        channelName: _channelName!,
        userId: _userId!,
        userName: _userName ?? 'User',
        chatroomInfo: _chatroomInfo,
      );

      _reconnectAttempts = 0;
      _logger.info('直播聊天重连成功');
    } catch (e) {
      _logger.severe('直播聊天重连失败: $e');

      // 分析错误类型并提供建议
      if (e.toString().contains('Failed host lookup')) {
        _logger.severe('🔍 DNS解析错误 - 可能的解决方案:');
        _logger.severe('   1. 检查网络连接');
        _logger.severe('   2. 更换DNS服务器 (如*******)');
        _logger.severe('   3. 检查防火墙设置');
        _logger.severe('   4. 联系网络管理员');
      }

      _scheduleReconnect();
    }
  }

  /// 处理接收到的消息
  void _processMessage(Map<String, dynamic> data) {
    try {
      // 检查是否是包含历史消息数组的connected类型消息
      if (data['type'] == 'connected' && data['msgs'] is List) {
        _logger.info(
            '📦 收到connected消息，处理其中的${(data['msgs'] as List).length}条历史消息');
        final List<dynamic> messages = data['msgs'];

        int processedCount = 0;
        for (var msgData in messages) {
          if (msgData is Map<String, dynamic>) {
            try {
              // 检查消息类型，跳过系统消息
              String? msgType = msgData['type'] as String?;
              if (msgType == 'app_state' ||
                  msgType == 'ping' ||
                  msgType == 'pong' ||
                  msgType == 'connect' ||
                  msgType == 'delivery_strategy' ||
                  msgType == 'user_state') {
                _logger.fine('跳过系统类型消息: $msgType');
                continue;
              }

              // 处理单条历史消息
              _processSingleMessage(msgData);
              processedCount++;
            } catch (e) {
              _logger.warning('处理历史消息失败: $e, 消息数据: $msgData');
            }
          }
        }

        _logger
            .info('✅ 成功处理了${processedCount}条历史消息，当前消息总数: ${_messages.length}');

        // 添加详细的消息列表日志
        if (_messages.isNotEmpty) {
          _logger.info('📋 当前消息列表预览:');
          for (int i = 0; i < _messages.length && i < 3; i++) {
            final msg = _messages[i];
            _logger.info(
                '  [$i] ${msg.userName}: ${msg.content} (时间: ${msg.createTime})');
          }
          if (_messages.length > 3) {
            _logger.info('  ... 还有${_messages.length - 3}条消息');
          }
        }
        _safeNotifyListeners();
        return;
      }

      // 处理单条消息
      _processSingleMessage(data);
      _safeNotifyListeners();
    } catch (e) {
      _logger.severe('处理直播聊天消息失败: $e');
    }
  }

  /// 处理单条消息
  void _processSingleMessage(Map<String, dynamic> data) {
    try {
      // 标准化消息数据
      final message = _standardizeMessage(data);

      // 检查消息是否已存在
      if (!_messageExists(message)) {
        // 添加消息到列表
        _messages.add(message);
        _logger.fine('📝 添加新消息到列表: ${message.content}');

        // 对消息进行排序
        _sortMessages();

        // 发送消息到流
        _messageController.add(message);

        // 保存消息到本地数据库
        if (_channelName != null &&
            _isDatabaseAvailable &&
            _chatDatabaseService != null &&
            _userId != null &&
            _userId != 'guest' &&
            _userId!.isNotEmpty) {
          String channelToUse = message.channelName ?? _channelName!;
          _chatDatabaseService!.saveMessage(message, channelToUse);
          _logger.fine('直播聊天消息已保存到数据库，频道: $channelToUse');
        }
      } else {
        _logger.fine('⚠️ 消息已存在，跳过: ${message.content}');
      }
    } catch (e) {
      _logger.severe('处理单条直播聊天消息失败: $e');
    }
  }

  /// 标准化消息数据
  ChatMessageData _standardizeMessage(Map<String, dynamic> data) {
    try {
      // 处理不同格式的消息数据
      Map<String, dynamic> standardData = Map<String, dynamic>.from(data);

      // 确保有必要的字段
      if (!standardData.containsKey('channelName') && _channelName != null) {
        standardData['channelName'] = _channelName;
      }

      if (!standardData.containsKey('channel_name') && _channelName != null) {
        standardData['channel_name'] = _channelName;
      }

      // 处理时间戳
      if (!standardData.containsKey('timestamp')) {
        standardData['timestamp'] = DateTime.now().toIso8601String();
      }

      if (!standardData.containsKey('create_time')) {
        standardData['create_time'] =
            DateTime.now().millisecondsSinceEpoch ~/ 1000;
      }

      return ChatMessageData.fromJson(standardData);
    } catch (e) {
      _logger.severe('标准化直播聊天消息失败: $e');
      rethrow;
    }
  }

  /// 检查消息是否已存在
  bool _messageExists(ChatMessageData message) {
    return _messages.any((m) {
      // 优先使用msgId进行比较
      if (message.msgId != null && m.msgId != null) {
        return m.msgId == message.msgId;
      }

      // 如果没有msgId，使用id进行比较
      if (message.id.isNotEmpty && m.id.isNotEmpty) {
        return m.id == message.id;
      }

      // 最后使用内容和时间戳进行比较
      return m.content == message.content &&
          m.userId == message.userId &&
          (m.createTime ?? 0) == (message.createTime ?? 0);
    });
  }

  /// 对消息进行排序
  void _sortMessages() {
    _messages.sort((a, b) {
      final timeA =
          a.createTime ?? (a.timestamp.millisecondsSinceEpoch ~/ 1000);
      final timeB =
          b.createTime ?? (b.timestamp.millisecondsSinceEpoch ~/ 1000);
      return timeA.compareTo(timeB); // 升序排列，旧消息在前
    });
  }

  /// 发送消息
  Future<void> send({
    required String content,
    String type = 'text',
    String? toUserId,
    String? toUserName,
    ChatMessageData? replyTo,
  }) async {
    if (!_isConnected) {
      _logger.warning('直播聊天服务器未连接，无法发送消息');
      return;
    }

    final message = {
      'userId': _userId,
      'userName': _userName,
      'channelName': _channelName,
      'content': content,
      'type': type,
      'timestamp': DateTime.now().toIso8601String(),
      'create_time': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      'client_type': Platform.isAndroid ? 'android' : 'ios',
    };

    if (toUserId != null && toUserName != null) {
      message['toUserId'] = toUserId;
      message['toUserName'] = toUserName;
    }

    // 添加回复消息处理
    if (replyTo != null) {
      message['reply'] = replyTo.toJson();
      _logger.info('添加回复消息数据: ${replyTo.content}');
    }

    try {
      await _webSocket?.send(jsonEncode(message));
      _logger.info('直播聊天消息发送成功: $content');

      // 将发送的消息添加到本地列表
      try {
        final chatMessage = ChatMessageData.fromJson(message);
        if (!_messageExists(chatMessage)) {
          _messages.add(chatMessage);
          _sortMessages();
          _messageController.add(chatMessage);
          _safeNotifyListeners();
        }
      } catch (e) {
        _logger.warning('处理发送的直播聊天消息失败: $e');
      }
    } catch (e) {
      _logger.severe('发送直播聊天消息失败: $e');
      _lastError = e.toString();
    }
  }

  /// 发送自定义消息（兼容web端格式）
  Future<void> sendCustomMessage(Map<String, dynamic> message) async {
    if (!_isConnected) {
      _logger.warning('直播聊天服务器未连接，无法发送消息');
      return;
    }

    // 确保消息包含频道信息
    if (_channelName != null) {
      message['channel_name'] = _channelName;
      message['channelName'] = _channelName;
    }

    _logger.info('发送直播聊天自定义消息: ${jsonEncode(message)}');

    try {
      await _webSocket?.send(jsonEncode(message));
      _logger.info('直播聊天自定义消息发送成功');
    } catch (e) {
      _logger.severe('发送直播聊天自定义消息失败: $e');
      _lastError = e.toString();
    }
  }

  /// 获取历史消息
  Future<List<ChatMessageData>> getHistoryMessages() async {
    if (!_isConnected) {
      _logger.warning('直播聊天服务器未连接，尝试从本地数据库获取历史消息');
      return _loadMessagesFromDatabase();
    }

    // 请求服务器历史消息
    await requestHistoryMessages();

    // 返回当前消息列表
    return List.unmodifiable(_messages);
  }

  /// 从数据库加载消息
  Future<List<ChatMessageData>> _loadMessagesFromDatabase() async {
    if (!_isDatabaseAvailable ||
        _chatDatabaseService == null ||
        _channelName == null) {
      _logger.warning('数据库不可用或频道名为空，无法加载历史消息');
      return [];
    }

    try {
      final messages = await _chatDatabaseService!
          .getLatestMessages(_channelName!, limit: 50);
      _logger.info('从数据库加载了${messages.length}条直播聊天历史消息');

      // 更新本地消息列表
      _messages.clear();
      _messages.addAll(messages);
      _sortMessages();

      _safeNotifyListeners();
      return messages;
    } catch (e) {
      _logger.warning('从数据库加载直播聊天历史消息失败: $e');
      return [];
    }
  }

  /// 请求历史消息（增量同步）
  Future<void> requestHistoryMessages({
    int? beforeTime,
    int limit = 50,
  }) async {
    if (!_isConnected) {
      _logger.warning('直播聊天服务器未连接，无法请求历史消息');
      return;
    }

    try {
      // 实现增量同步逻辑
      int? syncFromTime;

      if (beforeTime != null) {
        // 如果指定了时间，使用指定时间
        syncFromTime = beforeTime;
      } else {
        // 检查数据库中最后一条消息的时间
        if (_isDatabaseAvailable &&
            _chatDatabaseService != null &&
            _channelName != null) {
          final latestTimestamp = await _chatDatabaseService!
              .getLatestMessageTimestamp(_channelName!);
          if (latestTimestamp > 0) {
            // 数据库有消息，从最后一条消息时间开始同步
            syncFromTime = latestTimestamp;
            _logger.info(
                '直播聊天室数据库有消息，从最后一条消息时间开始同步: ${DateTime.fromMillisecondsSinceEpoch(latestTimestamp * 1000)}');
          } else {
            // 数据库为空，拉取最新消息（不指定时间）
            syncFromTime = null;
            _logger.info('直播聊天室数据库为空，拉取最新消息');
          }
        }
      }

      final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      // 根据后端实现，历史消息通过connect消息获取
      final message = {
        'type': 'connect',
        'id': _userId, // 后端期望的字段名
        'msg': syncFromTime != null ? '请求增量消息' : '请求最新消息', // 后端期望的消息内容字段
        'name': _userName ?? 'User$_userId', // 用户名
        'token': _chatroomInfo?['token']?.toString() ?? '',
        'channel_name': _channelName,
        'timestamp': (syncFromTime ?? currentTime).toString(),
        'client_type': Platform.isAndroid ? 'android' : 'ios',
      };

      if (syncFromTime != null) {
        message['afterTime'] = syncFromTime.toString(); // 请求此时间之后的消息
      }

      _logger.info(
          '请求直播聊天历史消息（增量同步）: ${syncFromTime != null ? "从时间点${DateTime.fromMillisecondsSinceEpoch(syncFromTime * 1000)}" : "最新消息"}');
      await _webSocket?.send(jsonEncode(message));
    } catch (e) {
      _logger.severe('请求直播聊天历史消息失败: $e');
      _lastError = e.toString();
    }
  }

  /// 清空消息列表
  void clearMessages() {
    _messages.clear();
    _safeNotifyListeners();
  }
}
