import 'package:flutter/material.dart';
import '../../services/records_service.dart';
import '../../widgets/live_record_list.dart';
import '../../theme.dart';

class LiveRecordsPage extends StatefulWidget {
  const LiveRecordsPage({Key? key}) : super(key: key);

  @override
  _LiveRecordsPageState createState() => _LiveRecordsPageState();
}

class _LiveRecordsPageState extends State<LiveRecordsPage> {
  final RecordsService _recordsService = RecordsService();
  final List<LiveRecordItem> _records = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 1;
  final int _pageSize = 10;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadRecords();
  }

  Future<void> _loadRecords({bool refresh = false}) async {
    if (_isLoading) return;

    if (refresh) {
      setState(() {
        _records.clear();
        _currentPage = 1;
        _hasMore = true;
        _errorMessage = '';
      });
    }

    if (!_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final data = await _recordsService.getLiveRecords(
        page: _currentPage,
        limit: _pageSize,
      );

      final List<dynamic> newRecords = data['records'] ?? [];
      final int total = data['total'] ?? 0;

      setState(() {
        for (var record in newRecords) {
          _records.add(LiveRecordItem(
            remark: record['remark'] ?? '直播记录',
            number: record['number'] != null ? int.tryParse(record['number'].toString()) ?? 0 : 0,
            isPositive: record['operator'] == 1,
            amount: record['amount'] != null && record['amount'] != '-' 
                ? (record['amount'] is num 
                    ? record['amount'].toStringAsFixed(2)
                    : record['amount'].toString())
                : '-',
            payMethod: record['paymethod'] ?? '',
            createTime: record['create_time'] ?? '',
            status: record['status'] ?? '未知状态',
          ));
        }

        _hasMore = _records.length < total;
        _currentPage++;
        _errorMessage = '';
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
    return;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('直播记录'),
        backgroundColor: Colors.white,
        foregroundColor: AppColors.textEmphasis,
        elevation: 0.5,
      ),
      body: Column(
        children: [
          if (_errorMessage.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(8.0),
              color: Colors.red[50],
              width: double.infinity,
              child: Text(
                _errorMessage,
                style: const TextStyle(color: Colors.red),
                textAlign: TextAlign.center,
              ),
            ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () => _loadRecords(refresh: true),
              child: LiveRecordList(
                records: _records,
                isLoading: _isLoading && _records.isEmpty,
                hasMore: _hasMore && !_isLoading,
                onLoadMore: _loadRecords,
                emptyText: '暂无直播记录',
              ),
            ),
          ),
        ],
      ),
    );
  }
}
