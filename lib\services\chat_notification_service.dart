import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:audioplayers/audioplayers.dart';
import '../models/chat_message.dart';
import '../models/user.dart';
import 'chat_database_service.dart';

class ChatNotificationService extends ChangeNotifier {
  static final ChatNotificationService _instance =
      ChatNotificationService._internal();
  factory ChatNotificationService() => _instance;

  ChatNotificationService._internal() {
    _loadSettings();
    // 设置日志级别
    _logger.level = Level.WARNING;
    // 初始化音频播放器
    _initAudioPlayer();
    // 尝试从SharedPreferences加载上次登录的用户ID并恢复未读消息
    _restoreUnreadMessages();
  }

  final Logger _logger = Logger('ChatNotificationService');
  bool _initialized = false;
  final List<ChatMessageData> _unreadMessages = [];

  // 数据库服务
  ChatDatabaseService? _databaseService;

  // 音频播放器
  AudioPlayer? _audioPlayer;
  bool _isAudioPlayerReady = false;

  // 获取总未读消息数
  int get unreadCount => _unreadMessages.length;

  // 获取所有未读消息
  List<ChatMessageData> get unreadMessages =>
      List.unmodifiable(_unreadMessages);

  // 用户设置：是否只显示与用户相关的消息
  bool _onlyShowRelevantMessages = false;
  bool get onlyShowRelevantMessages => _onlyShowRelevantMessages;

  // 通知设置
  bool _notificationsEnabled = true;
  bool get notificationsEnabled => _notificationsEnabled;
  set notificationsEnabled(bool value) {
    _notificationsEnabled = value;
    _saveNotificationSettings();
    notifyListeners();
  }

  bool _onlyMentionNotifications = false;
  bool get onlyMentionNotifications => _onlyMentionNotifications;
  set onlyMentionNotifications(bool value) {
    _onlyMentionNotifications = value;
    _saveNotificationSettings();
    notifyListeners();
  }

  // 添加通知声音设置
  bool _notificationSoundEnabled = true;
  bool get notificationSoundEnabled => _notificationSoundEnabled;
  set notificationSoundEnabled(bool value) {
    _notificationSoundEnabled = value;
    _saveNotificationSettings();
    notifyListeners();
  }

  // 通知设置
  bool _playSoundOnNewMessage = false; // 默认不播放声音
  String _notificationType = 'all'; // 默认全部消息提醒

  // 通知设置的getter和setter
  bool get playSoundOnNewMessage => _playSoundOnNewMessage;
  String get notificationType => _notificationType;

  void setPlaySoundOnNewMessage(bool value) {
    _playSoundOnNewMessage = value;
    _saveSettings();
    notifyListeners();
  }

  void setNotificationType(String value) {
    _notificationType = value;
    _saveSettings();
    notifyListeners();
  }

  // 当前用户ID，用于判断消息是否与用户相关
  String? _currentUserId;
  String? get currentUserId => _currentUserId;
  set currentUserId(String? userId) {
    _currentUserId = userId;
    if (userId != null) {
      _databaseService = ChatDatabaseService(userId: userId);
      _loadUnreadMessagesFromDatabase();
    } else {
      // 当设置为null时，清空未读消息列表
      _unreadMessages.clear();
      _databaseService = null;
      notifyListeners();
    }
  }

  // 初始化音频播放器
  Future<void> _initAudioPlayer() async {
    try {
      _audioPlayer = AudioPlayer();
      _isAudioPlayerReady = true;
      _logger.info('音频播放器初始化成功');
    } catch (e) {
      _logger.severe('音频播放器初始化失败: $e');
      _isAudioPlayerReady = false;
    }
  }

  // 播放通知声音
  Future<void> _playNotificationSound() async {
    // 检查声音设置是否开启，如果关闭则不播放声音
    if (!_isAudioPlayerReady ||
        _audioPlayer == null ||
        !_playSoundOnNewMessage) {
      _logger.info('声音设置已关闭或音频播放器未就绪，不播放声音');
      return;
    }

    try {
      // 使用资源文件播放声音
      await _audioPlayer!.play(AssetSource('sounds/notification.wav'));
      _logger.info('播放通知声音');
    } catch (e) {
      _logger.warning('播放通知声音失败: $e');
      // 如果播放失败，尝试重新初始化音频播放器
      _initAudioPlayer();
    }
  }

  // 请求通知权限（为了保持API兼容性，保留此方法）
  Future<void> requestNotificationPermissions() async {
    _initialized = true;
    _logger.info('通知服务初始化完成');
  }

  // 加载用户设置
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _onlyShowRelevantMessages =
          prefs.getBool('only_show_relevant_messages') ?? false;
      _notificationsEnabled =
          prefs.getBool('chat_notifications_enabled') ?? true;
      _onlyMentionNotifications =
          prefs.getBool('only_mention_notifications') ?? false;
      _notificationSoundEnabled =
          prefs.getBool('notification_sound_enabled') ?? true;
      _playSoundOnNewMessage = prefs.getBool('chat_play_sound') ?? false;
      _notificationType = prefs.getString('chat_notification_type') ?? 'all';
      notifyListeners();
    } catch (e) {
      _logger.severe('加载聊天通知设置失败: $e');
    }
  }

  // 更新用户设置
  Future<void> setOnlyShowRelevantMessages(bool value) async {
    _onlyShowRelevantMessages = value;
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('only_show_relevant_messages', value);
      notifyListeners();
    } catch (e) {
      _logger.severe('保存聊天通知设置失败: $e');
    }
  }

  // 保存设置到SharedPreferences
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('chat_play_sound', _playSoundOnNewMessage);
      await prefs.setString('chat_notification_type', _notificationType);
      _logger.info('通知设置已保存');
    } catch (e) {
      _logger.severe('保存通知设置失败: $e');
    }
  }

  // 判断消息是否与当前用户相关
  bool isMessageRelevantToUser(ChatMessageData message) {
    if (_currentUserId == null) return false;

    // 如果是系统消息，认为与所有用户相关
    if (message.isSystem) return true;

    // 如果是当前用户发送的消息，不需要通知
    if (message.userId == _currentUserId) return false;

    // 如果消息中包含@当前用户，认为与用户相关
    if (message.content.contains('@${_currentUserId}')) return true;

    // 如果是私聊消息且发给当前用户，认为与用户相关
    if (message.isPrivate && message.toUserId == _currentUserId) return true;

    // 其他情况，如果设置了只显示相关消息，则不通知
    return !_onlyShowRelevantMessages;
  }

  // 获取消息优先级（用于排序）
  int _getMessagePriority(ChatMessageData message) {
    if (_currentUserId == null) return 0;

    // 私聊消息最高优先级
    if (message.isPrivate && message.toUserId == _currentUserId) return 100;

    // @用户的消息次高优先级
    if (message.content.contains('@${_currentUserId}')) return 80;

    // 系统消息中等优先级
    if (message.isSystem) return 60;

    // 普通消息最低优先级
    return 20;
  }

  Future<void> _loadNotificationSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _notificationsEnabled =
          prefs.getBool('chat_notifications_enabled') ?? true;
      _onlyMentionNotifications =
          prefs.getBool('only_mention_notifications') ?? false;
      _notificationSoundEnabled =
          prefs.getBool('notification_sound_enabled') ?? true;
    } catch (e) {
      _logger.severe('加载通知设置失败: $e');
    }
  }

  Future<void> _saveNotificationSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('chat_notifications_enabled', _notificationsEnabled);
      await prefs.setBool(
          'only_mention_notifications', _onlyMentionNotifications);
      await prefs.setBool(
          'notification_sound_enabled', _notificationSoundEnabled);
    } catch (e) {
      _logger.severe('保存通知设置失败: $e');
    }
  }

  bool _shouldShowNotification(ChatMessageData message, {User? currentUser}) {
    // 如果通知被禁用，不显示通知
    if (!_notificationsEnabled) return false;

    // 过滤系统类型消息
    if (message.type == 'app_state' ||
        message.type == 'ping' ||
        message.type == 'pong' ||
        message.type == 'connect' ||
        message.type == 'delivery_strategy' ||
        message.type == 'user_state') {
      _logger.info('跳过系统类型消息通知: ${message.type}');
      return false;
    }

    // 过滤空内容消息
    if (message.content.trim().isEmpty) {
      _logger.warning('跳过显示空内容的通知');
      return false;
    }

    // 如果是当前用户发送的消息，不显示通知
    if (currentUser != null && message.userId == currentUser.id.toString()) {
      return false;
    }

    // 根据通知类型过滤消息
    switch (_notificationType) {
      case 'admin_only':
        // 只显示管理员消息
        return message.isAdmin;

      case 'mention_only':
        // 只显示回复当前用户的消息或@当前用户的消息
        if (currentUser != null) {
          // 检查是否是回复当前用户的消息
          if (message.reply != null &&
              message.reply!.userId == currentUser.id.toString()) {
            return true;
          }

          // 检查消息是否提及了当前用户
          final mentionPattern =
              RegExp('@${currentUser.name}\\b', caseSensitive: false);
          final isPrivateToUser = message.toUserId == currentUser.id.toString();

          return mentionPattern.hasMatch(message.content) || isPrivateToUser;
        }
        return false;

      case 'all':
      default:
        // 显示所有消息
        return true;
    }
  }

  // 从数据库加载未读消息
  Future<void> _loadUnreadMessagesFromDatabase() async {
    if (_databaseService == null) {
      _logger.warning('数据库服务未初始化，无法加载未读消息');
      return;
    }

    try {
      // 先清理过期消息，然后再加载未读消息
      await _cleanupOldMessages();

      // 限制每次最多加载200条未读消息
      final messages = await _databaseService!.getUnreadMessages(limit: 200);

      if (messages.isEmpty) {
        _logger.info('没有未读消息');
        return;
      }

      // 清空当前未读消息列表
      _unreadMessages.clear();

      // 添加到列表
      _unreadMessages.addAll(messages);
      _sortUnreadMessages();

      _logger.info('从数据库加载了 ${_unreadMessages.length} 条未读消息');
      notifyListeners();
    } catch (e) {
      _logger.warning('从数据库加载未读消息失败: $e');
    }
  }

  // 清理过期消息
  Future<void> _cleanupOldMessages() async {
    if (_databaseService == null) {
      _logger.warning('数据库服务未初始化，无法清理过期消息');
      return;
    }

    try {
      await _databaseService!.cleanupOldMessages();
      _logger.info('清理了过期消息');
    } catch (e) {
      _logger.warning('清理过期消息失败: $e');
    }
  }

  // 标记消息通知已显示
  Future<void> _markNotificationShown(String messageId) async {
    if (_databaseService == null) {
      _logger.warning('数据库服务未初始化，无法标记消息通知已显示');
      return;
    }

    try {
      await _databaseService!.markNotificationShown(messageId);
      _logger.info('成功标记消息通知已显示: $messageId');
    } catch (e) {
      _logger.warning('标记消息通知已显示失败: $e');
    }
  }

  // 清除所有未读消息
  Future<void> clearUnreadMessages() async {
    if (_unreadMessages.isEmpty) {
      return;
    }

    // 标记所有消息为已读
    if (_databaseService != null) {
      try {
        // 对每个频道的消息进行标记
        final channelNames = <String>{'default'}; // 添加默认频道
        for (var message in _unreadMessages) {
          if (message.channelName != null && message.channelName!.isNotEmpty) {
            channelNames.add(message.channelName!);
          }
        }

        // 首先尝试按频道标记所有消息为已读
        for (var channelName in channelNames) {
          await _databaseService!.markAllMessagesAsRead(channelName);
        }

        // 然后再执行一次全局的标记，确保所有消息都被标记为已读
        await _databaseService!.clearAllUnreadMessages();

        _logger.info('成功标记所有消息为已读');
      } catch (e) {
        _logger.warning('标记所有消息为已读失败: $e');
      }
    }

    _unreadMessages.clear();
    notifyListeners();
  }

  // 添加未读消息
  Future<void> addUnreadMessage(ChatMessageData message) async {
    // 检查消息是否已存在
    final existingIndex = _unreadMessages.indexWhere((m) => m.id == message.id);
    if (existingIndex >= 0) {
      return; // 消息已存在，跳过
    }

    _unreadMessages.add(message);
    _sortUnreadMessages();

    // 确保消息在数据库中被标记为未读
    if (_databaseService != null) {
      // 如果channelName为空，使用默认频道名称
      final channelName = message.channelName ?? 'default';

      // 不再直接保存消息，而是先检查消息是否存在
      try {
        // 只更新消息的状态，避免重复保存
        await _databaseService!.updateMessageStatus(message.id,
            read: false, notificationShown: false);
        _logger.fine('已更新消息状态为未读: ${message.id}');
      } catch (e) {
        _logger.warning('更新消息状态失败，尝试保存消息: $e');
        // 如果更新失败（可能是消息不存在），才尝试保存消息
        await _databaseService!.saveMessage(message, channelName);
      }
    }

    notifyListeners();
  }

  // 按优先级排序未读消息
  void _sortUnreadMessages() {
    _unreadMessages.sort((a, b) {
      // 首先按优先级排序
      final priorityA = _getMessagePriority(a);
      final priorityB = _getMessagePriority(b);

      if (priorityA != priorityB) {
        return priorityB.compareTo(priorityA); // 高优先级在前
      }

      // 其次按时间排序
      final timeA =
          a.createTime ?? (a.timestamp.millisecondsSinceEpoch ~/ 1000);
      final timeB =
          b.createTime ?? (b.timestamp.millisecondsSinceEpoch ~/ 1000);
      return timeB.compareTo(timeA); // 新消息在前
    });
  }

  Future<void> addMessage(ChatMessageData message, {User? currentUser}) async {
    // 检查是否应该显示通知
    if (_shouldShowNotification(message, currentUser: currentUser)) {
      // 添加到未读消息
      await addUnreadMessage(message);

      // 播放通知声音
      if (_notificationSoundEnabled && _isAudioPlayerReady) {
        _playNotificationSound();
      }
    }
  }

  // 显示应用内通知
  void showInAppNotification(
    BuildContext context,
    ChatMessageData message, {
    User? currentUser,
  }) {
    if (!_shouldShowNotification(message, currentUser: currentUser)) {
      return;
    }

    // 如果消息被撤回，不显示通知
    if (message.revoked) {
      return;
    }

    // 添加到未读消息列表
    addUnreadMessage(message);

    // 标记通知已显示
    if (_databaseService != null) {
      _markNotificationShown(message.id);
    }

    // 播放通知声音（如果启用）
    if (_playSoundOnNewMessage) {
      _playNotificationSound();
    }

    // 使用SnackBar显示应用内通知
    if (context.mounted) {
      final snackBar = SnackBar(
        content: Row(
          children: [
            CircleAvatar(
              radius: 15,
              backgroundImage:
                  message.userAvatar != null && message.userAvatar!.isNotEmpty
                      ? NetworkImage(message.userAvatar!)
                      : null,
              child: message.userAvatar == null || message.userAvatar!.isEmpty
                  ? const Icon(Icons.person, size: 15)
                  : null,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    message.isSystem
                        ? '系统消息'
                        : message.isPrivate
                            ? '私信: ${message.userName}'
                            : message.userName,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    message.isImage ? '[图片消息]' : message.content,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(8),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        action: SnackBarAction(
          label: '查看',
          onPressed: () {
            // 导航到聊天室，并传递消息ID
            Navigator.of(context).pushNamed(
              '/chat',
              arguments: {'messageId': message.id},
            );
          },
        ),
      );

      ScaffoldMessenger.of(context).showSnackBar(snackBar);
    }
  }

  // 清除消息（兼容旧API）
  void clearMessages() {
    clearUnreadMessages();
  }

  // 从SharedPreferences加载上次登录的用户ID并恢复未读消息
  Future<void> _restoreUnreadMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUserId = prefs.getString('last_logged_in_user_id');

      if (lastUserId != null && lastUserId.isNotEmpty) {
        _logger.info('发现上次登录的用户ID: $lastUserId，尝试恢复未读消息');
        _currentUserId = lastUserId;
        _databaseService = ChatDatabaseService(userId: lastUserId);
        await _loadUnreadMessagesFromDatabase();
      } else {
        _logger.info('没有找到上次登录的用户ID，跳过恢复未读消息');
      }
    } catch (e) {
      _logger.severe('恢复未读消息失败: $e');
    }
  }
}
