import 'package:estockcafe/utils/logging.dart';
import 'package:logging/logging.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class TokenService {
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String accessTokenExpiryKey = 'access_token_expiry';
  static const String refreshTokenExpiryKey = 'refresh_token_expiry';

  final Logger _logger = Logging.getLogger('TokenService');
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  // 添加一个标志，用于防止短时间内重复刷新令牌
  bool _isRefreshingToken = false;
  DateTime? _lastRefreshAttempt;
  static const _refreshCooldown = Duration(seconds: 5); // 将冷却时间从10秒减少到5秒

  Future<String?> getAccessToken() async {
    return _getToken(accessTokenKey, accessTokenExpiryKey);
  }

  Future<void> setAccessToken(String accessToken, int expiresIn) async {
    await _setToken(
        accessTokenKey, accessTokenExpiryKey, accessToken, expiresIn);
    _logger.info('设置新的访问令牌（有效期$expiresIn秒）');
  }

  Future<void> removeAccessToken() async {
    await _removeToken(accessTokenKey, accessTokenExpiryKey);
    _logger.info('移除访问令牌');
  }

  Future<String?> getRefreshToken() async {
    return _getToken(refreshTokenKey, refreshTokenExpiryKey);
  }

  Future<void> setRefreshToken(String refreshToken, int expiresIn) async {
    await _setToken(
        refreshTokenKey, refreshTokenExpiryKey, refreshToken, expiresIn);
    _logger.info('设置新的刷新令牌（有效期$expiresIn秒）');
  }

  Future<void> removeRefreshToken() async {
    await _removeToken(refreshTokenKey, refreshTokenExpiryKey);
    _logger.info('移除刷新令牌');
  }

  Future<void> removeTokens() async {
    await removeAccessToken();
    await removeRefreshToken();
    _logger.info('移除所有令牌');
  }

  Future<bool> needsTokenRefresh() async {
    final accessToken = await getAccessToken();
    final refreshToken = await getRefreshToken();
    return accessToken == null || refreshToken == null;
  }

  Future<DateTime?> getAccessTokenExpiry() async {
    return _getTokenExpiry(accessTokenExpiryKey);
  }

  Future<DateTime?> getRefreshTokenExpiry() async {
    return _getTokenExpiry(refreshTokenExpiryKey);
  }

  // 新增：检查刷新令牌是否有效
  Future<bool> isRefreshTokenValid() async {
    final refreshToken = await getRefreshToken();
    final refreshTokenExpiry = await getRefreshTokenExpiry();
    return refreshToken != null &&
        refreshTokenExpiry != null &&
        DateTime.now().isBefore(refreshTokenExpiry);
  }

  // 新增：获取令牌的剩余有效时间（秒）
  Future<int?> getTokenRemainingTime(String tokenKey, String expiryKey) async {
    final expiry = await _getTokenExpiry(expiryKey);
    if (expiry != null) {
      final remaining = expiry.difference(DateTime.now());
      return remaining.inSeconds > 0 ? remaining.inSeconds : null;
    }
    return null;
  }

  Future<bool> isTokenExpired(String token) async {
    final accessTokenExpiry = await getAccessTokenExpiry();
    if (accessTokenExpiry == null) return true;
    return DateTime.now().isAfter(accessTokenExpiry);
  }

  Future<String?> refreshToken() async {
    try {
      // 即使刷新令牌看起来已过期，也尝试使用它
      final refreshToken = await _secureStorage.read(key: refreshTokenKey);
      if (refreshToken == null) {
        _logger.warning('无可用的刷新令牌');
        return null;
      }

      // 在实际项目中，这里应该调用后端API刷新令牌
      // 由于当前没有实现实际的刷新逻辑，我们暂时返回当前的访问令牌
      // 并延长其有效期，以避免频繁的刷新尝试
      final currentToken = await _secureStorage.read(key: accessTokenKey);
      if (currentToken != null) {
        // 延长访问令牌的有效期（1小时）
        await setAccessToken(currentToken, 3600);
        _logger.info('延长了访问令牌的有效期');
        return currentToken;
      } else {
        _logger.warning('无可用的访问令牌');
        return null;
      }
    } catch (e) {
      _logger.severe('刷新令牌时发生错误: $e');
      return null;
    }
  }

  // 私有辅助方法
  Future<String?> _getToken(String tokenKey, String expiryKey) async {
    final token = await _secureStorage.read(key: tokenKey);
    final expiryTimestamp = await _secureStorage.read(key: expiryKey);
    // _logger.fine('获取令牌：$token , 过期时间戳：$expiryTimestamp');
    if (token != null && expiryTimestamp != null) {
      final expiryDate =
          DateTime.fromMillisecondsSinceEpoch(int.parse(expiryTimestamp));
      if (DateTime.now().isBefore(expiryDate)) {
        return token;
      } else {
        // Token已过期，清除它
        await _removeToken(tokenKey, expiryKey);
      }
    }
    return null;
  }

  Future<void> _setToken(
      String tokenKey, String expiryKey, String token, int expiresIn) async {
    await _secureStorage.write(key: tokenKey, value: token);
    final expiryDate = DateTime.now().add(Duration(seconds: expiresIn));
    await _secureStorage.write(
        key: expiryKey, value: expiryDate.millisecondsSinceEpoch.toString());
  }

  Future<void> _removeToken(String tokenKey, String expiryKey) async {
    await _secureStorage.delete(key: tokenKey);
    await _secureStorage.delete(key: expiryKey);
  }

  Future<DateTime?> _getTokenExpiry(String expiryKey) async {
    final expiryTimestamp = await _secureStorage.read(key: expiryKey);
    if (expiryTimestamp != null) {
      return DateTime.fromMillisecondsSinceEpoch(int.parse(expiryTimestamp));
    }
    return null;
  }
}
