import 'package:logging/logging.dart';
import 'package:estockcafe/utils/logging.dart';

class CategoryItem {
  final String category;
  final int categoryId;
  final List<PostItem> posts;
  final bool hasNextPage;
  final int currentPage;
  final int totalPages;

  CategoryItem({
    required this.category,
    required this.categoryId,
    required this.posts,
    required this.hasNextPage,
    required this.currentPage,
    required this.totalPages,
  });

  static final Logger _logger = Logging.getLogger('CategoryItem');

  factory CategoryItem.fromJson(Map<String, dynamic> json) {
    try {
      final category = json['category'] as String? ?? '';
      final categoryId = _parseIntOrZero(json['category_id']);
      final List<PostItem> posts = [];
      if (json['lists'] is List) {
        for (var postJson in json['lists']) {
          try {
            posts.add(PostItem.fromJson(postJson));
          } catch (e) {
            _logger.warning('解析 PostItem 时出错: $e');
          }
        }
      } else {
        _logger.warning('json["lists"] 不是 List 类型');
      }
      final hasNextPage = json['has_next_page'] as bool? ?? false;
      final currentPage = _parseIntOrZero(json['current_page']);
      final totalPages = _parseIntOrZero(json['total_pages']);

      return CategoryItem(
        category: category,
        categoryId: categoryId,
        posts: posts,
        hasNextPage: hasNextPage,
        currentPage: currentPage,
        totalPages: totalPages,
      );
    } catch (e) {
      _logger.severe('解析 CategoryItem 时出错: $e');
      rethrow;
    }
  }

  static int _parseIntOrZero(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    return 0;
  }
}

class PostItem {
  final int id;
  final String title;
  final String thumb;
  final String date;
  final int ping;
  final int view;
  final String route;
  final String content;
  final Map<dynamic, dynamic>? meta;

  PostItem({
    required this.id,
    required this.title,
    required this.thumb,
    required this.date,
    required this.ping,
    required this.view,
    required this.route,
    this.content = '',
    this.meta = const {},
  });

  static final Logger _logger = Logging.getLogger('PostItem');

  factory PostItem.fromJson(Map<String, dynamic> json) {
    try {
      return PostItem(
        id: json['id'], // 确保 id 始终被转换为字符串
        title: json['title'] as String? ?? '',
        thumb: json['thumb'] == null ? '' : json['thumb'] as String,
        date: json['date'] as String? ?? '',
        ping: _parseIntOrZero(json['ping']),
        view: _parseIntOrZero(json['view']),
        route: json['route'] as String? ?? '',
        content: json['content'] as String? ?? '',
        meta: _parseMeta(json['meta']),
      );
    } catch (e) {
      _logger.severe('解析 PostItem 时出错: $e');
      rethrow;
    }
  }

  static int _parseIntOrZero(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    return 0;
  }

  static Map<dynamic, dynamic>? _parseMeta(dynamic meta) {
    if (meta == null) return null;
    if (meta is Map) return meta;
    if (meta is List) {
      // _logger.warning('meta 是一个列表，而不是预期的 Map。返回空 Map。');
      return {};
    }
    // _logger.warning('meta 既不是 Map 也不是 List。返回空 Map。');
    return {};
  }
}
