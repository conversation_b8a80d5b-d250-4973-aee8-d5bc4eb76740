import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:logging/logging.dart';
import '../models/chat_message.dart';
import '../utils/web_socket.dart' as ws;
import '../config/config.dart';
import 'navigation_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;

  final Logger _logger = Logger('NotificationService');
  late FirebaseMessaging _firebaseMessaging;
  final NavigationService _navigationService = NavigationService();

  // 添加本地通知插件实例
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // 未读消息计数
  int _unreadCount = 0;

  // 存储最后点击的通知负载
  Map<String, dynamic>? _lastClickedNotificationPayload;

  // FCM 连接状态
  bool _isFcmConnected = false;
  bool get isFcmConnected => _isFcmConnected;

  // 当前的FCM令牌
  String? _currentToken;

  NotificationService._internal() {
    _logger.info('初始化通知服务');
  }

  // 初始化通知服务
  Future<void> initialize() async {
    try {
      // 在这里初始化 FirebaseMessaging 实例
      _firebaseMessaging = FirebaseMessaging.instance;

      // 初始化本地通知插件
      await _initLocalNotifications();

      // 配置 FCM
      await _configureFirebaseMessaging();

      _logger.info('通知服务初始化完成');
    } catch (e) {
      _logger.severe('通知服务初始化失败: $e');
    }
  }

  // 初始化本地通知插件
  Future<void> _initLocalNotifications() async {
    try {
      _logger.info('初始化本地通知插件');

      // 初始化设置
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      const InitializationSettings initializationSettings =
          InitializationSettings(android: initializationSettingsAndroid);

      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: (NotificationResponse response) {
          _logger.info('点击通知: ${response.payload}');
          if (response.payload != null) {
            try {
              final payload =
                  json.decode(response.payload!) as Map<String, dynamic>;
              _handleNotificationTap(payload);
            } catch (e) {
              _logger.warning('解析通知负载失败: $e');
            }
          }
        },
      );

      // 创建通知渠道
      await _createNotificationChannels();

      _logger.info('本地通知插件初始化完成');
    } catch (e) {
      _logger.severe('初始化本地通知插件失败: $e');
    }
  }

  // 创建通知渠道
  Future<void> _createNotificationChannels() async {
    if (Platform.isAndroid) {
      _logger.info('创建Android通知渠道');

      // 创建聊天消息通知渠道
      const AndroidNotificationChannel chatChannel = AndroidNotificationChannel(
        'chat_channel', // id
        '聊天消息', // name
        importance: Importance.high,
        description: '聊天室和私聊消息通知',
      );

      // 创建重要消息通知渠道
      const AndroidNotificationChannel importantChannel =
          AndroidNotificationChannel(
        'important_messages', // id - 与服务器发送的android_channel_id匹配
        '重要通知', // name
        importance: Importance.max,
        description: '重要系统消息和提醒',
        sound: RawResourceAndroidNotificationSound('notification_sound'),
        enableVibration: true,
      );

      // 注册通知渠道
      final androidPlugin = _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

      if (androidPlugin != null) {
        await androidPlugin.createNotificationChannel(chatChannel);
        await androidPlugin.createNotificationChannel(importantChannel);
      }

      _logger.info('Android通知渠道创建完成');
    }
  }

  // 检查 FCM 连接状态
  Future<bool> checkFcmConnection() async {
    try {
      // 尝试获取 FCM 令牌
      final token = await _firebaseMessaging.getToken();
      _currentToken = token;
      _isFcmConnected = token != null && token.isNotEmpty;

      if (_isFcmConnected) {
        _logger.info('FCM 连接正常，令牌: ${token!.substring(0, 10)}...');
      } else {
        _logger.warning('FCM 连接失败，无法获取令牌');
      }

      return _isFcmConnected;
    } catch (e) {
      _logger.severe('检查 FCM 连接失败: $e');
      _isFcmConnected = false;
      return false;
    }
  }

  // 配置 Firebase Cloud Messaging
  Future<void> _configureFirebaseMessaging() async {
    try {
      // 获取 FCM 令牌
      final token = await _firebaseMessaging.getToken();
      _currentToken = token;
      if (token != null && token.isNotEmpty) {
        _isFcmConnected = true;
        _logger.info('FCM 令牌: ${token.substring(0, 10)}...');
      } else {
        _isFcmConnected = false;
        _logger.warning('FCM 令牌为空');
      }

      // 监听令牌刷新
      _firebaseMessaging.onTokenRefresh.listen(_handleTokenRefresh);

      // 配置 FCM 消息处理
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
      FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

      // 检查应用是否是通过点击通知启动的
      final initialMessage = await _firebaseMessaging.getInitialMessage();
      if (initialMessage != null) {
        _handleInitialMessage(initialMessage);
      }
    } catch (e) {
      _isFcmConnected = false;
      _logger.severe('配置 FCM 失败: $e');
    }
  }

  // 处理令牌刷新
  void _handleTokenRefresh(String newToken) {
    _logger.info('FCM令牌已刷新: ${newToken.substring(0, 10)}...');
    // 如果是相同的令牌，则不处理
    if (_currentToken == newToken) {
      return;
    }

    _currentToken = newToken;
    _isFcmConnected = true;

    // 通过WebSocket发送令牌更新
    _updateTokenViaWebSocket(newToken);
  }

  // 处理前台消息
  void _handleForegroundMessage(RemoteMessage message) {
    _logger.info('收到前台消息: ${message.notification?.title}');

    try {
      // 将 FCM 消息转换为通知负载
      final payload = _extractPayloadFromMessage(message);
      if (payload != null) {
        _lastClickedNotificationPayload = payload;

        // 显示本地通知
        _showLocalNotification(
          title: message.notification?.title ?? 'EStockCafe',
          body: message.notification?.body ?? '',
          payload: payload,
        );
      }
    } catch (e) {
      _logger.severe('处理前台消息失败: $e');
    }
  }

  // 显示本地通知
  Future<void> _showLocalNotification({
    required String title,
    required String body,
    required Map<String, dynamic> payload,
  }) async {
    try {
      _logger.info('显示本地通知: $title - $body');

      // 获取通知渠道ID，优先使用payload中指定的渠道
      final String channelId = payload['android_channel_id'] ??
          (payload['type'] == 'system' ? 'important_messages' : 'chat_channel');

      _logger.info('使用通知渠道: $channelId');

      // 创建Android通知详情
      final AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        channelId,
        channelId == 'important_messages' ? '重要消息' : '聊天消息',
        importance: Importance.high,
        priority: Priority.high,
        channelDescription: '应用通知',
        showWhen: true,
        playSound: true, // 确保声音播放
        enableVibration: true, // 启用振动
        enableLights: true, // 启用通知灯
        // 确保通知声音正确播放
        sound: channelId == 'important_messages'
            ? const RawResourceAndroidNotificationSound(
                'notification_sound',
              )
            : null,
      );

      // 创建通知详情
      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
      );

      // 创建通知负载
      final String notificationPayload = json.encode(payload);

      // 生成唯一的通知ID
      final int notificationId = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      // 显示通知
      _logger.info('准备调用flutter_local_notifications显示通知，ID: $notificationId');
      await _flutterLocalNotificationsPlugin.show(
        notificationId,
        title,
        body,
        notificationDetails,
        payload: notificationPayload,
      );

      _logger.info('本地通知已显示成功，ID: $notificationId');

      // 保存最后点击的通知负载
      _lastClickedNotificationPayload = payload;
    } catch (e, stackTrace) {
      _logger.severe('显示本地通知失败: $e');
      _logger.severe('错误堆栈: $stackTrace');
    }
  }

  // 处理通知点击事件
  void _handleNotificationTap(Map<String, dynamic> payload) {
    _logger.info('用户点击了通知: $payload');

    // 获取消息ID，适用于所有类型通知
    final messageId = payload['messageId'];

    // 使用统一的方式导航到主页面，指定初始标签为聊天室(1)
    _navigationService.navigatorKey.currentState?.pushNamedAndRemoveUntil(
      '/',
      (route) => false,
      arguments: {'initialTab': 1, 'messageId': messageId},
    );

    // 显示调试信息
    _logger.info('已导航到主页面，初始标签: 1, 消息ID: $messageId');
  }

  // 处理通知点击事件
  void _handleMessageOpenedApp(RemoteMessage message) {
    _logger.info('用户点击了通知: ${message.notification?.title}');

    try {
      // 将 FCM 消息转换为通知负载
      final payload = _extractPayloadFromMessage(message);
      if (payload != null) {
        _lastClickedNotificationPayload = payload;

        // 获取消息ID
        final messageId = payload['messageId'];

        // 使用统一的方式导航到主页面，指定初始标签为聊天室(1)
        _navigationService.navigatorKey.currentState?.pushNamedAndRemoveUntil(
          '/',
          (route) => false,
          arguments: {'initialTab': 1, 'messageId': messageId},
        );

        _logger.info('已导航到主页面，初始标签: 1, 消息ID: $messageId');
      }
    } catch (e) {
      _logger.severe('处理通知点击事件失败: $e');
    }
  }

  // 处理初始消息
  void _handleInitialMessage(RemoteMessage message) {
    _logger.info('应用通过点击通知启动: ${message.notification?.title}');

    try {
      // 将 FCM 消息转换为通知负载
      final payload = _extractPayloadFromMessage(message);
      if (payload != null) {
        _lastClickedNotificationPayload = payload;

        // 获取消息ID
        final messageId = payload['messageId'];

        // 使用统一的方式导航到主页面，指定初始标签为聊天室(1)
        _navigationService.navigatorKey.currentState?.pushNamedAndRemoveUntil(
          '/',
          (route) => false,
          arguments: {'initialTab': 1, 'messageId': messageId},
        );

        _logger.info('已导航到主页面，初始标签: 1, 消息ID: $messageId');
      }
    } catch (e) {
      _logger.severe('处理初始消息失败: $e');
    }
  }

  // 通过WebSocket更新令牌
  Future<void> _updateTokenViaWebSocket(String token) async {
    try {
      // 获取WebSocketUtility的单例实例
      final webSocketUtility = ws.WebSocketUtility(url: AppConfig.chatWsUrl);

      // 如果WebSocket已连接，直接更新令牌
      if (webSocketUtility.isConnected) {
        webSocketUtility.setFcmToken(token);
        await webSocketUtility.registerFcmToken();
        _logger.info('通过WebSocket更新FCM令牌成功');
      } else {
        _logger.info('WebSocket未连接，仅设置令牌，下次连接时将自动注册');
        webSocketUtility.setFcmToken(token);
      }
    } catch (e) {
      _logger.warning('通过WebSocket更新FCM令牌失败: $e');
    }
  }

  // 从 FCM 消息中提取负载
  Map<String, dynamic>? _extractPayloadFromMessage(RemoteMessage message) {
    try {
      if (message.data.containsKey('message')) {
        final messageData = message.data['message'];
        Map<String, dynamic> chatMessageJson;

        if (messageData is String) {
          chatMessageJson = jsonDecode(messageData);
        } else if (messageData is Map) {
          chatMessageJson = Map<String, dynamic>.from(messageData);
        } else {
          return null;
        }

        // 创建通知负载
        return {
          'type': 'chat',
          'messageId': chatMessageJson['id'] ?? chatMessageJson['msg_id'],
          'channel_name': chatMessageJson['metadata']?['channel_name'] ??
              chatMessageJson['meta']?['channel_name'] ??
              chatMessageJson['channelName'],
          'isPrivate': chatMessageJson['toUserId'] != null,
          'toUserId': chatMessageJson['toUserId'],
        };
      } else if (message.data.containsKey('type') &&
          message.data['type'] == 'chat') {
        // 直接使用消息数据
        return Map<String, dynamic>.from(message.data);
      }

      return null;
    } catch (e) {
      _logger.severe('提取负载失败: $e');
      return null;
    }
  }

  // 请求通知权限
  Future<bool> requestPermissions() async {
    try {
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );

      return settings.authorizationStatus == AuthorizationStatus.authorized ||
          settings.authorizationStatus == AuthorizationStatus.provisional;
    } catch (e) {
      _logger.severe('请求通知权限失败: $e');
      return false;
    }
  }

  // 显示聊天消息通知
  Future<void> showChatNotification(ChatMessageData message) async {
    try {
      // 验证消息内容
      if (message.content.isEmpty) {
        _logger.warning('跳过显示空内容的通知');
        return;
      }

      // 如果是系统消息，需要特殊处理
      if (message.isSystem) {
        _logger.info('系统消息: ${message.content}');
        // 只有在内容非空且不是普通状态更新时才显示通知
        if (!_isStatusUpdateMessage(message.content)) {
          _unreadCount++;
          _logger.info('FCM 将显示系统通知: ${message.content}');

          // 显示系统通知
          await _showLocalNotification(
            title: '系统消息',
            body: message.content,
            payload: {
              'type': 'system',
              'messageId': message.id,
              'channel_name': message.channelName,
            },
          );
        }
        return;
      }

      // 增加未读消息计数
      _unreadCount++;
      _logger.info('FCM 将显示聊天通知: ${message.userName} - ${message.content}');

      // 显示聊天消息通知
      await _showLocalNotification(
        title: message.userName,
        body: message.content,
        payload: {
          'type': 'chat',
          'messageId': message.id,
          'channel_name': message.channelName,
          'roomId': message.metadata?['room'] ?? message.channelName,
        },
      );
    } catch (e) {
      _logger.severe('显示聊天通知失败: $e');
    }
  }

  // 判断是否是状态更新消息
  bool _isStatusUpdateMessage(String content) {
    // 添加需要过滤的状态更新消息关键词
    final statusKeywords = ['进入房间', '离开房间', '连接成功', '断开连接', '重新连接'];

    return statusKeywords.any((keyword) => content.contains(keyword));
  }

  // 清除所有通知
  Future<void> clearAllNotifications() async {
    try {
      _unreadCount = 0;

      _logger.info('清除所有通知');
    } catch (e) {
      _logger.severe('清除通知失败: $e');
    }
  }

  // 获取并清除最后点击的通知负载
  Map<String, dynamic>? getAndClearLastClickedNotificationPayload() {
    final payload = _lastClickedNotificationPayload;
    _lastClickedNotificationPayload = null;
    return payload;
  }

  // 获取当前FCM令牌
  String? getCurrentToken() {
    return _currentToken;
  }

  // 发送测试消息
  Future<bool> sendTestMessage() async {
    try {
      _logger.info('尝试发送FCM测试消息');

      // 检查FCM连接状态
      if (!_isFcmConnected || _currentToken == null) {
        _logger.warning('FCM未连接或令牌为空，无法发送测试消息');
        return false;
      }

      // 创建一个测试消息
      final testMessage = ChatMessageData(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        userId: 'system',
        userName: 'FCM测试',
        content: '这是一条FCM测试消息 - ${DateTime.now().toString().substring(11, 19)}',
        timestamp: DateTime.now(),
        type: 'system',
        isAdmin: false,
        channelName: 'test',
      );

      try {
        // 增加未读消息计数
        _unreadCount++;

        // 创建通知内容
        final title = 'FCM测试消息';
        final body = testMessage.content;

        // 创建通知负载 - 使用system类型和important_messages渠道
        final payload = {
          'type': 'system', // 使用system类型而非test
          'messageId': testMessage.id,
          'channel_name': testMessage.channelName,
          'time': DateTime.now().toString(),
          'android_channel_id': 'important_messages', // 显式指定重要通知渠道
        };

        // 直接调用_showLocalNotification方法显示通知
        await _showLocalNotification(
          title: title,
          body: body,
          payload: payload,
        );

        // 同时也通过_handleForegroundMessage方法处理
        _handleForegroundMessage(
          RemoteMessage(
            notification: RemoteNotification(title: title, body: body),
            data: {
              'type': 'system',
              'message': jsonEncode(testMessage.toJson()),
              'android_channel_id': 'important_messages'
            },
            messageId: testMessage.id,
          ),
        );

        _logger.info('成功发送FCM测试消息: $title - $body');
      } catch (e) {
        _logger.severe('显示本地通知失败: $e');
        return false;
      }

      return true;
    } catch (e) {
      _logger.severe('发送FCM测试消息失败: $e');
      return false;
    }
  }
}
