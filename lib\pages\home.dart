import 'package:estockcafe/models/home.dart';
import 'package:estockcafe/services/home_service.dart';
import 'package:estockcafe/theme.dart';
import 'package:estockcafe/utils/logging.dart';
import 'package:estockcafe/widgets/post/list_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:logging/logging.dart';
import 'dart:async';
import '../widgets/bottom_navbar.dart' as navbar;
import '../widgets/drawer_menu.dart' as drawer;
import '../widgets/drawer_message.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'search/search_page.dart';
import '../services/chat_notification_service.dart';
import 'package:provider/provider.dart';
import '../providers/navigation_provider.dart';

class HomeScreen extends StatefulWidget {
  final int initialTab;
  final String? messageId;

  const HomeScreen({
    super.key,
    this.initialTab = 0, // 默认显示首页标签
    this.messageId, // 可选消息ID，用于导航到特定消息
  });

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final Logger _logger = Logging.getLogger('HomeScreen');
  final HomeService _homeService = HomeService();
  List<DailyRecommendation> _dailyRecommendations = [];
  List<CarouselItem> _carouselItems = [];
  List<MenuItem> _menuItems = [];
  String _errorMessage = '';
  bool _isRefreshing = false;
  bool _disposed = false;

  @override
  void dispose() {
    _disposed = true;
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _initializeData();
    // 确保ChatNotificationService被初始化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ChatNotificationService>(context, listen: false);
    });
  }

  Future<void> _initializeData() async {
    if (_disposed) return;

    try {
      // 从缓存获取所有数据
      _dailyRecommendations = await _homeService.getCachedRecommendations();
      _carouselItems = await _homeService.fetchHomeSlider();
      _menuItems = await _homeService.fetchHomeMenu();

      if (!_disposed && mounted) {
        setState(() {});
      }

      // 如果缓存为空，立即获取数据
      if (_dailyRecommendations.isEmpty && !_disposed) {
        await _fetchDailyRecommendations(forceRefresh: false);
      }

      // 在后台静默更新数据
      if (!_disposed && mounted) {
        // 使用微任务确保在下一帧之前执行
        scheduleMicrotask(() async {
          try {
            if (_disposed) return;

            final recommendations = await _homeService
                .fetchDailyRecommendations(forceRefresh: true);

            if (!_disposed && mounted && recommendations.isNotEmpty) {
              setState(() {
                _dailyRecommendations = recommendations;
              });
            }
          } catch (e) {
            if (!_disposed) {
              _logger.warning('后台更新数据失败: $e');
            }
          }
        });
      }
    } catch (e) {
      if (!_disposed) {
        _logger.warning('初始化数据失败: $e');
      }
    }
  }

  Future<void> _fetchDailyRecommendations({bool forceRefresh = false}) async {
    if (_isRefreshing) return;

    setState(() {
      _errorMessage = '';
      _isRefreshing = true;
    });

    try {
      final recommendations = await _homeService.fetchDailyRecommendations(
          forceRefresh: forceRefresh);

      if (mounted) {
        setState(() {
          _dailyRecommendations = recommendations;
          _isRefreshing = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          if (e is Exception) {
            _errorMessage = e.toString().replaceAll('Exception: ', '');
          } else {
            _errorMessage = '获取最新文章失败，请稍后重试';
          }
          _isRefreshing = false;
        });
      }
    }
  }

  Widget _buildCarousel() {
    if (_carouselItems.isEmpty) {
      return const SizedBox.shrink();
    }

    return CarouselSlider(
      options: CarouselOptions(
        height: 180.0,
        aspectRatio: 16 / 9,
        viewportFraction: 0.8,
        initialPage: 0,
        enableInfiniteScroll: true,
        reverse: false,
        autoPlay: true,
        autoPlayInterval: const Duration(seconds: 3),
        autoPlayAnimationDuration: const Duration(milliseconds: 800),
        autoPlayCurve: Curves.fastOutSlowIn,
        enlargeCenterPage: true,
        scrollDirection: Axis.horizontal,
      ),
      items: _carouselItems.map((item) {
        return GestureDetector(
          onTap: () {
            // 设置导航状态，标记为从首页链接进入
            Provider.of<NavigationProvider>(context, listen: false)
                .setFromHomePage(true);
            Navigator.pushNamed(context, item.route);
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 5.0),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.0),
              child: Image.network(
                item.pic,
                fit: BoxFit.cover,
                width: double.infinity,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[200],
                    child: const Center(
                      child: Icon(Icons.error_outline, color: Colors.grey),
                    ),
                  );
                },
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSearchBar() {
    return GestureDetector(
      onTap: () {
        // 设置导航状态，标记为从首页链接进入
        Provider.of<NavigationProvider>(context, listen: false)
            .setFromHomePage(true);
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const SearchPage()),
        );
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24.0),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Icon(Icons.search, color: AppColors.primaryBlue),
            const SizedBox(width: 12.0),
            Text(
              '搜索感兴趣的内容',
              style: TextStyle(color: Colors.grey[500], fontSize: 14),
            ),
            const Spacer(),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuGrid() {
    if (_menuItems.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16.0),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          childAspectRatio: 1.0,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: _menuItems.length,
        itemBuilder: (context, index) {
          final item = _menuItems[index];
          return GestureDetector(
            onTap: () {
              // 设置导航状态，标记为从首页链接进入
              Provider.of<NavigationProvider>(context, listen: false)
                  .setFromHomePage(true);
              Navigator.pushNamed(context, item.route);
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(12.0),
                  decoration: BoxDecoration(
                    color: AppColors.secondaryLightBlue,
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: item.icon.isNotEmpty
                      ? SvgPicture.network(
                          item.icon,
                          width: 24,
                          height: 24,
                          color: AppColors.primaryBlue,
                        )
                      : const Icon(
                          Icons.apps,
                          size: 24,
                          color: AppColors.primaryBlue,
                        ),
                ),
                const SizedBox(height: 4),
                Text(
                  item.title,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('EStockCafe'),
        centerTitle: true,
        titleTextStyle: const TextStyle(
          color: AppColors.primaryBlue,
          fontSize: AppFontSizes.fontSizeExtraLarge,
          fontWeight: FontWeight.bold,
        ),
        leading: Builder(
          builder: (BuildContext context) {
            return IconButton(
              icon: const Icon(Icons.menu),
              onPressed: () {
                Scaffold.of(context).openDrawer();
              },
            );
          },
        ),
        actions: [
          /* // 添加聊天通知下拉组件
          const ChatNotificationDropdown(),
          // 添加设置按钮
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsPage()),
              );
            },
          ), */
          Builder(
            builder: (BuildContext context) {
              return IconButton(
                icon: const Icon(Icons.message),
                onPressed: () {
                  Scaffold.of(context).openEndDrawer();
                },
              );
            },
          ),
          // 通知图标和调试菜单已移除
        ],
      ),
      drawer: const drawer.DrawerMenu(),
      endDrawer: const DrawerMessage(),
      body: RefreshIndicator(
        onRefresh: () => _fetchDailyRecommendations(forceRefresh: true),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildCarousel(),
              _buildSearchBar(),
              _buildMenuGrid(),
              if (_errorMessage.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    _errorMessage,
                    style: const TextStyle(
                      color: Colors.red,
                      fontSize: 14,
                    ),
                  ),
                ),
              Container(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Text(
                      '最新文章',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (_dailyRecommendations.isEmpty && _errorMessage.isEmpty)
                      const Center(
                        child: Text(
                          '暂无数据',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 16,
                          ),
                        ),
                      )
                    else
                      PostListView(
                        articles: _dailyRecommendations,
                        hasNext: false,
                        cacheWidth: 300,
                        cacheHeight: 200,
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: navbar.BottomNavBar(
        currentIndex: widget.initialTab,
        messageId: widget.messageId,
      ),
    );
  }
}
