import 'dart:async';
import 'package:estockcafe/pages/live/live_check.dart';
import 'package:estockcafe/pages/user/center.dart';
import 'package:estockcafe/utils/logging.dart';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../pages/home.dart';
import '../pages/chat/chat_room.dart';
import '../pages/chat/chat_check.dart';
import '../pages/vip.dart';
import '../pages/live/live_room.dart';
import '../pages/live/live_unauthorized.dart';
import '../pages/live/live_purchase.dart';
import '../pages/user/profile.dart';
import '../pages/posts/category.dart';
import '../pages/posts/post.dart';
import '../pages/user/login.dart';
import '../pages/user/register.dart';
import '../pages/user/purchase_records.dart';
import '../pages/user/vip_records.dart';
import '../pages/user/live_records.dart';
import '../pages/user/recharge_records.dart';
import '../pages/user/edit_profile.dart';
import '../pages/user/change_password.dart';
import '../pages/user/forgot_password.dart';
import '../pages/user/reset_password.dart';
import '../pages/settings/notification_settings_page.dart';
import '../pages/debug/fcm_test_page.dart';
import '../pages/debug/log_viewer.dart';

import '../pages/debug/debug_menu.dart';
import '../pages/debug/network_diagnostic.dart';
import 'route_guards.dart';
import '../pages/pay/recharge.dart';
import '../pages/chatroom_page.dart' as fcm_chat;

class AppRouter {
  static final Logger _logger = Logging.getLogger('AppRouter');
  static final Map<String, Widget> _pageCache = {};

  static Route<dynamic>? generateRoute(RouteSettings settings) {
    final uri = Uri.parse(settings.name!);
    final args = settings.arguments;

    // 记录路由请求，但不要记录详细信息（避免与_getPageForPath重复）
    _logger.info('处理路由请求: ${uri.path}');

    return MaterialPageRoute(
      builder: (context) => FutureBuilder<String?>(
        future: _handleRedirect(context, uri.path),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Scaffold(
              body: Center(
                  child: SpinKitPulsingGrid(
                size: 20,
                color: Colors.grey,
              )),
            );
          }
          if (snapshot.hasError) {
            _logger.severe('路由重定向错误: ${snapshot.error}');
            return const NotFoundPage();
          }
          final redirectPath = snapshot.data;
          if (redirectPath != null && redirectPath != uri.path) {
            _logger.info('重定向到: $redirectPath');
            return _getPageForPath(redirectPath, args, context);
          } else {
            return _getPageForPath(uri.path, args, context);
          }
        },
      ),
      settings: settings,
    );
  }

  static Future<String?> _handleRedirect(
      BuildContext context, String path) async {
    try {
      return await RouteGuards.redirectPath(context, path);
    } catch (e) {
      _logger.severe('处理重定向时发生错误: $e');
      return '/error'; // 添加一个错误页面路由
    }
  }

  static Widget _getPageForPath(
      String path, dynamic args, BuildContext context) {
    // 检查是否需要清除缓存（例如，在用户登录或注销后）
    if (path == '/user/login' || path == '/home') {
      _pageCache.clear();
    }

    if (_pageCache.containsKey(path)) {
      _logger.fine('从缓存中获取页面: $path');
      return _pageCache[path]!;
    }

    final uri = Uri.parse(path);
    final pathSegments = uri.pathSegments;

    Widget page;

    if (pathSegments.isEmpty) {
      // 对于根路径'/'，检查是否有initialTab参数
      if (args is Map<String, dynamic>) {
        final initialTab = args['initialTab'] as int? ?? 0;
        final messageId = args['messageId'] as String?;
        page = HomeScreen(initialTab: initialTab, messageId: messageId);
      } else {
        page = const HomeScreen();
      }
    } else {
      switch (pathSegments[0]) {
        case 'home':
          // 对于'/home'路径，检查是否有initialTab参数
          if (args is Map<String, dynamic>) {
            final initialTab = args['initialTab'] as int? ?? 0;
            final messageId = args['messageId'] as String?;
            page = HomeScreen(initialTab: initialTab, messageId: messageId);
          } else {
            page = const HomeScreen();
          }
          break;
        case 'chat':
          // 处理聊天相关页面
          if (pathSegments.length > 1 && pathSegments[1] == 'check') {
            // 检查是否有messageId参数
            if (args is Map<String, dynamic> && args.containsKey('messageId')) {
              final messageId = args['messageId'] as String?;
              page = ChatCheckPage(messageId: messageId);
            } else {
              page = const ChatCheckPage();
            }
          } else {
            // 检查是否有messageId参数
            if (args is Map<String, dynamic> && args.containsKey('messageId')) {
              final messageId = args['messageId'] as String?;
              page = ChatRoomPage(messageId: messageId);
            } else {
              page = const ChatRoomPage();
            }
          }
          break;
        case 'chatroom':
          // 废弃的FCM推送方案聊天室路由，重定向到主页面的聊天标签
          // 所有通知现在应该使用带底部导航栏的主页面
          _logger.warning('尝试访问废弃的chatroom路由，重定向到主页面');
          if (args is Map<String, dynamic>) {
            final messageId = args['messageId'] as String?;
            page = HomeScreen(initialTab: 1, messageId: messageId);
          } else if (pathSegments.length > 1) {
            // 兼容旧版路由格式
            page = HomeScreen(initialTab: 1);
          } else {
            page = HomeScreen(initialTab: 1);
          }
          break;
        case 'debug':
          // 处理调试页面
          if (pathSegments.length > 1) {
            switch (pathSegments[1]) {
              case 'menu':
                page = const DebugMenuPage();
                break;
              case 'fcm_test':
                page = const FcmTestPage();
                break;
              case 'log_viewer':
                page = const LogViewerPage();
                break;
              case 'player_debug':
                // PlayerDebugPage已删除，返回404页面
                page = const NotFoundPage();
                break;
              case 'network_diagnostic':
                page = const NetworkDiagnosticPage();
                break;
              default:
                page = const NotFoundPage();
            }
          } else {
            page = const NotFoundPage();
          }
          break;
        case 'vip':
          page = const VipPage();
          break;
        case 'live':
          // 处理直播相关页面
          if (pathSegments.length > 1 && pathSegments[1] == 'unauthorized') {
            page = const LiveUnauthorizedPage();
          } else if (pathSegments.length > 1 && pathSegments[1] == 'check') {
            page = const LiveCheckPage();
          } else if (pathSegments.length > 1 && pathSegments[1] == 'purchase') {
            page = const LivePurchasePage();
          } else {
            page = const LiveRoomPage();
          }
          break;
        case 'search':
          page = const Placeholder(child: Text('搜索页面'));
          break;
        case 'category':
          // 处理分类页面
          if (pathSegments.length == 2) {
            page = CategoryPage(id: pathSegments[1]);
          } else {
            page = const NotFoundPage();
          }
          break;
        case 'post':
          // 处理文章页面
          if (pathSegments.length == 2) {
            page = PostPage(articleId: pathSegments[1]);
          } else {
            page = const NotFoundPage();
          }
          break;
        case 'page':
          // 处理通用页面
          if (pathSegments.length == 2) {
            page = Placeholder(child: Text('${pathSegments[1]}页面'));
          } else {
            page = const NotFoundPage();
          }
          break;
        case 'user':
          // 处理用户相关页面
          if (pathSegments.length == 2) {
            switch (pathSegments[1]) {
              case 'login':
                page = const LoginPage();
                break;
              case 'register':
                page = const RegisterPage();
                break;
              case 'forgot-password':
                page = const ForgotPasswordPage();
                break;
              case 'reset-password':
                // 从查询参数中获取token
                final token = uri.queryParameters['token'];
                page = ResetPasswordPage(token: token);
                break;
              case 'profile':
                page = const ProfilePage();
                break;
              case 'center':
                page = const UserCenterPage();
                break;
              case 'purchase_records':
                page = const PurchaseRecordsPage();
                break;
              case 'vip_records':
                page = const VipRecordsPage();
                break;
              case 'live_records':
                page = const LiveRecordsPage();
                break;
              case 'recharge_records':
                page = const RechargeRecordsPage();
                break;
              case 'edit_profile':
                page = const EditProfilePage();
                break;
              case 'change_password':
                page = const ChangePasswordPage();
                break;
              default:
                page = const NotFoundPage();
            }
          } else {
            page = const NotFoundPage();
          }
          break;
        case 'settings':
          // 处理设置相关页面
          if (pathSegments.length > 1 && pathSegments[1] == 'notifications') {
            page = const NotificationSettingsPage();
          } else {
            page = const NotFoundPage();
          }
          break;
        case 'pay':
          // 处理支付相关页面
          if (pathSegments.length > 1 && pathSegments[1] == 'recharge') {
            page = const RechargePage();
          } else {
            page = const NotFoundPage();
          }
          break;
        default:
          // 处理WordPress页面和其他未匹配的路径
          // 如果路径看起来像WordPress页面，使用WebView显示
          if (_isWordPressPath(path)) {
            page = WordPressWebViewPage(path: path);
          } else {
            page = const NotFoundPage();
          }
      }
    }

    // 只在创建新页面时记录日志，避免重复
    _logger.fine('创建新页面: $path');
    _pageCache[path] = page;
    return page;
  }

  // 判断是否为WordPress页面路径
  static bool _isWordPressPath(String path) {
    // WordPress常见路径模式
    final wordpressPatterns = [
      RegExp(r'^/[a-zA-Z0-9_-]+/\d+\.html$'), // 如 /xlkc/4437.html
      RegExp(r'^/seriesindicators/\d+\.html$'), // 如 /seriesindicators/4888.html
      RegExp(r'^/[a-zA-Z0-9_-]+/?$'), // 如 /sma
      RegExp(r'^/[a-zA-Z0-9_-]+/[a-zA-Z0-9_-]+/?$'), // 两级路径
    ];

    return wordpressPatterns.any((pattern) => pattern.hasMatch(path));
  }
}

class NotFoundPage extends StatelessWidget {
  const NotFoundPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('页面未找到')),
      body: const Center(child: Text('请求的页面不存在')),
    );
  }
}

class WordPressWebViewPage extends StatefulWidget {
  final String path;

  const WordPressWebViewPage({super.key, required this.path});

  @override
  State<WordPressWebViewPage> createState() => _WordPressWebViewPageState();
}

class _WordPressWebViewPageState extends State<WordPressWebViewPage> {
  late final WebViewController _controller;
  bool _isLoading = true;
  String _title = '加载中...';

  @override
  void initState() {
    super.initState();

    final String fullUrl = 'https://max.estockcafe.com${widget.path}';

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
            // 获取页面标题
            _controller.getTitle().then((title) {
              if (title != null && mounted) {
                setState(() {
                  _title = title;
                });
              }
            });
          },
          onWebResourceError: (WebResourceError error) {
            setState(() {
              _isLoading = false;
              _title = '加载失败';
            });
          },
        ),
      )
      ..loadRequest(Uri.parse(fullUrl));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_title),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0.5,
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading)
            const Center(
              child: SpinKitPulsingGrid(
                size: 40,
                color: Colors.blue,
              ),
            ),
        ],
      ),
    );
  }
}
