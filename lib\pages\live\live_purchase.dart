import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../theme.dart';
import '../../widgets/bottom_navbar.dart';
import '../../services/user_service.dart';
import '../../widgets/payment_widget.dart';
import 'package:logging/logging.dart';

class LivePurchasePage extends StatefulWidget {
  const LivePurchasePage({super.key});

  @override
  State<LivePurchasePage> createState() => _LivePurchasePageState();
}

class _LivePurchasePageState extends State<LivePurchasePage> {
  final _logger = Logger('LivePurchasePage');
  final TextEditingController _countController = TextEditingController();
  int _selectedCount = 0;
  int? _selectedPaymentMethod;
  final UserService _userService = UserService();
  List<Map<String, dynamic>> _paymentMethods = [];
  bool _isLoading = false;
  final String _paymentInfo = '';
  final bool _showPaymentInfo = false;
  int _originalLiveCount = 0; // 保存购买前的次数

  @override
  void initState() {
    super.initState();
    _loadPaymentMethods();
    _countController.text = '1';
    _selectedCount = 1;
  }

  Future<void> _loadPaymentMethods() async {
    setState(() {
      _isLoading = true;
    });
    try {
      final data = await _userService.loadMembershipData();
      setState(() {
        _paymentMethods = data['paymentMethods'] ?? [];
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载支付方式失败：$e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _countController.dispose();
    super.dispose();
  }

  void _selectCount(int count) {
    setState(() {
      _selectedCount = count;
      _countController.text = count.toString();
    });
  }

  void _handlePaymentComplete(bool success,
      {String? paymentType, Map<String, dynamic>? paymentResult}) {
    _logger.info(
        '_handlePaymentComplete called: success=$success, paymentType=$paymentType');
    _logger.info('paymentResult: $paymentResult');

    if (success) {
      // 支付成功，直接显示成功弹窗
      if (mounted) {
        _logger.info('Calling _showSuccessDialogWithUpdatedCount');
        _showSuccessDialogWithUpdatedCount(paymentResult);
      } else {
        _logger.warning('Widget not mounted, cannot show dialog');
      }
    } else {
      _logger.warning('Payment failed, not showing success dialog');
    }
  }

  Future<void> _showSuccessDialogWithUpdatedCount(
      Map<String, dynamic>? paymentResult) async {
    _logger.info('_showSuccessDialogWithUpdatedCount called');

    // 先刷新用户信息
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    _logger.info('Refreshing user info...');
    await authProvider.refreshUserInfo();

    // 添加短暂延迟确保数据完全同步
    await Future.delayed(const Duration(milliseconds: 500));

    if (!mounted) {
      _logger.warning('Widget not mounted after refresh, returning');
      return;
    }

    // 优先使用后端返回的成功消息
    final successMessage = paymentResult?['success_message'] ??
        paymentResult?['message'] ??
        paymentResult?['description'] ??
        '购买成功！';

    final user = authProvider.user;
    final currentCount = user?.assets['live'] as int? ?? 0;

    // 从支付结果中获取购买的次数
    final purchasedCount = paymentResult?['live_num'] as int? ?? _selectedCount;

    // 智能计算新的总次数
    int newCount;
    if (currentCount > _originalLiveCount) {
      // 如果当前次数大于原始次数，说明后端已经更新了，使用当前次数
      newCount = currentCount;
      _logger.info('Using updated count from backend: $newCount');
    } else {
      // 如果当前次数没有变化，使用原始次数 + 购买次数
      newCount = _originalLiveCount + purchasedCount;
      _logger.info(
          'Calculating count: $_originalLiveCount + $purchasedCount = $newCount');
    }

    _logger.info('Success message: $successMessage');
    _logger.info('User data: ${user?.toJson()}');
    _logger.info('User assets: ${user?.assets}');
    _logger.info('Current count from assets: $currentCount');
    _logger.info('Purchased count: $purchasedCount');
    _logger.info('Final display count: $newCount');
    _logger.info('About to show dialog...');

    // 显示科技感弹窗
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        _logger.info('Dialog builder called');
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            width: MediaQuery.of(context).size.width * 0.85,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF1A1A2E),
                  Color(0xFF16213E),
                  Color(0xFF0F3460),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.cyan.withValues(alpha: 0.3),
                  blurRadius: 20,
                  spreadRadius: 2,
                ),
                BoxShadow(
                  color: Colors.blue.withValues(alpha: 0.2),
                  blurRadius: 40,
                  spreadRadius: 5,
                ),
              ],
              border: Border.all(
                color: Colors.cyan.withValues(alpha: 0.5),
                width: 1,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 成功图标动画区域
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          Colors.green.withValues(alpha: 0.8),
                          Colors.green.withValues(alpha: 0.3),
                        ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.green.withValues(alpha: 0.5),
                          blurRadius: 20,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.check_circle_outline,
                      color: Colors.white,
                      size: 40,
                    ),
                  ),
                  const SizedBox(height: 20),

                  // 标题
                  ShaderMask(
                    shaderCallback: (bounds) => const LinearGradient(
                      colors: [Colors.cyan, Colors.blue, Colors.purple],
                    ).createShader(bounds),
                    child: const Text(
                      '支付成功',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 成功消息
                  Text(
                    successMessage,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),

                  // 次数显示卡片
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.cyan.withValues(alpha: 0.2),
                          Colors.blue.withValues(alpha: 0.1),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.cyan.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.cyan.withValues(alpha: 0.2),
                          ),
                          child: const Icon(
                            Icons.live_tv,
                            color: Colors.cyan,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              '当前可用次数',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.white60,
                              ),
                            ),
                            const SizedBox(height: 4),
                            ShaderMask(
                              shaderCallback: (bounds) => const LinearGradient(
                                colors: [Colors.cyan, Colors.blue],
                              ).createShader(bounds),
                              child: Text(
                                '$newCount 次',
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 32),

                  // 按钮区域
                  Row(
                    children: [
                      // 取消按钮
                      Expanded(
                        child: Container(
                          height: 48,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24),
                            border: Border.all(
                              color: Colors.white30,
                              width: 1,
                            ),
                          ),
                          child: TextButton(
                            onPressed: () {
                              Navigator.of(context).pop(); // 只关闭弹窗，停留在当前页面
                            },
                            style: TextButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(24),
                              ),
                            ),
                            child: const Text(
                              '继续购买',
                              style: TextStyle(
                                color: Colors.white70,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),

                      // 确定按钮
                      Expanded(
                        child: Container(
                          height: 48,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Colors.cyan, Colors.blue],
                            ),
                            borderRadius: BorderRadius.circular(24),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.cyan.withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: TextButton(
                            onPressed: () {
                              Navigator.of(context).pop(); // 关闭弹窗
                              // 跳转到直播记录页面
                              Navigator.pushReplacementNamed(
                                  context, 'user/live_records');
                            },
                            style: TextButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(24),
                              ),
                            ),
                            child: const Text(
                              '查看记录',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
    _logger.info('Dialog completed');
  }

  void _handlePaymentMethodChange(int methodId) {
    setState(() {
      _selectedPaymentMethod = methodId;
    });
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final user = authProvider.user;
    final currentCount = user?.assets['live'] as int? ?? 0;

    // 初始化原始次数（只在第一次或次数变化时更新）
    if (_originalLiveCount == 0 || _originalLiveCount != currentCount) {
      _originalLiveCount = currentCount;
    }

    final price = (user?.live['price'] is String)
        ? double.tryParse(user?.live['price'] as String? ?? '0') ?? 0.0
        : (user?.live['price'] as num?)?.toDouble() ?? 0.0;
    final totalPrice = _selectedCount * price;

    return Scaffold(
      appBar: AppBar(
        title: const Text('购买直播次数'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5DC),
                border: Border.all(color: const Color(0xFFFFF2CC)),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Row(
                children: [
                  const Text(
                    '当前可用次数:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '$currentCount 次',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              '购买次数',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _countController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: '请输入购买次数',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: Colors.grey[300]!,
                          width: 0.5,
                        ),
                      ),
                      isDense: true,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      suffixIcon: IconButton(
                        icon: const Icon(
                          Icons.clear,
                          size: 18,
                        ),
                        onPressed: () {
                          _countController.clear();
                          setState(() {
                            _selectedCount = 0;
                          });
                        },
                      ),
                    ),
                    style: const TextStyle(
                      fontSize: 14,
                    ),
                    onChanged: (value) {
                      setState(() {
                        _selectedCount = int.tryParse(value) ?? 0;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Wrap(
                  spacing: 4,
                  runSpacing: 4,
                  children: [5, 10, 20].map((count) {
                    return ElevatedButton(
                      onPressed: () => _selectCount(count),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _selectedCount == count
                            ? AppColors.primaryBlue
                            : Colors.grey[200],
                        foregroundColor: _selectedCount == count
                            ? Colors.white
                            : Colors.black,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                      ),
                      child: Text('$count次'),
                    );
                  }).toList(),
                ),
              ],
            ),
            if (_selectedCount > 0) ...[
              const SizedBox(height: 24),
              const Text(
                '支付方式',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              if (_showPaymentInfo)
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(_paymentInfo),
                ),
              PaymentWidget(
                paymentMethods: _paymentMethods,
                paymentParams: {
                  'userType': 100, // 购买直播次数
                  'price': totalPrice,
                  'live_num': _selectedCount, // 改为live_num与后端统一
                  'type': 'live_number', // 标识这是直播购买
                  'payment_method': _selectedPaymentMethod, //
                },
                onPaymentComplete: _handlePaymentComplete,
                onPaymentMethodChange: _handlePaymentMethodChange, //
              ),
            ],
          ],
        ),
      ),
      bottomNavigationBar: const BottomNavBar(currentIndex: 4),
    );
  }
}
