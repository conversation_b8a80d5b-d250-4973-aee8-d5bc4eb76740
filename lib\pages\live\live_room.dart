import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import '../../utils/logging.dart';
import '../../utils/wakelock_helper.dart';
import '../../widgets/live/super_live_player.dart'; // 使用SuperPlayer直播播放器
import '../../widgets/bottom_navbar.dart';
import 'live_chat_room.dart'; // 导入聊天室组件
import 'package:flutter/services.dart'; // 导入SystemChrome
import 'dart:async'; // 导入Timer

class LiveRoomPage extends StatefulWidget {
  final String? liveUrl;
  final String? channelName;

  const LiveRoomPage({
    super.key,
    this.liveUrl,
    this.channelName,
  });

  @override
  State<LiveRoomPage> createState() => _LiveRoomPageState();
}

class _LiveRoomPageState extends State<LiveRoomPage> {
  final Logger _logger = Logging.getLogger('LiveRoomPage');
  bool _isRefreshing = false; // 控制刷新状态
  GlobalKey<State> _normalPlayerKey = GlobalKey(); // 正常模式播放器key

  @override
  void initState() {
    super.initState();
    _logger.info('LiveRoomPage: 构造函数被调用');
    _logger.info('LiveRoomPage: channelName = ${widget.channelName}');
    _logger.info('LiveRoomPage: liveUrl = ${widget.liveUrl}');

    // 使用WebRTC直播播放器
    if (widget.liveUrl != null && widget.liveUrl!.startsWith('webrtc://')) {
      _logger.info('检测到WebRTC协议，使用WebRTC直播播放器');
    } else {
      _logger.info('使用WebRTC直播播放器处理URL: ${widget.liveUrl}');
    }
  }

  /// 处理全屏模式变化
  void _handleFullscreenChange() {
    _logger.info('请求全屏播放');

    // 在全屏切换前，通知当前播放器准备过渡
    _logger.info('通知播放器准备全屏过渡');

    Navigator.of(context)
        .push(
      PageRouteBuilder(
        opaque: true, // 改为true，确保完全覆盖
        barrierColor: Colors.black,
        // 增加构建器优先级，确保过渡期间不会显示底层内容
        settings: const RouteSettings(name: '/fullscreen_player'),
        pageBuilder: (context, animation, secondaryAnimation) {
          return FullScreenPlayerPage(
            liveUrl: widget.liveUrl,
            onExit: () {
              Navigator.of(context).pop();
            },
          );
        },
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          // 使用更平滑的过渡动画组合
          return AnimatedBuilder(
            animation: animation,
            builder: (context, child) {
              // 创建黑色背景保护层
              return Container(
                color: Colors.black,
                child: FadeTransition(
                  opacity: Tween<double>(
                    begin: 0.0,
                    end: 1.0,
                  ).animate(CurvedAnimation(
                    parent: animation,
                    curve: Curves.easeOutCubic, // 使用更平滑的曲线
                  )),
                  child: child,
                ),
              );
            },
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 400), // 增加进入时间
        reverseTransitionDuration:
            const Duration(milliseconds: 600), // 增加退出时间，给屏幕方向切换更多时间
      ),
    )
        .then((_) async {
      // 全屏页面关闭后的回调处理
      _logger.info('全屏播放器页面已关闭，开始恢复正常模式');

      // 关键修复：立即强制恢复屏幕方向
      try {
        _logger.info('强制恢复竖屏方向');
        await SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
        ]);
        await SystemChrome.setEnabledSystemUIMode(
          SystemUiMode.edgeToEdge,
          overlays: SystemUiOverlay.values,
        );
        _logger.info('屏幕方向和系统UI强制恢复完成');
      } catch (e) {
        _logger.severe('强制恢复屏幕方向失败: $e');
      }

      // 给足够的时间让屏幕方向切换和系统UI恢复完成
      Future.delayed(const Duration(milliseconds: 1500), () {
        if (mounted) {
          _logger.info('开始重建正常模式播放器');
          try {
            // 重建播放器以确保正确的渲染状态
            setState(() {
              _normalPlayerKey = GlobalKey();
            });
            _logger.info('正常模式播放器重建完成');
          } catch (e) {
            _logger.severe('重建播放器失败: $e');
          }
        } else {
          _logger.warning('页面已销毁，跳过播放器重建');
        }
      });
    }).catchError((error) {
      _logger.severe('全屏页面关闭时发生错误: $error');
    });
  }

  @override
  void dispose() {
    // 在页面销毁时恢复屏幕方向
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    super.dispose();
  }

  /// 刷新直播流
  Future<void> _refreshLiveStream() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    _logger.info('用户请求刷新直播流');

    try {
      // 重建播放器组件来重新加载直播流
      setState(() {
        _normalPlayerKey = GlobalKey();
      });

      // 给一个适当的延迟让播放器重新初始化
      await Future.delayed(const Duration(milliseconds: 800));

      _logger.info('直播流刷新完成');

      // 显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('直播流已刷新'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      _logger.severe('刷新直播流时发生错误: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('刷新失败: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  /// 构建播放器组件
  Widget _buildPlayer() {
    return SuperLivePlayer(
      key: _normalPlayerKey,
      liveUrl: widget.liveUrl,
      onError: () {
        _logger.warning('SuperLivePlayer播放器发生错误');
      },
      onLoaded: () {
        _logger.info('SuperLivePlayer播放器加载成功');
      },
      onFullscreenChanged: _handleFullscreenChange,
    );
  }

  @override
  Widget build(BuildContext context) {
    _logger.info('LiveRoomPage: build 方法被调用');

    // 简化的布局，不再使用Stack
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '直播室',
          textAlign: TextAlign.center,
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0.5,
      ),
      body: _buildPlayerWithChatLayoutWithRefresh(),
      bottomNavigationBar: const BottomNavBar(currentIndex: 3),
    );
  }

  /// 构建带下拉刷新的播放器+聊天室布局
  Widget _buildPlayerWithChatLayoutWithRefresh() {
    return RefreshIndicator(
      onRefresh: _refreshLiveStream,
      color: Colors.blue,
      backgroundColor: Colors.white,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: constraints.maxHeight,
              ),
              child: _buildPlayerWithChatLayout(),
            ),
          );
        },
      ),
    );
  }

  /// 构建播放器+聊天室布局
  Widget _buildPlayerWithChatLayout() {
    final screenHeight = MediaQuery.of(context).size.height -
        AppBar().preferredSize.height -
        MediaQuery.of(context).padding.top -
        kBottomNavigationBarHeight;

    // 计算16:9比例的播放器高度
    final screenWidth = MediaQuery.of(context).size.width;
    final idealPlayerHeight = screenWidth * 9 / 16; // 16:9比例

    // 关键修复：确保播放器高度不超过可用空间的70%，为聊天室预留至少30%空间
    final maxPlayerHeight = screenHeight * 0.7;
    final playerHeight = idealPlayerHeight > maxPlayerHeight
        ? maxPlayerHeight
        : idealPlayerHeight;

    // 计算聊天室高度，需要减去底部安全区域
    final bottomSafeArea = MediaQuery.of(context).padding.bottom;
    final chatHeight = screenHeight - playerHeight - bottomSafeArea;

    // 使用Column布局，避免CustomScrollView的内在尺寸问题
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 播放器区域 - 使用16:9比例
        Container(
          width: double.infinity,
          height: playerHeight,
          color: Colors.black,
          child: _buildPlayer(),
        ),
        // 聊天室区域 - 使用固定高度确保占满剩余空间
        SizedBox(
          height: chatHeight,
          child: widget.channelName != null
              ? LiveChatRoom(channelName: widget.channelName!)
              : Container(
                  color: Colors.grey[200],
                  child: const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.orange,
                          size: 32,
                        ),
                        SizedBox(height: 8),
                        Text(
                          '聊天室频道未设置',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
        ),
      ],
    );
  }
}

/// 全屏播放器页面
class FullScreenPlayerPage extends StatefulWidget {
  final String? liveUrl;
  final VoidCallback? onExit;

  const FullScreenPlayerPage({
    super.key,
    this.liveUrl,
    this.onExit,
  });

  @override
  State<FullScreenPlayerPage> createState() => _FullScreenPlayerPageState();
}

class _FullScreenPlayerPageState extends State<FullScreenPlayerPage> {
  final Logger _logger = Logging.getLogger('FullScreenPlayerPage');
  bool _isExiting = false; // 新增：标记是否正在退出全屏
  bool _isInitializing = true; // 新增：标记是否正在初始化
  Timer? _initializationTimer;

  @override
  void initState() {
    super.initState();
    _initFullscreen();
  }

  /// 确保屏幕常亮启用
  Future<void> _ensureWakelockEnabled() async {
    try {
      await WakelockHelper.enable('直播全屏模式');
      _logger.info('全屏模式屏幕常亮已启用');
    } catch (e) {
      _logger.warning('全屏模式启用屏幕常亮失败: $e');
    }
  }

  /// 禁用屏幕常亮
  Future<void> _disableWakelock() async {
    try {
      await WakelockHelper.disable('退出直播全屏模式');
      _logger.info('全屏模式屏幕常亮已禁用');
    } catch (e) {
      _logger.warning('全屏模式禁用屏幕常亮失败: $e');
    }
  }

  /// 初始化全屏模式
  void _initFullscreen() async {
    try {
      _logger.info('开始初始化全屏模式');

      // 首先显示黑屏保护，防止过渡期间的闪烁
      setState(() {
        _isInitializing = true;
      });

      // 第一步：等待当前帧渲染完成，避免视图冲突
      await Future.delayed(const Duration(milliseconds: 200));

      // 第二步：切换到横屏方向（先设置方向，再隐藏UI）
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);

      // 第三步：等待屏幕方向切换完成
      await Future.delayed(const Duration(milliseconds: 800));

      // 第四步：隐藏系统UI（在方向切换完成后）
      await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

      // 第五步：额外等待确保所有渲染完成
      await Future.delayed(const Duration(milliseconds: 300));

      // 第六步：完成初始化，显示播放器
      if (mounted) {
        setState(() {
          _isInitializing = false;
        });
        _logger.info('全屏模式初始化完成');
        // 确保全屏状态下屏幕常亮
        await _ensureWakelockEnabled();
      }
    } catch (e) {
      _logger.severe('全屏初始化失败: $e');
      if (mounted) {
        setState(() {
          _isInitializing = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _logger.info('FullScreenPlayerPage开始销毁');

    // 取消定时器
    _initializationTimer?.cancel();

    // 异步执行退出全屏，避免阻塞dispose
    _exitFullscreen().catchError((error) {
      _logger.severe('dispose中退出全屏失败: $error');
    });

    _logger.info('FullScreenPlayerPage销毁完成');
    super.dispose();
  }

  /// 退出全屏模式
  Future<void> _exitFullscreen() async {
    if (_isExiting) return; // 防止重复调用

    try {
      _logger.info('开始退出全屏模式');

      // 设置退出状态，显示黑色覆盖层
      if (mounted) {
        setState(() {
          _isExiting = true;
        });
      }

      // 第一步：给覆盖层时间显示
      await Future.delayed(const Duration(milliseconds: 200));

      // 第二步：先恢复系统UI（在方向切换前）
      await SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.edgeToEdge,
        overlays: SystemUiOverlay.values,
      );
      _logger.info('系统UI已恢复');

      // 第三步：等待系统UI恢复完成
      await Future.delayed(const Duration(milliseconds: 300));

      // 第四步：强制恢复竖屏方向
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown, // 添加备选方向
      ]);
      _logger.info('屏幕方向设置为竖屏');

      // 第五步：等待屏幕方向切换完成（增加更长的延迟）
      await Future.delayed(const Duration(milliseconds: 1200));

      // 第六步：再次确保竖屏方向（双重保险）
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
      ]);
      _logger.info('再次确认竖屏方向');

      // 退出全屏时禁用屏幕常亮
      await _disableWakelock();

      _logger.info('全屏模式退出完成');
    } catch (e) {
      _logger.severe('退出全屏失败: $e');
    }
  }

  /// 处理播放器全屏切换请求（用户点击全屏按钮）
  void _handlePlayerFullscreenToggle() async {
    _logger.info('播放器请求退出全屏');

    // 设置退出状态，显示黑色覆盖层
    if (mounted) {
      setState(() {
        _isExiting = true;
      });
    }

    // 关键修复：先停止播放器，避免资源冲突
    try {
      _logger.info('停止全屏播放器以避免资源冲突');
      // 这里不需要手动停止，让dispose自然处理

      // 等待覆盖层显示
      await Future.delayed(const Duration(milliseconds: 300));

      // 开始退出流程
      await _exitFullscreen();

      // 等待退出完成后再关闭页面
      await Future.delayed(const Duration(milliseconds: 200));

      // 调用退出回调
      widget.onExit?.call();
    } catch (e) {
      _logger.severe('退出全屏处理失败: $e');
      // 即使出错也要退出
      widget.onExit?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // 播放器内容
          if (!_isInitializing)
            Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.black,
              child: SuperLivePlayer(
                key: const ValueKey('fullscreen_player'), // 添加key确保独立实例
                liveUrl: widget.liveUrl,
                onError: () {
                  _logger.warning('全屏播放器发生错误');
                },
                onLoaded: () {
                  _logger.info('全屏播放器加载成功');
                },
                onFullscreenChanged: _handlePlayerFullscreenToggle,
              ),
            ),

          // 初始化过渡覆盖层
          if (_isInitializing)
            AnimatedOpacity(
              duration: const Duration(milliseconds: 300),
              opacity: _isInitializing ? 1.0 : 0.0,
              child: Container(
                color: Colors.black,
                width: double.infinity,
                height: double.infinity,
                child: const Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: 28,
                        height: 28,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 3,
                        ),
                      ),
                      SizedBox(height: 20),
                      Text(
                        '正在进入全屏...',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

          // 退出过渡覆盖层
          if (_isExiting)
            AnimatedOpacity(
              duration: const Duration(milliseconds: 200),
              opacity: _isExiting ? 1.0 : 0.0,
              child: Container(
                color: Colors.black,
                width: double.infinity,
                height: double.infinity,
                child: const Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      ),
                      SizedBox(height: 16),
                      Text(
                        '正在退出全屏...',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
