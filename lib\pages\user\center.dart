import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/user/user_info.dart';
import '../../widgets/bottom_navbar.dart';
import '../../theme.dart';
import '../../services/cache_service.dart';
import '../../services/user_service.dart';
import '../../services/log_service.dart';
import '../../services/update_service.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:package_info_plus/package_info_plus.dart';

class UserCenterPage extends StatefulWidget {
  const UserCenterPage({super.key});

  @override
  _UserCenterPageState createState() => _UserCenterPageState();
}

class _UserCenterPageState extends State<UserCenterPage> {
  final UserService _userService = UserService();
  bool _isLoading = false;
  double _balance = 0.0;
  bool _isClearing = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  // 初始化页面数据，包括检查用户状态和加载余额
  Future<void> _initializeData() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      // 检查用户登录状态
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      if (!authProvider.isInitialized) {
        await authProvider.initialize();
      }

      // 如果用户已登录，加载余额
      if (authProvider.isLoggedIn) {
        await _loadBalance();
      }
    } catch (e) {
      debugPrint('初始化用户中心数据失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadBalance() async {
    try {
      final data = await _userService.loadMembershipData();
      if (mounted) {
        setState(() {
          final paymentMethods = data['paymentMethods'] as List;
          final balancePayment = paymentMethods.firstWhere(
            (method) => method['id'] == 99, // 余额支付
            orElse: () => {'money': '0'},
          );
          _balance = double.tryParse(balancePayment['money'].toString()) ?? 0.0;
        });
      }
    } catch (e) {
      debugPrint('加载余额失败: $e');
      // Handle error silently
    }
  }

  Future<void> _refreshUserInfo() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final refreshSuccess = await authProvider.refreshUserInfo();
      if (refreshSuccess && authProvider.isLoggedIn) {
        await _loadBalance(); // 只在用户成功登录时加载余额
      }
    } catch (e) {
      debugPrint('刷新用户信息失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 显示APP版本信息对话框
  Future<void> _showAppVersionDialog() async {
    try {
      final PackageInfo packageInfo = await PackageInfo.fromPlatform();
      if (!mounted) return;

      // 获取当前年份
      final int currentYear = DateTime.now().year;

      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.green[50], // 设置淡绿色背景
            title: const Text('应用信息',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('应用名称: ${packageInfo.appName}'),
                const SizedBox(height: 8),
                Text('版本: ${packageInfo.version}'),
                const SizedBox(height: 8),
                Text('构建号: ${packageInfo.buildNumber}'),
                const SizedBox(height: 8),
                Text('包名: ${packageInfo.packageName}'),
                const SizedBox(height: 16),
                Text('联系邮箱：<EMAIL>'),
                const SizedBox(height: 16),
                Text('股市咖啡屋 $currentYear 版权所有'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () async {
                  // 检查更新
                  final updateService = UpdateService();
                  await updateService.checkForUpdates(context);
                },
                child: const Text('检查更新',
                    style: TextStyle(color: AppColors.primaryBlue)),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('关闭'),
              ),
            ],
          );
        },
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('获取版本信息失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final user = authProvider.user;

    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      body: RefreshIndicator(
        onRefresh: _refreshUserInfo,
        child: _isLoading
            ? const Center(
                child: SpinKitPulsingGrid(
                  size: 20,
                  color: Colors.grey,
                ),
              )
            : ListView(
                children: [
                  const SizedBox(height: 50),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: const UserInfoBox(),
                  ),
                  const SizedBox(height: 20),
                  _buildVipStatus(user),
                  if (user != null) ...[
                    const SizedBox(height: 20),
                    _buildBalance(user),
                    const SizedBox(height: 20),
                    _buildLiveCount(user),
                  ],
                  const SizedBox(height: 20),
                  _buildFunctionButtons(),
                ],
              ),
      ),
      bottomNavigationBar: const BottomNavBar(currentIndex: 4),
    );
  }

  Widget _buildVipStatus(user) {
    bool isVip = user?.vip?['level'] != null && (user.vip['level'] as int) > 0;
    String vipName = 'VIP会员';
    if (isVip) {
      if (user.vip['level'] == 8) {
        vipName = '普通会员';
      } else if (user.vip['level'] == 9) {
        vipName = '高级会员';
      }
    }
    String vipStatus = isVip
        ? '续期'
        : user != null
            ? '开通'
            : '请先登录';

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            vipName,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          Row(
            children: [
              ElevatedButton(
                onPressed: () {
                  // 根据登录状态跳转到不同页面
                  if (user != null) {
                    Navigator.pushNamed(context, '/vip');
                  } else {
                    Navigator.pushNamed(context, '/user/login');
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: Text(vipStatus,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    )),
              ),
              if (isVip && user.vip['level'] == 8) ...[
                const SizedBox(width: 10),
                ElevatedButton(
                  onPressed: () {
                    // 跳转到VIP页面
                    Navigator.pushNamed(context, '/vip');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: const Text('升级',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      )),
                ),
              ]
            ],
          )
        ],
      ),
    );
  }

  Widget _buildBalance(user) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '¥ ${_balance.toStringAsFixed(2)}',
                style:
                    const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 10),
              const Text(
                '可用余额(元)',
                style: TextStyle(color: Colors.grey, fontSize: 12),
              ),
            ],
          ),
          Row(
            children: [
              ElevatedButton(
                onPressed: () {
                  // 跳转到充值页面
                  Navigator.pushNamed(context, '/pay/recharge');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    side: const BorderSide(color: AppColors.primaryBlue),
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: const Text('立即充值',
                    style: TextStyle(
                      color: AppColors.primaryBlue,
                      fontSize: 12,
                    )),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () {
                  // 跳转到充值记录页面
                  Navigator.pushNamed(context, '/user/recharge_records');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: const Text('充值记录',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    )),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLiveCount(user) {
    int liveCount = 0;
    if (user?.assets?['live'] != null) {
      liveCount = int.tryParse(user.assets['live'].toString()) ?? 0;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '$liveCount 次',
                style:
                    const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 10),
              const Text(
                '直播可用次数',
                style: TextStyle(color: Colors.grey, fontSize: 12),
              ),
            ],
          ),
          ElevatedButton(
            onPressed: () {
              // 跳转到直播购买页面
              Navigator.pushNamed(context, '/live/purchase');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            child: const Text('购买次数',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                )),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionButtons() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.user;

    // 所有用户都可以看到的功能
    final List<Map<String, dynamic>> publicFunctionItems = [
      {
        'icon': Icons.cleaning_services,
        'label': '清除缓存',
        'route': 'clear_cache'
      },
      {'icon': Icons.info, 'label': '应用信息', 'route': 'app_info'},
    ];

    // 管理员专用功能按钮
    final List<Map<String, dynamic>> adminFunctionItems = [
      {
        'icon': Icons.notifications,
        'label': 'FCM测试',
        'route': '/debug/fcm_test'
      },
      {'icon': Icons.bug_report, 'label': '调试日志', 'route': '/debug/log_viewer'},
    ];

    // 只有登录用户才能看到的功能
    final List<Map<String, dynamic>> loggedInFunctionItems = [
      {'icon': Icons.edit, 'label': '修改资料', 'route': '/user/edit_profile'},
      {'icon': Icons.lock, 'label': '修改密码', 'route': '/user/change_password'},
      {
        'icon': Icons.shopping_cart,
        'label': '购买记录',
        'route': '/user/purchase_records'
      },
      {'icon': Icons.diamond, 'label': 'VIP记录', 'route': '/user/vip_records'},
      {'icon': Icons.live_tv, 'label': '直播记录', 'route': '/user/live_records'},
      {
        'icon': Icons.account_balance_wallet,
        'label': '充值记录',
        'route': '/user/recharge_records'
      },
    ];

    // 检查用户是否为管理员
    bool isAdmin = false;
    if (user != null && user.live['is_admin'] != null) {
      isAdmin = user.live['is_admin'] as bool? ?? false;
    }

    // 根据登录状态和管理员权限确定显示哪些功能按钮
    final List<Map<String, dynamic>> functionItems = user != null
        ? [
            ...loggedInFunctionItems,
            ...publicFunctionItems,
            if (isAdmin) ...adminFunctionItems
          ]
        : [...publicFunctionItems];

    return Container(
      color: Colors.white,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          childAspectRatio: 1,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
        ),
        itemCount: functionItems.length,
        itemBuilder: (context, index) {
          return InkWell(
            onTap: () {
              if (functionItems[index]['route'] == 'clear_cache') {
                // 显示清理动画
                setState(() {
                  _isClearing = true;
                });

                // 清除缓存
                CacheService().clearCache();

                // 延迟一下以显示动画效果
                Future.delayed(const Duration(milliseconds: 800), () {
                  if (mounted) {
                    setState(() {
                      _isClearing = false;
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('缓存已清除'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  }
                });
              } else if (functionItems[index]['route'] == 'app_info') {
                _showAppVersionDialog();
              } else {
                Navigator.pushNamed(context, functionItems[index]['route']);
              }
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (_isClearing &&
                    functionItems[index]['route'] == 'clear_cache')
                  const SizedBox(
                    width: 28,
                    height: 28,
                    child: SpinKitPulsingGrid(
                      color: AppColors.primaryBlue,
                    ),
                  )
                else
                  Icon(
                    functionItems[index]['icon'],
                    color: Colors.grey[600],
                    size: 28,
                  ),
                const SizedBox(height: 5),
                Text(
                  functionItems[index]['label'],
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
