PODS:
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_secure_storage (6.0.0):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - super_player (12.0.1):
    - Flutter
    - TXLiteAVSDK_Player (= 12.0.16301)
  - TXLiteAVSDK_Player (12.0.16301)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - super_player (from `.symlinks/plugins/super_player/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - TXLiteAVSDK_Player

EXTERNAL SOURCES:
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  super_player:
    :path: ".symlinks/plugins/super_player/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  device_info_plus: 97af1d7e84681a90d0693e63169a5d50e0839a0d
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_secure_storage: 23fc622d89d073675f2eaa109381aefbcf5a49be
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  super_player: edae5d459d78e129b5dcdf4824afd9293d7ae73c
  TXLiteAVSDK_Player: cd42ef1f44717360dad1913a07a43678d6466972
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: 819463e6a0290f5a72f145ba7cde16e8b6ef0796

COCOAPODS: 1.15.2
