package com.dexterous.flutterlocalnotifications;

import android.graphics.Bitmap;
import android.app.Notification.BigPictureStyle;

/**
 * 修复 BigPictureStyle.bigLargeIcon 方法的歧义问题
 * 在较新的 Android 版本中，BigPictureStyle 类同时提供了接受 Bitmap 和 Icon 参数的 bigLargeIcon 方法
 */
public class BigPictureStyleFix {
    /**
     * 明确使用 Bitmap 版本的 bigLargeIcon 方法
     * @param style BigPictureStyle 实例
     * @param bitmap Bitmap 实例，可以为 null
     */
    public static void setBigLargeIcon(BigPictureStyle style, Bitmap bitmap) {
        if (style != null) {
            style.bigLargeIcon(bitmap);
        }
    }
}