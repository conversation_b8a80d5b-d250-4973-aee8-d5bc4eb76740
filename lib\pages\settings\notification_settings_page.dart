import 'package:flutter/material.dart';
import '../../services/chat_notification_manager.dart';

/// 通知设置页面 - 允许用户自定义通知显示方式和免打扰时段
class NotificationSettingsPage extends StatefulWidget {
  const NotificationSettingsPage({Key? key}) : super(key: key);

  @override
  State<NotificationSettingsPage> createState() => _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends State<NotificationSettingsPage> {
  final ChatNotificationManager _notificationManager = ChatNotificationManager();
  
  // 通知设置
  bool _notificationsEnabled = true;
  bool _onlyMentionNotifications = false;
  bool _notificationSoundEnabled = true;
  bool _mergeNotifications = true;
  bool _showAvatarInNotification = true;
  int _notificationExpiryDays = 7;
  
  // 免打扰时段
  bool _doNotDisturbEnabled = false;
  TimeOfDay? _doNotDisturbStart;
  TimeOfDay? _doNotDisturbEnd;
  
  // 通知优先级
  final Map<String, NotificationPriority> _notificationPriorities = {
    'reply': NotificationPriority.high,
    'mention': NotificationPriority.high,
    'private': NotificationPriority.high,
    'admin': NotificationPriority.medium,
    'system': NotificationPriority.medium,
    'normal': NotificationPriority.low,
  };
  
  @override
  void initState() {
    super.initState();
    _loadSettings();
  }
  
  Future<void> _loadSettings() async {
    setState(() {
      _notificationsEnabled = _notificationManager.notificationsEnabled;
      _onlyMentionNotifications = _notificationManager.onlyMentionNotifications;
      _notificationSoundEnabled = _notificationManager.notificationSoundEnabled;
      _mergeNotifications = _notificationManager.mergeNotifications;
      _showAvatarInNotification = _notificationManager.showAvatarInNotification;
      _notificationExpiryDays = _notificationManager.notificationExpiryDays;
      _doNotDisturbEnabled = _notificationManager.doNotDisturbEnabled;
      _doNotDisturbStart = _notificationManager.doNotDisturbStart;
      _doNotDisturbEnd = _notificationManager.doNotDisturbEnd;
    });
  }
  
  Future<void> _selectDoNotDisturbStart() async {
    final initialTime = _doNotDisturbStart ?? const TimeOfDay(hour: 22, minute: 0);
    final selectedTime = await showTimePicker(
      context: context,
      initialTime: initialTime,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
          child: child!,
        );
      },
    );
    
    if (selectedTime != null) {
      setState(() {
        _doNotDisturbStart = selectedTime;
      });
      
      await _notificationManager.setDoNotDisturbPeriod(
        _doNotDisturbEnabled,
        selectedTime,
        _doNotDisturbEnd,
      );
    }
  }
  
  Future<void> _selectDoNotDisturbEnd() async {
    final initialTime = _doNotDisturbEnd ?? const TimeOfDay(hour: 7, minute: 0);
    final selectedTime = await showTimePicker(
      context: context,
      initialTime: initialTime,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
          child: child!,
        );
      },
    );
    
    if (selectedTime != null) {
      setState(() {
        _doNotDisturbEnd = selectedTime;
      });
      
      await _notificationManager.setDoNotDisturbPeriod(
        _doNotDisturbEnabled,
        _doNotDisturbStart,
        selectedTime,
      );
    }
  }
  
  String _formatTimeOfDay(TimeOfDay? time) {
    if (time == null) return '未设置';
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('通知设置'),
      ),
      body: ListView(
        children: [
          _buildSectionHeader('基本设置'),
          SwitchListTile(
            title: const Text('启用通知'),
            subtitle: const Text('接收新消息通知'),
            value: _notificationsEnabled,
            onChanged: (value) async {
              setState(() {
                _notificationsEnabled = value;
              });
              await _notificationManager.setNotificationsEnabled(value);
            },
          ),
          SwitchListTile(
            title: const Text('只接收与我相关的消息'),
            subtitle: const Text('仅显示@我或私聊消息的通知'),
            value: _onlyMentionNotifications,
            onChanged: _notificationsEnabled ? (value) async {
              setState(() {
                _onlyMentionNotifications = value;
              });
              await _notificationManager.setOnlyMentionNotifications(value);
            } : null,
          ),
          SwitchListTile(
            title: const Text('通知声音'),
            subtitle: const Text('收到通知时播放提示音'),
            value: _notificationSoundEnabled,
            onChanged: _notificationsEnabled ? (value) async {
              setState(() {
                _notificationSoundEnabled = value;
              });
              await _notificationManager.setNotificationSoundEnabled(value);
            } : null,
          ),
          const Divider(),
          
          _buildSectionHeader('通知显示'),
          SwitchListTile(
            title: const Text('合并通知'),
            subtitle: const Text('短时间内收到多条消息时合并显示'),
            value: _mergeNotifications,
            onChanged: _notificationsEnabled ? (value) async {
              setState(() {
                _mergeNotifications = value;
              });
              await _notificationManager.setMergeNotifications(value);
            } : null,
          ),
          SwitchListTile(
            title: const Text('显示头像'),
            subtitle: const Text('在通知中显示发送者头像'),
            value: _showAvatarInNotification,
            onChanged: _notificationsEnabled ? (value) async {
              setState(() {
                _showAvatarInNotification = value;
              });
              await _notificationManager.setShowAvatarInNotification(value);
            } : null,
          ),
          ListTile(
            title: const Text('通知保留时间'),
            subtitle: Text('保留最近 $_notificationExpiryDays 天的未读消息'),
            trailing: DropdownButton<int>(
              value: _notificationExpiryDays,
              onChanged: _notificationsEnabled ? (value) async {
                if (value != null) {
                  setState(() {
                    _notificationExpiryDays = value;
                  });
                  await _notificationManager.setNotificationExpiryDays(value);
                }
              } : null,
              items: const [
                DropdownMenuItem(value: 1, child: Text('1天')),
                DropdownMenuItem(value: 3, child: Text('3天')),
                DropdownMenuItem(value: 7, child: Text('7天')),
                DropdownMenuItem(value: 14, child: Text('14天')),
                DropdownMenuItem(value: 30, child: Text('30天')),
              ],
            ),
          ),
          const Divider(),
          
          _buildSectionHeader('免打扰时段'),
          SwitchListTile(
            title: const Text('启用免打扰'),
            subtitle: Text(
              _doNotDisturbEnabled
                  ? '免打扰时间: ${_formatTimeOfDay(_doNotDisturbStart)} - ${_formatTimeOfDay(_doNotDisturbEnd)}'
                  : '在指定时间段内不接收普通消息通知',
            ),
            value: _doNotDisturbEnabled,
            onChanged: _notificationsEnabled ? (value) async {
              setState(() {
                _doNotDisturbEnabled = value;
              });
              
              // 如果启用免打扰但未设置时间，设置默认值
              if (value && (_doNotDisturbStart == null || _doNotDisturbEnd == null)) {
                setState(() {
                  _doNotDisturbStart ??= const TimeOfDay(hour: 22, minute: 0);
                  _doNotDisturbEnd ??= const TimeOfDay(hour: 7, minute: 0);
                });
              }
              
              await _notificationManager.setDoNotDisturbPeriod(
                value,
                _doNotDisturbStart,
                _doNotDisturbEnd,
              );
            } : null,
          ),
          if (_doNotDisturbEnabled)
            ListTile(
              title: const Text('开始时间'),
              trailing: TextButton(
                onPressed: _notificationsEnabled ? _selectDoNotDisturbStart : null,
                child: Text(_formatTimeOfDay(_doNotDisturbStart)),
              ),
            ),
          if (_doNotDisturbEnabled)
            ListTile(
              title: const Text('结束时间'),
              trailing: TextButton(
                onPressed: _notificationsEnabled ? _selectDoNotDisturbEnd : null,
                child: Text(_formatTimeOfDay(_doNotDisturbEnd)),
              ),
            ),
          const Divider(),
          
          _buildSectionHeader('通知优先级'),
          _buildPriorityListTile(
            '回复我的消息',
            'reply',
            Icons.reply,
            Colors.blue,
          ),
          _buildPriorityListTile(
            '@我的消息',
            'mention',
            Icons.alternate_email,
            Colors.orange,
          ),
          _buildPriorityListTile(
            '私聊消息',
            'private',
            Icons.mail,
            Colors.purple,
          ),
          _buildPriorityListTile(
            '管理员消息',
            'admin',
            Icons.verified_user,
            Colors.green,
          ),
          _buildPriorityListTile(
            '系统消息',
            'system',
            Icons.announcement,
            Colors.blue,
          ),
          _buildPriorityListTile(
            '普通消息',
            'normal',
            Icons.chat,
            Colors.grey,
          ),
        ],
      ),
    );
  }
  
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }
  
  Widget _buildPriorityListTile(
    String title,
    String key,
    IconData icon,
    Color color,
  ) {
    return ListTile(
      leading: Icon(icon, color: color),
      title: Text(title),
      trailing: DropdownButton<NotificationPriority>(
        value: _notificationPriorities[key] ?? NotificationPriority.medium,
        onChanged: _notificationsEnabled ? (value) async {
          if (value != null) {
            setState(() {
              _notificationPriorities[key] = value;
            });
            await _notificationManager.setNotificationPriority(key, value);
          }
        } : null,
        items: const [
          DropdownMenuItem(
            value: NotificationPriority.high,
            child: Text('高优先级'),
          ),
          DropdownMenuItem(
            value: NotificationPriority.medium,
            child: Text('中优先级'),
          ),
          DropdownMenuItem(
            value: NotificationPriority.low,
            child: Text('低优先级'),
          ),
        ],
      ),
    );
  }
}
