import 'package:flutter/material.dart';
import '../../services/records_service.dart';
import '../../widgets/recharge_record_list.dart';
import '../../theme.dart';

class RechargeRecordsPage extends StatefulWidget {
  const RechargeRecordsPage({Key? key}) : super(key: key);

  @override
  _RechargeRecordsPageState createState() => _RechargeRecordsPageState();
}

class _RechargeRecordsPageState extends State<RechargeRecordsPage> {
  final RecordsService _recordsService = RecordsService();
  final List<RechargeRecordItem> _records = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 1;
  final int _pageSize = 10;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadRecords();
  }

  Future<void> _loadRecords({bool refresh = false}) async {
    if (_isLoading) return;

    if (refresh) {
      setState(() {
        _records.clear();
        _currentPage = 1;
        _hasMore = true;
        _errorMessage = '';
      });
    }

    if (!_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final data = await _recordsService.getRechargeRecords(
        page: _currentPage,
        limit: _pageSize,
        context: context,
      );

      final List<dynamic> newRecords = data['records'] ?? [];
      final int total = data['total'] ?? 0;

      setState(() {
        for (var record in newRecords) {
          // 根据PC端展示方式处理充值记录
          // 处理支付方式，根据ice_note字段
          String paymentType = '其他';
          if (record['ice_note'] != null) {
            final String iceNote = record['ice_note'].toString();
            if (iceNote.contains('支付宝')) {
              paymentType = '支付宝';
            } else if (iceNote.contains('微信')) {
              paymentType = '微信支付';
            } else if (iceNote.contains('银行卡')) {
              paymentType = '银行卡';
            } else if (iceNote.isNotEmpty) {
              paymentType = iceNote;
            }
          }

          _records.add(RechargeRecordItem(
            money: record['money'] != null
                ? (record['money'] is num
                    ? record['money'].toDouble()
                    : double.tryParse(record['money'].toString()) ?? 0.0)
                : 0.0,
            paymentType: paymentType,
            time: record['time'] ?? '',
          ));
        }

        _hasMore = _records.length < total;
        _currentPage++;
        _errorMessage = '';
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
    return;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('充值记录'),
        backgroundColor: Colors.white,
        foregroundColor: AppColors.textEmphasis,
        elevation: 0.5,
      ),
      body: Column(
        children: [
          if (_errorMessage.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(8.0),
              color: Colors.red[50],
              width: double.infinity,
              child: Text(
                _errorMessage,
                style: const TextStyle(color: Colors.red),
                textAlign: TextAlign.center,
              ),
            ),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () => _loadRecords(refresh: true),
              child: RechargeRecordList(
                records: _records,
                isLoading: _isLoading && _records.isEmpty,
                hasMore: _hasMore && !_isLoading,
                onLoadMore: _loadRecords,
                emptyText: '暂无充值记录',
              ),
            ),
          ),
        ],
      ),
    );
  }
}
