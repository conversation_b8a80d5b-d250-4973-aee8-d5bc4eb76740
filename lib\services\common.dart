import 'package:estockcafe/config/config.dart';
import 'package:estockcafe/utils/logging.dart';
import 'dart:convert';

import 'package:logging/logging.dart';
import 'package:estockcafe/utils/functions.dart';

class CommonService {
  final String baseUrl = AppConfig.baseUrl;
  final Logger _logger = Logging.getLogger('CommonService');

  Future<Map<String, dynamic>> fetchMenuItems() async {
    try {
      final response = await httpRequest('$baseUrl/side_menu');

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        _logger.severe(
            'Failed to load menu items: ${response.statusCode} ${response.body}');
        throw HttpException('Failed to load menu items', response.statusCode);
      }
    } catch (e) {
      _logger.severe('Error fetching menu items: $e');
      rethrow;
    }
  }
}

class HttpException implements Exception {
  final String message;
  final int statusCode;

  HttpException(this.message, this.statusCode);

  @override
  String toString() => 'HttpException: $message (Status code: $statusCode)';
}
