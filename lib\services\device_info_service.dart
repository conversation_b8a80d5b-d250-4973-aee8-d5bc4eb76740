import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';

class DeviceInfoService {
  static final DeviceInfoService _instance = DeviceInfoService._internal();
  factory DeviceInfoService() => _instance;
  DeviceInfoService._internal();

  final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();
  String? _userAgent;

  Future<String> getUserAgent() async {
    if (_userAgent != null) {
      return _userAgent!;
    }

    if (Platform.isAndroid) {
      final androidInfo = await _deviceInfoPlugin.androidInfo;
      _userAgent =
          'EstockCafe/ Android/${androidInfo.version.release} ${androidInfo.model}';
    } else if (Platform.isIOS) {
      final iosInfo = await _deviceInfoPlugin.iosInfo;
      _userAgent = 'EstockCafe/ iOS/${iosInfo.systemVersion} ${iosInfo.model}';
    } else {
      _userAgent = 'EstockCafe/ Unknown Platform';
    }

    return _userAgent!;
  }
}
